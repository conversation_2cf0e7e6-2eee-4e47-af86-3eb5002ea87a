-- =====================================================
-- 考公刷题记录系统 - 索引优化脚本
-- =====================================================
-- 版本: v1.0
-- 数据库: MySQL 8.0+
-- 用途: 性能优化索引，在基础建表完成后执行
-- 创建时间: 2024
-- =====================================================

USE kaogong_db;

-- =====================================================
-- 复合索引优化
-- =====================================================

-- 用户表复合索引
-- 用于用户搜索和筛选场景
ALTER TABLE `users` ADD INDEX `idx_reputation_role` (`reputation_level`, `system_role`);
ALTER TABLE `users` ADD INDEX `idx_active_created` (`is_active`, `created_at`);

-- 刷题记录表复合索引
-- 用于学习统计和数据分析
ALTER TABLE `study_records` ADD INDEX `idx_user_module_date` (`user_id`, `module_type`, `study_date`);
ALTER TABLE `study_records` ADD INDEX `idx_date_accuracy` (`study_date`, `accuracy_rate`);

-- 错题本表复合索引
-- 用于错题复习和掌握度统计
ALTER TABLE `wrong_questions` ADD INDEX `idx_user_mastery` (`user_id`, `mastery_status`);
ALTER TABLE `wrong_questions` ADD INDEX `idx_type_difficulty` (`question_type`, `difficulty_level`);

-- 小桌成员表复合索引
-- 用于小桌管理和活跃度统计
ALTER TABLE `desk_members` ADD INDEX `idx_desk_status_active` (`desk_id`, `status`, `last_active_at`);
ALTER TABLE `desk_members` ADD INDEX `idx_user_role` (`user_id`, `role`);

-- 小桌申请表复合索引
-- 用于申请管理和审核
ALTER TABLE `desk_applications` ADD INDEX `idx_desk_status_applied` (`desk_id`, `status`, `applied_at`);

-- 排行榜表复合索引
-- 用于排行榜查询和统计
ALTER TABLE `rankings` ADD INDEX `idx_type_desk_rank` (`ranking_type`, `desk_id`, `rank_position`);
ALTER TABLE `rankings` ADD INDEX `idx_user_type_calculated` (`user_id`, `ranking_type`, `calculated_at`);

-- 公告表复合索引
-- 用于公告搜索和筛选
ALTER TABLE `announcements` ADD INDEX `idx_region_type_status` (`region`, `exam_type`, `status`);
ALTER TABLE `announcements` ADD INDEX `idx_status_published` (`status`, `published_at`);

-- 通知表复合索引
-- 用于通知查询和管理
ALTER TABLE `notifications` ADD INDEX `idx_user_read_created` (`user_id`, `is_read`, `created_at`);
ALTER TABLE `notifications` ADD INDEX `idx_type_created` (`type`, `created_at`);

-- =====================================================
-- 分区表优化（可选）
-- =====================================================

-- 刷题记录表按月分区（适用于大数据量场景）
-- 注意：需要在建表时就设计分区，这里仅作为参考
/*
ALTER TABLE `study_records` 
PARTITION BY RANGE (YEAR(study_date) * 100 + MONTH(study_date)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- =====================================================
-- 性能监控查询
-- =====================================================

-- 查看表大小和索引使用情况
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'DB Size in MB',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'kaogong_db'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 查看索引使用统计
SELECT 
    OBJECT_SCHEMA,
    OBJECT_NAME,
    INDEX_NAME,
    COUNT_FETCH,
    COUNT_INSERT,
    COUNT_UPDATE,
    COUNT_DELETE
FROM performance_schema.table_io_waits_summary_by_index_usage 
WHERE OBJECT_SCHEMA = 'kaogong_db'
ORDER BY COUNT_FETCH DESC;

-- =====================================================
-- 索引维护建议
-- =====================================================

-- 定期分析表统计信息（建议每周执行）
ANALYZE TABLE users, invite_codes, study_records, wrong_questions, study_plans,
              desks, desk_members, desk_applications, reputation_logs, rankings,
              announcements, notifications;

-- 检查索引碎片（建议每月执行）
-- 如果碎片率超过30%，建议重建索引
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    ROUND(STAT_VALUE * 100 / (SELECT STAT_VALUE 
                              FROM INFORMATION_SCHEMA.INNODB_SYS_TABLESTATS 
                              WHERE NAME = CONCAT(TABLE_SCHEMA, '/', TABLE_NAME)), 2) AS fragmentation_pct
FROM INFORMATION_SCHEMA.INNODB_SYS_INDEXES 
WHERE TABLE_SCHEMA = 'kaogong_db';

-- =====================================================
-- 索引优化脚本执行完成
-- =====================================================
