package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件访问控制器
 * 提供静态文件访问服务
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@RestController
@RequestMapping("/files")
@RequiredArgsConstructor
@Tag(name = "文件访问", description = "静态文件访问接口")
public class FileAccessController {

    private final FileUploadProperties fileUploadProperties;

    /**
     * 访问上传的文件
     */
    @GetMapping("/**")
    @Operation(summary = "访问文件", description = "根据文件路径访问上传的静态文件")
    public ResponseEntity<Resource> getFile(
            @Parameter(description = "文件路径") 
            HttpServletRequest request) {
        
        // 获取文件路径（去掉 /files 前缀）
        String requestPath = request.getRequestURI();
        String filePath = requestPath.substring("/api/files".length());
        
        // 如果路径以 / 开头，去掉它
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        
        log.debug("访问文件: requestPath={}, filePath={}", requestPath, filePath);
        
        try {
            // 构建完整的文件路径
            Path file = Paths.get(fileUploadProperties.getPath()).resolve(filePath).normalize();
            Resource resource = new UrlResource(file.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                // 确定文件的MIME类型
                String contentType = null;
                try {
                    contentType = Files.probeContentType(file);
                } catch (IOException ex) {
                    log.debug("无法确定文件类型: {}", file, ex);
                }
                
                // 如果无法确定MIME类型，使用默认值
                if (contentType == null) {
                    contentType = "application/octet-stream";
                }
                
                log.debug("返回文件: path={}, contentType={}, size={}", 
                         file, contentType, resource.contentLength());
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, 
                               "inline; filename=\"" + resource.getFilename() + "\"")
                        .body(resource);
            } else {
                log.warn("文件不存在或不可读: {}", file);
                return ResponseEntity.notFound().build();
            }
        } catch (MalformedURLException ex) {
            log.error("文件路径格式错误: {}", filePath, ex);
            return ResponseEntity.badRequest().build();
        } catch (Exception ex) {
            log.error("访问文件时发生错误: {}", filePath, ex);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 检查文件是否存在
     */
    @RequestMapping(value = "/**", method = RequestMethod.HEAD)
    @Operation(summary = "检查文件", description = "检查文件是否存在")
    public ResponseEntity<Void> checkFile(HttpServletRequest request) {
        // 获取文件路径
        String requestPath = request.getRequestURI();
        String filePath = requestPath.substring("/api/files".length());
        
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        
        try {
            Path file = Paths.get(fileUploadProperties.getPath()).resolve(filePath).normalize();
            Resource resource = new UrlResource(file.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception ex) {
            log.error("检查文件时发生错误: {}", filePath, ex);
            return ResponseEntity.internalServerError().build();
        }
    }
}
