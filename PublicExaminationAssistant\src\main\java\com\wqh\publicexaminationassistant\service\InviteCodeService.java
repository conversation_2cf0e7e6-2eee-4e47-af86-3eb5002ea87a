package com.wqh.publicexaminationassistant.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.ValidateInviteCodeRequest;
import com.wqh.publicexaminationassistant.dto.response.ValidateInviteCodeResponse;
import com.wqh.publicexaminationassistant.entity.InviteCode;
import com.wqh.publicexaminationassistant.mapper.InviteCodeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 邀请码服务类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InviteCodeService {

    private final InviteCodeMapper inviteCodeMapper;
    private static final String CODE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CODE_LENGTH = 8;

    /**
     * 生成邀请码
     * @param createdBy 创建者ID，null表示系统生成
     * @param count 生成数量
     * @param expiresAt 过期时间，null则默认30天后过期
     * @return 生成的邀请码列表
     */
    public List<String> generateInviteCodes(String createdBy, int count, LocalDateTime expiresAt) {
        List<String> codes = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            String code = generateUniqueCode();

            InviteCode inviteCode = new InviteCode();
            inviteCode.setCode(code);
            inviteCode.setCreatedBy(createdBy); // 允许为null，表示系统生成
            inviteCode.setExpiresAt(expiresAt != null ? expiresAt : LocalDateTime.now().plusDays(30));
            inviteCode.setIsActive(true);

            inviteCodeMapper.insert(inviteCode);
            codes.add(code);
        }

        String creatorInfo = createdBy != null ? createdBy : "SYSTEM";
        log.info("生成邀请码成功: count={}, createdBy={}", count, creatorInfo);
        return codes;
    }

    /**
     * 系统生成邀请码（便捷方法）
     * @param count 生成数量
     * @return 生成的邀请码列表
     */
    public List<String> generateSystemInviteCodes(int count) {
        return generateInviteCodes(null, count, LocalDateTime.now().plusDays(30));
    }

    /**
     * 用户生成邀请码
     * @param userId 用户ID
     * @param count 生成数量
     * @param expiresAt 过期时间
     * @return 生成的邀请码列表
     */
    public List<String> generateUserInviteCodes(String userId, int count, LocalDateTime expiresAt) {
        if (userId == null || userId.trim().isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "用户ID不能为空");
        }
        return generateInviteCodes(userId, count, expiresAt);
    }

    /**
     * 验证邀请码
     */
    public ValidateInviteCodeResponse validateInviteCode(ValidateInviteCodeRequest request) {
        InviteCode inviteCode = inviteCodeMapper.selectOne(
                new LambdaQueryWrapper<InviteCode>()
                        .eq(InviteCode::getCode, request.getCode())
                        .eq(InviteCode::getIsActive, true)
        );

        boolean valid = false;
        LocalDateTime expiresAt = null;

        if (inviteCode != null) {
            if (inviteCode.getUsedBy() == null && inviteCode.getExpiresAt().isAfter(LocalDateTime.now())) {
                valid = true;
                expiresAt = inviteCode.getExpiresAt();
            }
        }

        return ValidateInviteCodeResponse.builder()
                .valid(valid)
                .expiresAt(expiresAt)
                .build();
    }

    /**
     * 生成唯一的邀请码
     */
    private String generateUniqueCode() {
        String code;
        int attempts = 0;
        int maxAttempts = 100;
        
        do {
            code = generateRandomCode();
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成邀请码失败，请重试");
            }
        } while (codeExists(code));
        
        return code;
    }

    /**
     * 生成随机邀请码
     */
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            code.append(CODE_CHARS.charAt(random.nextInt(CODE_CHARS.length())));
        }
        
        return code.toString();
    }

    /**
     * 检查邀请码是否已存在
     */
    private boolean codeExists(String code) {
        return inviteCodeMapper.selectCount(
                new LambdaQueryWrapper<InviteCode>().eq(InviteCode::getCode, code)
        ) > 0;
    }
}
