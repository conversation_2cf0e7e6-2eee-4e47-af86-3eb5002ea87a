-- 清理重复的排行榜数据
-- 只保留每个用户在每种排行榜类型中的最新记录

-- 1. 查看重复数据统计
SELECT 
    ranking_type,
    user_id,
    COUNT(*) as record_count,
    MIN(calculated_at) as first_time,
    MAX(calculated_at) as last_time
FROM rankings 
WHERE desk_id IS NULL
GROUP BY ranking_type, user_id
HAVING COUNT(*) > 1
ORDER BY record_count DESC;

-- 2. 删除重复数据，只保留最新的记录
DELETE r1 FROM rankings r1
INNER JOIN rankings r2 
WHERE r1.desk_id IS NULL 
AND r2.desk_id IS NULL
AND r1.ranking_type = r2.ranking_type
AND r1.user_id = r2.user_id
AND r1.calculated_at < r2.calculated_at;

-- 3. 验证清理结果
SELECT 
    ranking_type,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as unique_users
FROM rankings 
WHERE desk_id IS NULL
GROUP BY ranking_type
ORDER BY ranking_type;

-- 4. 查看清理后的数据
SELECT 
    ranking_type,
    user_id,
    score,
    rank_position,
    calculated_at
FROM rankings 
WHERE desk_id IS NULL
ORDER BY ranking_type, rank_position
LIMIT 20;
