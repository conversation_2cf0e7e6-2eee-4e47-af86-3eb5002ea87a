import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from '@douyinfe/semi-ui';
import zh_CN from '@douyinfe/semi-ui/lib/es/locale/source/zh_CN';

// 导入页面组件
import Login from './pages/Login';
import Register from './pages/Register';
import ApiTest from './pages/ApiTest';
import { ProtectedRoute } from './components/ProtectedRoute';

// 导入样式
import './styles/theme.css';
import './styles/reputation.css';

// 导入仪表板组件
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import ComingSoon from './pages/ComingSoon';
import CreateStudyRecord from './pages/CreateStudyRecord';
import StudyRecordsList from './pages/StudyRecordsList';
import StudyRecordDetail from './pages/StudyRecordDetail';
import EditStudyRecord from './pages/EditStudyRecord';
import WrongQuestions from './pages/WrongQuestions';
import AvatarUploadTest from './pages/AvatarUploadTest';

// 导入小桌系统组件
import DeskList from './pages/DeskList';
import DeskDetail from './pages/DeskDetail';
import MyDesks from './pages/MyDesks';
import DeskDashboard from './pages/DeskDashboard';
import DeskRanking from './pages/DeskRanking';

// 导入信誉系统组件
import ReputationCenter from './pages/ReputationCenter';
import ReputationCenterSimple from './pages/ReputationCenterSimple';
import ReputationLogs from './pages/ReputationLogs';
import ReputationTest from './pages/ReputationTest';

// 导入排行榜系统组件
import GlobalRanking from './pages/GlobalRanking';

function App() {
  return (
    <ConfigProvider locale={zh_CN}>
      <Router>
        <div className="App">
          <Routes>
            {/* 默认路由重定向 */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />

            {/* 认证相关路由 */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* 开发测试路由 */}
            <Route path="/api-test" element={<ApiTest />} />
            <Route path="/avatar-test" element={<AvatarUploadTest />} />
            <Route path="/reputation-test" element={<ReputationTest />} />

            {/* 受保护的路由 */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            {/* 刷题记录相关路由 */}
            <Route
              path="/study-records"
              element={
                <ProtectedRoute>
                  <StudyRecordsList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/study-records/create"
              element={
                <ProtectedRoute>
                  <CreateStudyRecord />
                </ProtectedRoute>
              }
            />
            <Route
              path="/study-records/:id"
              element={
                <ProtectedRoute>
                  <StudyRecordDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/study-records/:id/edit"
              element={
                <ProtectedRoute>
                  <EditStudyRecord />
                </ProtectedRoute>
              }
            />

            {/* 错题管理路由 */}
            <Route
              path="/wrong-questions"
              element={
                <ProtectedRoute>
                  <WrongQuestions />
                </ProtectedRoute>
              }
            />
            {/* 小桌系统路由 */}
            <Route
              path="/desks"
              element={
                <ProtectedRoute>
                  <DeskList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/desks/:deskId"
              element={
                <ProtectedRoute>
                  <DeskDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="/desks/:deskId/dashboard"
              element={
                <ProtectedRoute>
                  <DeskDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/desks/:deskId/ranking"
              element={
                <ProtectedRoute>
                  <DeskRanking />
                </ProtectedRoute>
              }
            />
            <Route
              path="/my-desks"
              element={
                <ProtectedRoute>
                  <MyDesks />
                </ProtectedRoute>
              }
            />

            {/* 信誉系统路由 */}
            <Route
              path="/reputation"
              element={
                <ProtectedRoute>
                  <ReputationCenter />
                </ProtectedRoute>
              }
            />
            <Route
              path="/reputation/logs"
              element={
                <ProtectedRoute>
                  <ReputationLogs />
                </ProtectedRoute>
              }
            />

            {/* 排行榜系统路由 */}
            <Route
              path="/rankings"
              element={
                <ProtectedRoute>
                  <GlobalRanking />
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              }
            />

            {/* 404 页面 */}
            <Route
              path="*"
              element={
                <div style={{
                  textAlign: 'center',
                  padding: '100px',
                  fontFamily: 'var(--font-handwritten)'
                }}>
                  <h1 style={{ fontSize: '48px' }}>🤔</h1>
                  <h2>页面未找到</h2>
                  <p>您访问的页面不存在</p>
                </div>
              }
            />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App;
