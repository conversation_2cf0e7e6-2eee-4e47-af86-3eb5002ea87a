import React from 'react';
import { Card, Space, Typography, Button, Alert } from '@douyinfe/semi-ui';

const { Title, Text } = Typography;

// 尝试导入所有需要的图标
let IconEyeOpened, IconDelete, IconUpload, IconDownload, IconArrowLeft, IconArrowRight, IconPlus, IconRefresh, IconFilter;
let iconErrors: string[] = [];

try {
  const icons = require('@douyinfe/semi-icons');
  IconEyeOpened = icons.IconEyeOpened;
  IconDelete = icons.IconDelete;
  IconUpload = icons.IconUpload;
  IconDownload = icons.IconDownload;
  IconArrowLeft = icons.IconArrowLeft;
  IconArrowRight = icons.IconArrowRight;
  IconPlus = icons.IconPlus;
  IconRefresh = icons.IconRefresh;
  IconFilter = icons.IconFilter;

  // 检查每个图标是否存在
  const iconChecks = [
    { name: 'IconEyeOpened', component: IconEyeOpened },
    { name: 'IconDelete', component: IconDelete },
    { name: 'IconUpload', component: IconUpload },
    { name: 'IconDownload', component: IconDownload },
    { name: 'IconArrowLeft', component: IconArrowLeft },
    { name: 'IconArrowRight', component: IconArrowRight },
    { name: 'IconPlus', component: IconPlus },
    { name: 'IconRefresh', component: IconRefresh },
    { name: 'IconFilter', component: IconFilter }
  ];

  iconChecks.forEach(({ name, component }) => {
    if (!component) {
      iconErrors.push(name);
    }
  });

} catch (error) {
  iconErrors.push('Failed to import @douyinfe/semi-icons');
}

const IconValidation: React.FC = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>Semi Design 图标验证</Title>
        <Text type="secondary">
          验证项目中使用的所有图标是否正确导入
        </Text>
        
        {iconErrors.length > 0 && (
          <Alert
            type="error"
            message="图标导入错误"
            description={
              <div>
                <p>以下图标无法正确导入：</p>
                <ul>
                  {iconErrors.map(error => (
                    <li key={error}>{error}</li>
                  ))}
                </ul>
              </div>
            }
            style={{ marginTop: '16px' }}
          />
        )}

        {iconErrors.length === 0 && (
          <Alert
            type="success"
            message="所有图标导入成功"
            description="项目中使用的所有图标都可以正确导入和使用"
            style={{ marginTop: '16px' }}
          />
        )}

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>图标测试</Title>
          <Space wrap>
            {IconEyeOpened && <Button icon={<IconEyeOpened />}>查看详情</Button>}
            {IconDelete && <Button type="danger" icon={<IconDelete />}>删除</Button>}
            {IconUpload && <Button icon={<IconUpload />}>上传</Button>}
            {IconDownload && <Button icon={<IconDownload />}>下载</Button>}
            {IconPlus && <Button icon={<IconPlus />}>添加</Button>}
            {IconRefresh && <Button icon={<IconRefresh />}>刷新</Button>}
            {IconFilter && <Button icon={<IconFilter />}>筛选</Button>}
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>导航图标测试</Title>
          <Space>
            {IconArrowLeft && <Button icon={<IconArrowLeft />}>上一页</Button>}
            {IconArrowRight && <Button icon={<IconArrowRight />}>下一页</Button>}
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>可用图标列表</Title>
          <div style={{ 
            padding: '16px', 
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '6px',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            <div>✅ IconEyeOpened - 查看/详情图标</div>
            <div>✅ IconDelete - 删除图标</div>
            <div>✅ IconUpload - 上传图标</div>
            <div>✅ IconDownload - 下载图标</div>
            <div>✅ IconArrowLeft - 左箭头</div>
            <div>✅ IconArrowRight - 右箭头</div>
            <div>✅ IconPlus - 添加图标</div>
            <div>✅ IconRefresh - 刷新图标</div>
            <div>✅ IconFilter - 筛选图标</div>
          </div>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>使用建议</Title>
          <div style={{ 
            padding: '16px', 
            backgroundColor: 'var(--semi-color-warning-light-default)',
            borderRadius: '6px'
          }}>
            <Text>
              <strong>推荐的图标导入方式：</strong>
            </Text>
            <pre style={{ 
              marginTop: '8px', 
              padding: '8px', 
              backgroundColor: 'var(--semi-color-fill-1)',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
{`import { 
  IconEyeOpened, 
  IconDelete, 
  IconUpload, 
  IconDownload 
} from '@douyinfe/semi-icons';`}
            </pre>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IconValidation;
