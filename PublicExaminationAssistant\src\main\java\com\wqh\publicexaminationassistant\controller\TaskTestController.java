package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.entity.ScheduledTaskLog;
import com.wqh.publicexaminationassistant.mapper.ScheduledTaskLogMapper;
import com.wqh.publicexaminationassistant.service.DailyStudyCheckTask;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时任务测试控制器
 * 用于测试和监控定时任务的执行
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/api/v1/task-test")
@Tag(name = "定时任务测试", description = "定时任务测试和监控相关接口")
@Slf4j
public class TaskTestController {

    @Autowired
    private DailyStudyCheckTask dailyStudyCheckTask;
    
    @Autowired
    private ScheduledTaskLogMapper scheduledTaskLogMapper;

    /**
     * 手动执行每日学习检查任务
     */
    @PostMapping("/daily-study-check")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "手动执行每日学习检查", description = "手动触发每日学习检查任务，用于测试")
    public ApiResponse<String> manualDailyStudyCheck(
            @Parameter(description = "检查日期，格式：yyyy-MM-dd") 
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate checkDate) {
        try {
            LocalDate targetDate = checkDate != null ? checkDate : LocalDate.now().minusDays(1);
            
            log.info("手动执行每日学习检查任务，检查日期: {}", targetDate);
            dailyStudyCheckTask.manualCheckDailyStudy(targetDate);
            
            return ApiResponse.success(null, 
                String.format("手动执行每日学习检查任务成功，检查日期: %s", targetDate));
            
        } catch (Exception e) {
            log.error("手动执行每日学习检查任务失败", e);
            return ApiResponse.error("执行任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取定时任务执行历史
     */
    @GetMapping("/task-history")
    @Operation(summary = "获取任务执行历史", description = "获取指定任务的执行历史记录")
    public ApiResponse<List<ScheduledTaskLog>> getTaskHistory(
            @Parameter(description = "任务名称") @RequestParam(required = false) String taskName,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @Parameter(description = "记录数量限制") @RequestParam(defaultValue = "20") int limit) {
        try {
            String finalTaskName = taskName != null ? taskName : "daily_study_check";
            LocalDate finalStartDate = startDate != null ? startDate : LocalDate.now().minusDays(7);
            LocalDate finalEndDate = endDate != null ? endDate : LocalDate.now();
            
            List<ScheduledTaskLog> taskHistory = scheduledTaskLogMapper.selectTaskHistory(
                finalTaskName, finalStartDate, finalEndDate, limit);
            
            return ApiResponse.success(taskHistory, "获取任务执行历史成功");
            
        } catch (Exception e) {
            log.error("获取任务执行历史失败", e);
            return ApiResponse.error("获取任务执行历史失败");
        }
    }

    /**
     * 获取任务执行统计
     */
    @GetMapping("/task-statistics")
    @Operation(summary = "获取任务执行统计", description = "获取最近一段时间的任务执行统计信息")
    public ApiResponse<List<Map<String, Object>>> getTaskStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> statistics = dailyStudyCheckTask.getTaskStatistics(days);
            
            return ApiResponse.success(statistics, "获取任务执行统计成功");
            
        } catch (Exception e) {
            log.error("获取任务执行统计失败", e);
            return ApiResponse.error("获取任务执行统计失败");
        }
    }

    /**
     * 获取正在运行的任务
     */
    @GetMapping("/running-tasks")
    @Operation(summary = "获取正在运行的任务", description = "获取当前正在运行的定时任务列表")
    public ApiResponse<List<ScheduledTaskLog>> getRunningTasks() {
        try {
            List<ScheduledTaskLog> runningTasks = scheduledTaskLogMapper.selectRunningTasks();
            
            return ApiResponse.success(runningTasks, "获取正在运行的任务成功");
            
        } catch (Exception e) {
            log.error("获取正在运行的任务失败", e);
            return ApiResponse.error("获取正在运行的任务失败");
        }
    }

    /**
     * 获取失败的任务
     */
    @GetMapping("/failed-tasks")
    @Operation(summary = "获取失败的任务", description = "获取最近失败的定时任务记录")
    public ApiResponse<List<ScheduledTaskLog>> getFailedTasks(
            @Parameter(description = "开始日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate) {
        try {
            LocalDate finalStartDate = startDate != null ? startDate : LocalDate.now().minusDays(7);
            List<ScheduledTaskLog> failedTasks = scheduledTaskLogMapper.selectFailedTasks(finalStartDate);
            
            return ApiResponse.success(failedTasks, "获取失败的任务成功");
            
        } catch (Exception e) {
            log.error("获取失败的任务失败", e);
            return ApiResponse.error("获取失败的任务失败");
        }
    }

    /**
     * 获取每日任务概览
     */
    @GetMapping("/daily-overview")
    @Operation(summary = "获取每日任务概览", description = "获取最近几天的任务执行概览")
    public ApiResponse<List<Map<String, Object>>> getDailyTaskOverview(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "7") int days) {
        try {
            LocalDate startDate = LocalDate.now().minusDays(days);
            List<Map<String, Object>> overview = scheduledTaskLogMapper.selectDailyTaskOverview(startDate);
            
            return ApiResponse.success(overview, "获取每日任务概览成功");
            
        } catch (Exception e) {
            log.error("获取每日任务概览失败", e);
            return ApiResponse.error("获取每日任务概览失败");
        }
    }

    /**
     * 检查任务是否今日已执行
     */
    @GetMapping("/check-task-status")
    @Operation(summary = "检查任务执行状态", description = "检查指定任务在指定日期是否已成功执行")
    public ApiResponse<Map<String, Object>> checkTaskStatus(
            @Parameter(description = "任务名称") @RequestParam(defaultValue = "daily_study_check") String taskName,
            @Parameter(description = "检查日期") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate checkDate) {
        try {
            LocalDate targetDate = checkDate != null ? checkDate : LocalDate.now().minusDays(1);
            
            boolean isCompleted = scheduledTaskLogMapper.isTaskCompletedToday(taskName, targetDate);
            ScheduledTaskLog latestLog = scheduledTaskLogMapper.selectByTaskAndDate(taskName, targetDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskName", taskName);
            result.put("checkDate", targetDate);
            result.put("isCompleted", isCompleted);
            result.put("latestLog", latestLog);
            
            return ApiResponse.success(result, "检查任务执行状态成功");
            
        } catch (Exception e) {
            log.error("检查任务执行状态失败", e);
            return ApiResponse.error("检查任务执行状态失败");
        }
    }

    /**
     * 获取任务执行详情
     */
    @GetMapping("/task-detail/{taskId}")
    @Operation(summary = "获取任务执行详情", description = "获取指定任务的详细执行信息")
    public ApiResponse<ScheduledTaskLog> getTaskDetail(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        try {
            ScheduledTaskLog taskLog = scheduledTaskLogMapper.selectById(taskId);
            
            if (taskLog == null) {
                return ApiResponse.error("任务记录不存在");
            }
            
            return ApiResponse.success(taskLog, "获取任务执行详情成功");
            
        } catch (Exception e) {
            log.error("获取任务执行详情失败", e);
            return ApiResponse.error("获取任务执行详情失败");
        }
    }

    /**
     * 清理过期的任务日志
     */
    @PostMapping("/cleanup-logs")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "清理过期任务日志", description = "清理指定天数之前的任务执行日志")
    public ApiResponse<String> cleanupTaskLogs(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "30") int keepDays) {
        try {
            LocalDate cutoffDate = LocalDate.now().minusDays(keepDays);
            int deletedCount = scheduledTaskLogMapper.deleteExpiredLogs(cutoffDate);
            
            return ApiResponse.success(null, 
                String.format("清理完成，删除了%d条过期日志记录", deletedCount));
            
        } catch (Exception e) {
            log.error("清理过期任务日志失败", e);
            return ApiResponse.error("清理过期任务日志失败");
        }
    }
}
