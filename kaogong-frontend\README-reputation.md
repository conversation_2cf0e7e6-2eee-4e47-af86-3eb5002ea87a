# 信誉系统前端开发文档

## 📋 概述

本文档介绍考公刷题系统信誉模块的前端实现，包括页面组件、状态管理、API集成和测试方法。

## 🎨 设计风格

信誉系统遵循项目的**手绘温馨风格**设计规范：

- **色彩方案**: 纸质背景色、墨水色彩、温馨强调色
- **字体**: Kalam手写字体 + 系统字体
- **组件风格**: sketch-card、sketch-button等手绘风格
- **装饰元素**: Emoji图标、浮动动画、旋转效果

## 🏗️ 架构设计

### 文件结构
```
src/
├── pages/
│   ├── ReputationCenter.tsx      # 信誉中心主页面
│   ├── ReputationLogs.tsx        # 信誉记录页面
│   └── ReputationTest.tsx        # 测试页面（开发用）
├── components/
│   └── ReputationNotification.tsx # 信誉通知组件
├── stores/
│   └── useReputationStore.ts     # Zustand状态管理
├── services/
│   └── reputationService.ts      # API服务层
├── types/
│   └── reputation.ts             # TypeScript类型定义
└── styles/
    └── reputation.css            # 信誉系统样式
```

### 技术栈
- **React 19** + **TypeScript**
- **Semi Design** UI组件库
- **Zustand** 状态管理
- **React Router** 路由管理
- **Axios** HTTP客户端

## 🎯 核心功能

### 1. 信誉中心页面 (`/reputation`)

**功能特性:**
- 显示用户当前信誉分数和等级
- 等级进度条和升级提示
- 保护期状态显示
- 信誉统计数据（累计获得/扣除、连续天数）
- 等级说明和跳转按钮

**关键组件:**
```tsx
// 信誉概览卡片
<Card className="reputation-overview-card">
  <div className="score-display">
    <h2 className="current-score">{userStats.currentScore}</h2>
  </div>
  <div className="level-badge">
    <IconTrophy />
    {currentLevelInfo?.name}
  </div>
  {/* 等级进度条 */}
  <div className="sketch-progress">
    <div className="sketch-progress-bar" style={{ width: `${progress}%` }} />
  </div>
</Card>
```

### 2. 信誉记录页面 (`/reputation/logs`)

**功能特性:**
- 时间轴样式的信誉变更记录
- 分类筛选（登录奖励、学习奖励等）
- 分页显示和数量控制
- 记录详情（分数变化、原因、来源）

**关键组件:**
```tsx
// 时间轴记录
<Timeline>
  {reputationLogs.map(log => (
    <Timeline.Item
      type={log.changeType === 'earn' ? 'success' : 'warning'}
      extra={<span>{log.changeType === 'earn' ? '+' : '-'}{log.points}分</span>}
    >
      <div>{log.reason}</div>
      <div>{categoryNames[log.category]}</div>
    </Timeline.Item>
  ))}
</Timeline>
```

### 3. 信誉通知组件

**功能特性:**
- 实时信誉变更通知
- 等级升级庆祝动画
- 手绘风格的通知样式
- 自动消失和手动关闭

**使用方法:**
```tsx
import { useReputationNotification } from '../components/ReputationNotification';

const { notification, showNotification, clearNotification } = useReputationNotification();

// 显示通知
showNotification({
  id: 'unique-id',
  changeType: 'earn',
  points: 10,
  reason: '每日登录奖励',
  category: 'daily_login',
  newScore: 110,
  newLevel: 'bronze',
  levelChanged: true
});
```

## 🔧 状态管理

### Zustand Store

```tsx
interface ReputationState {
  // 数据状态
  userStats: UserReputationStats | null;
  reputationLogs: ReputationLog[];
  protectionInfo: ProtectionInfo | null;
  
  // 加载状态
  isLoading: boolean;
  isLoadingLogs: boolean;
  error: string | null;
  
  // 操作方法
  fetchUserStats: () => Promise<void>;
  fetchReputationLogs: (limit?: number) => Promise<void>;
  fetchProtectionStatus: () => Promise<void>;
}
```

### API服务层

```tsx
class ReputationService {
  async getUserReputationStats(): Promise<UserReputationStats>;
  async getReputationLogs(limit: number): Promise<ReputationLog[]>;
  async getProtectionStatus(): Promise<ProtectionInfo>;
}
```

## 🎨 样式系统

### CSS类命名规范

```css
/* 页面容器 */
.reputation-page { /* 页面根容器 */ }
.reputation-container { /* 内容容器 */ }

/* 组件样式 */
.reputation-overview-card { /* 概览卡片 */ }
.score-display { /* 分数显示 */ }
.level-badge { /* 等级徽章 */ }
.sketch-progress { /* 手绘进度条 */ }
.protection-notice { /* 保护期提示 */ }

/* 交互元素 */
.sketch-button { /* 手绘按钮 */ }
.floating-emoji { /* 浮动装饰 */ }
```

### 响应式设计

```css
@media (max-width: 768px) {
  .reputation-overview-card {
    padding: 20px;
    transform: rotate(0deg); /* 移动端取消旋转 */
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}
```

## 🧪 测试方法

### 1. 开发测试页面

访问 `/reputation-test` 页面进行功能测试：

**测试功能:**
- 信誉分数变更通知
- 等级升级庆祝动画
- 不同类型的奖励/扣分场景
- 通知样式和交互效果

### 2. 集成测试

**测试流程:**
1. 用户登录 → 检查登录奖励通知
2. 创建学习记录 → 检查学习奖励通知
3. 访问信誉中心 → 检查数据显示
4. 查看信誉记录 → 检查历史记录

### 3. API测试

```bash
# 获取用户信誉统计
GET /api/v1/reputation/stats

# 获取信誉记录
GET /api/v1/reputation/logs?limit=20

# 获取保护期状态
GET /api/v1/reputation/protection
```

## 🚀 部署和集成

### 1. 路由配置

```tsx
// App.tsx
<Route path="/reputation" element={<ReputationCenter />} />
<Route path="/reputation/logs" element={<ReputationLogs />} />
```

### 2. 导航集成

```tsx
// Navigation.tsx
{
  key: 'reputation',
  path: '/reputation',
  icon: <IconStar />,
  label: '信誉',
  tooltip: '信誉中心'
}
```

### 3. 个人中心集成

在个人中心页面添加信誉概览和跳转按钮：

```tsx
<Link to="/reputation">
  <Button icon={<IconStar />}>
    信誉中心
    <IconArrowRight />
  </Button>
</Link>
```

## 📱 移动端适配

### 响应式特性
- 自适应网格布局
- 移动端优化的按钮尺寸
- 简化的动画效果
- 触摸友好的交互区域

### 移动端测试
```bash
# 使用Chrome DevTools
1. 打开开发者工具
2. 切换到移动设备模拟
3. 测试各个页面的响应式效果
4. 验证触摸交互和滚动性能
```

## 🔍 调试和故障排除

### 常见问题

1. **API调用失败**
   - 检查后端服务是否启动
   - 验证API接口地址和参数
   - 查看浏览器网络面板

2. **样式显示异常**
   - 确认CSS文件正确导入
   - 检查CSS变量定义
   - 验证Semi Design主题配置

3. **状态管理问题**
   - 检查Zustand store的状态更新
   - 验证组件的状态订阅
   - 查看React DevTools

### 调试工具

```tsx
// 开发环境调试
if (process.env.NODE_ENV === 'development') {
  console.log('Reputation State:', useReputationStore.getState());
}
```

## 📈 性能优化

### 1. 代码分割
```tsx
// 懒加载信誉系统页面
const ReputationCenter = lazy(() => import('./pages/ReputationCenter'));
const ReputationLogs = lazy(() => import('./pages/ReputationLogs'));
```

### 2. 缓存策略
- API响应缓存
- 图片资源缓存
- 状态持久化

### 3. 渲染优化
- React.memo包装组件
- useMemo缓存计算结果
- useCallback优化事件处理

## 🎯 后续开发计划

### 短期目标
- [ ] 添加信誉变更推送通知
- [ ] 实现信誉排行榜功能
- [ ] 优化移动端体验

### 长期目标
- [ ] 信誉系统数据可视化
- [ ] 个性化信誉徽章系统
- [ ] 社交分享功能

---

**开发团队**: 考公刷题系统开发组  
**最后更新**: 2025-07-20  
**版本**: v1.0.0
