import React, { useState, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  Typography,
  Tag,
  Avatar,
  Pagination,
  Empty,
  Spin,
  Modal,
  Form,
  InputNumber,
  Toast,
  Divider,
  Row,
  Col
} from '@douyinfe/semi-ui';
import {
  IconSearch,
  IconPlus,
  IconUser,
  IconUserGroup,
  IconCalendar,
  IconStar,
  IconRefresh
} from '@douyinfe/semi-icons';
import { useNavigate } from 'react-router-dom';
import { deskService } from '../services/deskService';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;

interface DeskItem {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  ownerUsername: string;
  ownerNickname: string;
  maxMembers: number;
  currentMembers: number;
  availableSlots: number;
  status: string;
  isOwner: boolean;
  isMember: boolean;
  hasApplied: boolean;
  canJoin: boolean;
  createdAt: string;
}

interface CreateDeskForm {
  name: string;
  description: string;
  maxMembers: number;
  autoApproveRules: boolean;
}

/**
 * 小桌列表页面
 * 显示所有活跃的小桌，支持搜索、创建、申请加入等功能
 */
const DeskList: React.FC = () => {
  const navigate = useNavigate();
  
  // 状态管理
  const [desks, setDesks] = useState<DeskItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0
  });
  
  // 创建小桌模态框
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createFormData, setCreateFormData] = useState<CreateDeskForm>({
    name: '',
    description: '',
    maxMembers: 8,
    autoApproveRules: false
  });
  const [createLoading, setCreateLoading] = useState(false);

  // 加载小桌列表
  const loadDesks = async (page = 1, keyword = '') => {
    try {
      setLoading(true);
      const response = await deskService.searchDesks({
        keyword: keyword || undefined,
        page,
        size: pagination.pageSize
      });
      
      setDesks(response.records);
      setPagination(prev => ({
        ...prev,
        current: page,
        total: response.total
      }));
    } catch (error: any) {
      console.error('加载小桌列表失败:', error);
      Toast.error(error.message || '加载小桌列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 搜索小桌
  const handleSearch = () => {
    loadDesks(1, searchKeyword);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchKeyword('');
    loadDesks(1, '');
  };

  // 分页变化
  const handlePageChange = (page: number) => {
    loadDesks(page, searchKeyword);
  };

  // 创建小桌
  const handleCreateDesk = async () => {
    try {
      setCreateLoading(true);

      const autoApproveRules = createFormData.autoApproveRules ?
        JSON.stringify({ autoApprove: true, minReputationScore: 80 }) :
        JSON.stringify({ autoApprove: false });

      await deskService.createDesk({
        name: createFormData.name,
        description: createFormData.description,
        maxMembers: createFormData.maxMembers,
        autoApproveRules
      });

      Toast.success('小桌创建成功');
      setCreateModalVisible(false);
      setCreateFormData({
        name: '',
        description: '',
        maxMembers: 8,
        autoApproveRules: false
      });
      loadDesks(1, searchKeyword); // 重新加载列表
    } catch (error: any) {
      console.error('创建小桌失败:', error);
      Toast.error(error.message || '创建小桌失败');
    } finally {
      setCreateLoading(false);
    }
  };

  // 申请加入小桌
  const handleApplyToJoin = (desk: DeskItem) => {
    navigate(`/desks/${desk.id}`);
  };

  // 查看小桌详情
  const handleViewDetail = (desk: DeskItem) => {
    navigate(`/desks/${desk.id}`);
  };

  // 进入小桌（已是成员）
  const handleEnterDesk = (desk: DeskItem) => {
    navigate(`/desks/${desk.id}/dashboard`);
  };

  // 初始化加载
  useEffect(() => {
    loadDesks();
  }, []);

  return (
    <>
      <Navigation />
      <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto', paddingTop: '104px' }}>
      {/* 页面标题和操作栏 */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title heading={2} style={{ margin: 0 }}>
            学习小桌
          </Title>
          <Button 
            theme="solid" 
            type="primary" 
            icon={<IconPlus />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建小桌
          </Button>
        </div>
        
        <Text type="secondary">
          加入学习小桌，与志同道合的伙伴一起学习进步，查看排行榜激励自己！
        </Text>
      </div>

      {/* 搜索栏 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <Input
            prefix={<IconSearch />}
            placeholder="搜索小桌名称..."
            value={searchKeyword}
            onChange={setSearchKeyword}
            onEnterPress={handleSearch}
            style={{ width: '300px' }}
          />
          <Button type="primary" onClick={handleSearch}>
            搜索
          </Button>
          <Button icon={<IconRefresh />} onClick={handleReset}>
            重置
          </Button>
        </Space>
      </Card>

      {/* 小桌列表 */}
      <Spin spinning={loading}>
        {desks.length > 0 ? (
          <>
            <Row gutter={[16, 16]}>
              {desks.map(desk => (
                <Col span={8} key={desk.id}>
                  <Card
                    style={{ height: '100%' }}
                    bodyStyle={{ padding: '20px' }}
                    hoverable
                  >
                    {/* 小桌标题 */}
                    <div style={{ marginBottom: '12px' }}>
                      <Title heading={4} style={{ margin: 0, marginBottom: '8px' }}>
                        {desk.name}
                      </Title>
                      <Text type="secondary" size="small">
                        {desk.description || '暂无描述'}
                      </Text>
                    </div>

                    {/* 桌长信息 */}
                    <div style={{ marginBottom: '12px' }}>
                      <Space>
                        <Avatar size="small">
                          <IconUser />
                        </Avatar>
                        <Text size="small">
                          桌长：{desk.ownerNickname || desk.ownerUsername}
                        </Text>
                      </Space>
                    </div>

                    {/* 成员信息 */}
                    <div style={{ marginBottom: '12px' }}>
                      <Space>
                        <IconUserGroup />
                        <Text size="small">
                          {desk.currentMembers}/{desk.maxMembers} 人
                        </Text>
                        <Tag 
                          color={desk.availableSlots > 0 ? 'green' : 'red'}
                          size="small"
                        >
                          {desk.availableSlots > 0 ? `还有${desk.availableSlots}个位置` : '已满员'}
                        </Tag>
                      </Space>
                    </div>

                    {/* 创建时间 */}
                    <div style={{ marginBottom: '16px' }}>
                      <Space>
                        <IconCalendar />
                        <Text size="small" type="secondary">
                          {new Date(desk.createdAt).toLocaleDateString()}
                        </Text>
                      </Space>
                    </div>

                    <Divider margin="12px" />

                    {/* 操作按钮 */}
                    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                      <Button 
                        size="small" 
                        type="tertiary"
                        onClick={() => handleViewDetail(desk)}
                      >
                        查看详情
                      </Button>
                      
                      {desk.isMember ? (
                        <Button 
                          size="small" 
                          type="primary"
                          onClick={() => handleEnterDesk(desk)}
                        >
                          进入小桌
                        </Button>
                      ) : desk.hasApplied ? (
                        <Button size="small" disabled>
                          已申请
                        </Button>
                      ) : desk.canJoin ? (
                        <Button 
                          size="small" 
                          theme="solid" 
                          type="primary"
                          onClick={() => handleApplyToJoin(desk)}
                        >
                          申请加入
                        </Button>
                      ) : (
                        <Button size="small" disabled>
                          无法加入
                        </Button>
                      )}
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>

            {/* 分页 */}
            {pagination.total > pagination.pageSize && (
              <div style={{ textAlign: 'center', marginTop: '32px' }}>
                <Pagination
                  current={pagination.current}
                  pageSize={pagination.pageSize}
                  total={pagination.total}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                />
              </div>
            )}
          </>
        ) : (
          <Empty
            image={<IconStar size="extra-large" />}
            title="暂无小桌"
            description="还没有找到符合条件的学习小桌，试试创建一个吧！"
          />
        )}
      </Spin>

      {/* 创建小桌模态框 */}
      <Modal
        title="创建学习小桌"
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <Typography.Text strong>小桌名称</Typography.Text>
            <Input
              placeholder="请输入小桌名称"
              value={createFormData.name}
              onChange={(value) => setCreateFormData(prev => ({ ...prev, name: value }))}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <Typography.Text strong>小桌描述</Typography.Text>
            <Input
              placeholder="请描述小桌的学习目标和特色"
              value={createFormData.description}
              onChange={(value) => setCreateFormData(prev => ({ ...prev, description: value }))}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div style={{ marginBottom: '16px' }}>
            <Typography.Text strong>最大成员数</Typography.Text>
            <InputNumber
              placeholder="2-8人"
              min={2}
              max={8}
              value={createFormData.maxMembers}
              onChange={(value) => setCreateFormData(prev => ({ ...prev, maxMembers: value || 8 }))}
              style={{ marginTop: '8px', width: '100%' }}
            />
          </div>

          <div style={{ marginBottom: '24px' }}>
            <Typography.Text strong>自动审核</Typography.Text>
            <div style={{ marginTop: '8px' }}>
              <Button
                type={createFormData.autoApproveRules ? 'primary' : 'tertiary'}
                onClick={() => setCreateFormData(prev => ({ ...prev, autoApproveRules: !prev.autoApproveRules }))}
              >
                {createFormData.autoApproveRules ? '已开启' : '已关闭'}
              </Button>
              <Typography.Text type="secondary" size="small" style={{ marginLeft: '8px' }}>
                开启后，信誉分数达到要求的用户可以自动加入
              </Typography.Text>
            </div>
          </div>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setCreateModalVisible(false)}>
                取消
              </Button>
              <Button
                theme="solid"
                type="primary"
                onClick={handleCreateDesk}
                loading={createLoading}
                disabled={!createFormData.name.trim()}
              >
                创建小桌
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
      </div>
    </>
  );
};

export default DeskList;
