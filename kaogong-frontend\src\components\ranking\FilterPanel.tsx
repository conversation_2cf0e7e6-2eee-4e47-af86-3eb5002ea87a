import React from 'react';
import { Card, Select, Space, Typography, Tooltip } from '@douyinfe/semi-ui';
import { IconInfoCircle } from '@douyinfe/semi-icons';
import { RankingType, RankingPeriod, rankingUtils } from '../../services/rankingService';
import './FilterPanel.css';

const { Title, Text } = Typography;

interface FilterPanelProps {
  selectedType: RankingType;
  selectedPeriod: RankingPeriod;
  onTypeChange: (type: RankingType) => void;
  onPeriodChange: (period: RankingPeriod) => void;
  loading?: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  selectedType,
  selectedPeriod,
  onTypeChange,
  onPeriodChange,
  loading = false
}) => {
  // 处理类型变化的包装函数
  const handleTypeChange = (value: any) => {
    console.log('Type changed:', value);
    onTypeChange(value as RankingType);
  };

  // 处理周期变化的包装函数
  const handlePeriodChange = (value: any) => {
    console.log('Period changed:', value);
    onPeriodChange(value as RankingPeriod);
  };
  const rankingTypes: Array<{ value: RankingType; label: string; description: string }> = [
    {
      value: 'study_questions',
      label: '刷题数量',
      description: '根据完成的题目总数排名'
    },
    {
      value: 'study_accuracy',
      label: '正确率',
      description: '根据答题的平均正确率排名'
    },
    {
      value: 'study_time',
      label: '学习时长',
      description: '根据累计学习时长排名'
    },
    {
      value: 'reputation_score',
      label: '信誉分数',
      description: '根据当前信誉分数排名'
    },
    {
      value: 'comprehensive',
      label: '综合排名',
      description: '多维度加权计算的综合排名'
    }
  ];

  const rankingPeriods: Array<{ value: RankingPeriod; label: string; description: string }> = [
    {
      value: 'daily',
      label: '日榜',
      description: '当日排行榜'
    },
    {
      value: 'weekly',
      label: '周榜',
      description: '本周排行榜'
    },
    {
      value: 'monthly',
      label: '月榜',
      description: '本月排行榜'
    },
    {
      value: 'all_time',
      label: '总榜',
      description: '历史总排行榜'
    }
  ];



  return (
    <div className="filter-panel-horizontal">
      {/* 横排筛选布局 */}
      <div className="filter-row">
        {/* 排行榜类型选择 */}
        <div className="filter-group">
          <Text strong className="filter-label">
            排行榜类型
          </Text>
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            style={{ width: '200px' }}
            placeholder="选择排行榜类型"
            disabled={loading}
            size="default"
            showClear={false}
          >
            {rankingTypes.map(type => (
              <Select.Option key={type.value} value={type.value}>
                <div className="filter-option">
                  <div className="option-label">{type.label}</div>
                  <div className="option-description">{type.description}</div>
                </div>
              </Select.Option>
            ))}
          </Select>
        </div>

        {/* 时间周期选择 */}
        <div className="filter-group">
          <Text strong className="filter-label">
            时间周期
          </Text>
          <Select
            value={selectedPeriod}
            onChange={handlePeriodChange}
            style={{ width: '150px' }}
            placeholder="选择时间周期"
            disabled={loading}
            size="default"
            showClear={false}
          >
            {rankingPeriods.map(period => (
              <Select.Option key={period.value} value={period.value}>
                <div className="filter-option">
                  <div className="option-label">{period.label}</div>
                  <div className="option-description">{period.description}</div>
                </div>
              </Select.Option>
            ))}
          </Select>
        </div>

        {/* 当前选择显示 */}
        <div className="current-selection">
          <Text type="secondary" size="small">
            当前查看：
            <Text code style={{ margin: '0 4px' }}>
              {rankingUtils.getTypeDisplayName(selectedType)}
            </Text>
            ·
            <Text code style={{ margin: '0 4px' }}>
              {rankingUtils.getPeriodDisplayName(selectedPeriod)}
            </Text>
          </Text>
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
