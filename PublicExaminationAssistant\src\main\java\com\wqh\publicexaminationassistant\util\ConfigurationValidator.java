package com.wqh.publicexaminationassistant.util;

import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 配置验证器
 * 在应用启动时验证文件上传配置和环境
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(value = "app.config.validation.enabled", havingValue = "true", matchIfMissing = true)
public class ConfigurationValidator implements CommandLineRunner {

    private final FileUploadProperties fileUploadProperties;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始验证文件上传配置和环境...");
        
        boolean allValid = true;
        
        // 验证配置加载
        allValid &= validateConfigurationLoading();
        
        // 验证目录结构
        allValid &= validateDirectoryStructure();
        
        // 验证权限
        allValid &= validatePermissions();
        
        // 验证配置合理性
        allValid &= validateConfigurationValues();
        
        if (allValid) {
            log.info("✅ 文件上传配置验证通过，系统已准备就绪！");
            printConfigurationSummary();
        } else {
            log.error("❌ 文件上传配置验证失败，请检查配置和环境！");
        }
    }

    /**
     * 验证配置是否正确加载
     */
    private boolean validateConfigurationLoading() {
        try {
            if (fileUploadProperties == null) {
                log.error("FileUploadProperties未正确注入");
                return false;
            }
            
            if (fileUploadProperties.getAllowedTypes() == null || fileUploadProperties.getAllowedTypes().isEmpty()) {
                log.error("允许的文件类型配置为空");
                return false;
            }
            
            if (fileUploadProperties.getPath() == null || fileUploadProperties.getPath().trim().isEmpty()) {
                log.error("上传路径配置为空");
                return false;
            }
            
            log.info("✅ 配置加载验证通过");
            return true;
        } catch (Exception e) {
            log.error("配置加载验证失败", e);
            return false;
        }
    }

    /**
     * 验证目录结构
     */
    private boolean validateDirectoryStructure() {
        try {
            String projectRoot = System.getProperty("user.dir");
            Path uploadsPath = Paths.get(projectRoot, "uploads");
            
            // 检查主目录
            if (!Files.exists(uploadsPath)) {
                log.error("uploads目录不存在: {}", uploadsPath);
                return false;
            }
            
            if (!Files.isDirectory(uploadsPath)) {
                log.error("uploads路径不是目录: {}", uploadsPath);
                return false;
            }
            
            // 检查子目录
            String[] requiredDirs = {
                "wrong-questions",
                "wrong-questions/question",
                "wrong-questions/answer", 
                "wrong-questions/explanation",
                "wrong-questions/thumbnails",
                "temp"
            };
            
            for (String dir : requiredDirs) {
                Path dirPath = uploadsPath.resolve(dir);
                if (!Files.exists(dirPath)) {
                    log.error("必需的目录不存在: {}", dirPath);
                    return false;
                }
                if (!Files.isDirectory(dirPath)) {
                    log.error("路径不是目录: {}", dirPath);
                    return false;
                }
            }
            
            log.info("✅ 目录结构验证通过");
            return true;
        } catch (Exception e) {
            log.error("目录结构验证失败", e);
            return false;
        }
    }

    /**
     * 验证权限
     */
    private boolean validatePermissions() {
        try {
            String projectRoot = System.getProperty("user.dir");
            Path uploadsPath = Paths.get(projectRoot, "uploads");
            
            if (!Files.isReadable(uploadsPath)) {
                log.error("uploads目录不可读: {}", uploadsPath);
                return false;
            }
            
            if (!Files.isWritable(uploadsPath)) {
                log.error("uploads目录不可写: {}", uploadsPath);
                return false;
            }
            
            // 测试创建临时文件
            Path testFile = uploadsPath.resolve("temp").resolve("test_" + System.currentTimeMillis() + ".tmp");
            try {
                Files.createFile(testFile);
                Files.delete(testFile);
                log.info("✅ 权限验证通过");
                return true;
            } catch (Exception e) {
                log.error("无法在uploads目录创建文件: {}", e.getMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("权限验证失败", e);
            return false;
        }
    }

    /**
     * 验证配置值的合理性
     */
    private boolean validateConfigurationValues() {
        try {
            // 验证文件大小限制
            if (fileUploadProperties.getMaxSize() <= 0) {
                log.error("文件最大大小配置无效: {}", fileUploadProperties.getMaxSize());
                return false;
            }
            
            if (fileUploadProperties.getMaxSize() > 100 * 1024 * 1024) { // 100MB
                log.warn("文件最大大小配置过大: {} MB", fileUploadProperties.getMaxSize() / 1024 / 1024);
            }
            
            // 验证图片配置
            FileUploadProperties.ImageConfig imageConfig = fileUploadProperties.getImage();
            if (imageConfig.getMaxWidth() <= 0 || imageConfig.getMaxHeight() <= 0) {
                log.error("图片尺寸配置无效: {}x{}", imageConfig.getMaxWidth(), imageConfig.getMaxHeight());
                return false;
            }
            
            if (imageConfig.getQuality() < 1 || imageConfig.getQuality() > 100) {
                log.error("图片质量配置无效: {}", imageConfig.getQuality());
                return false;
            }
            
            // 验证缩略图配置
            FileUploadProperties.ThumbnailConfig thumbnailConfig = fileUploadProperties.getThumbnail();
            if (thumbnailConfig.getWidth() <= 0 || thumbnailConfig.getHeight() <= 0) {
                log.error("缩略图尺寸配置无效: {}x{}", thumbnailConfig.getWidth(), thumbnailConfig.getHeight());
                return false;
            }
            
            log.info("✅ 配置值验证通过");
            return true;
        } catch (Exception e) {
            log.error("配置值验证失败", e);
            return false;
        }
    }

    /**
     * 打印配置摘要
     */
    private void printConfigurationSummary() {
        log.info("==================== 文件上传配置摘要 ====================");
        log.info("最大文件大小: {} MB", fileUploadProperties.getMaxSize() / 1024 / 1024);
        log.info("允许的文件类型: {}", String.join(", ", fileUploadProperties.getAllowedTypes()));
        log.info("上传路径: {}", fileUploadProperties.getPath());
        log.info("图片最大尺寸: {}x{}", 
                fileUploadProperties.getImage().getMaxWidth(), 
                fileUploadProperties.getImage().getMaxHeight());
        log.info("图片质量: {}", fileUploadProperties.getImage().getQuality());
        log.info("缩略图尺寸: {}x{}", 
                fileUploadProperties.getThumbnail().getWidth(), 
                fileUploadProperties.getThumbnail().getHeight());
        log.info("存储类型: {}", fileUploadProperties.getStorage().getType());
        log.info("基础URL: {}", fileUploadProperties.getStorage().getBaseUrl());
        log.info("========================================================");
    }
}
