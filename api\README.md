# 考公刷题记录系统 - API 接口文档总览

## 📋 文档结构

```
api/
├── README.md                    # 本总览文档
├── user-management-api.md       # 用户管理模块 API
├── study-records-api.md         # 刷题记录模块 API
├── desk-system-api.md          # 小桌系统模块 API 🌟
├── reputation-system-api.md    # 信誉体系模块 API 🌟
├── rankings-api.md             # 排行榜模块 API
├── announcements-api.md        # 考试公告模块 API
└── notifications-api.md        # 通知系统模块 API
```

## 🎯 API 设计概览

### 设计原则
- **RESTful 规范**: 遵循 REST 架构风格，资源导向设计
- **统一响应格式**: 标准化的成功/错误响应结构
- **版本控制**: 使用 URL 版本控制 `/api/v1/`
- **安全认证**: JWT Token 认证 + 基于角色的权限控制
- **性能优化**: 支持分页、缓存、批量操作

### 技术栈集成
- **Spring Boot**: 基于 Spring MVC 的控制器设计
- **MyBatis Plus**: 统一的分页和查询规范
- **JWT 认证**: 完整的认证授权机制
- **MySQL 8.0**: 支持全文搜索和 JSON 字段
- **Redis 缓存**: 高频数据缓存策略

## 📊 模块统计

| 模块 | 接口数量 | 优先级 | 核心功能 |
|------|---------|--------|---------|
| 用户管理 | 15个 | P0 | 注册登录、个人资料、邀请码管理 |
| 刷题记录 | 22个 | P1 | 学习记录、错题本、学习计划、统计分析 |
| 小桌系统 🌟 | 28个 | P1 | 小桌管理、成员审核、排行榜、互动 |
| 信誉体系 🌟 | 24个 | P1 | 信誉计算、等级管理、行为记录、质量保障 |
| 排行榜 | 18个 | P2 | 多维度排行、历史趋势、统计分析 |
| 考试公告 | 20个 | P2 | 公告管理、全文搜索、时间提醒 |
| 通知系统 | 26个 | P2 | 消息推送、站内信、通知设置 |
| **总计** | **153个** | - | **完整的考公刷题记录系统** |

## 🔐 认证授权体系

### JWT Token 认证
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 权限等级
| 角色 | 权限范围 | 说明 |
|------|---------|------|
| `user` | 基础用户权限 | 使用所有普通功能 |
| `admin` | 管理员权限 | 用户管理、公告管理、内容审核 |
| `super_admin` | 超级管理员 | 所有权限，包括系统配置 |

### 小桌权限
| 角色 | 权限范围 | 说明 |
|------|---------|------|
| `owner` | 桌长权限 | 小桌管理、成员审核、活跃度管理 |
| `member` | 成员权限 | 参与小桌活动、查看排行榜 |

## 📝 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [...],
    "total": 100,
    "current": 1,
    "size": 20,
    "pages": 5
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ],
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🌟 核心创新功能

### 小桌系统 API 亮点
- **完整的小桌生命周期管理**: 创建、成员管理、解散
- **智能申请审核系统**: 自动审核规则 + 人工审核
- **多维度排行榜**: 小桌内排行 + 小桌间对比
- **活跃度管理**: 成员活跃度监控和预警
- **社交互动功能**: 动态分享、点赞评论

### 信誉体系 API 亮点
- **全方位行为记录**: 学习、社交、违规行为追踪
- **动态信誉计算**: 实时分数更新和等级变化
- **质量保障机制**: 举报处理、信用档案管理
- **激励机制**: 成就系统、特权解锁
- **数据分析**: 行为统计、趋势分析

## 🚀 性能优化特性

### 分页查询
- 统一的分页参数: `page`, `size`
- 支持多字段排序: `sortBy`, `sortOrder`
- 总数优化: 大数据量时的 count 优化

### 缓存策略
- 用户信息缓存 (30分钟)
- 排行榜数据缓存 (5分钟)
- 公告列表缓存 (10分钟)
- 通知未读数缓存 (1分钟)

### 批量操作
- 批量标记通知已读
- 批量审核小桌申请
- 批量处理公告状态
- 批量信誉分数调整

## 🔍 搜索功能

### 全文搜索
- **公告搜索**: 支持中文分词的全文搜索
- **用户搜索**: 用户名、昵称模糊搜索
- **小桌搜索**: 小桌名称、描述搜索
- **搜索高亮**: 关键词高亮显示

### 高级筛选
- **多条件组合**: 支持多个筛选条件组合
- **时间范围**: 灵活的时间范围筛选
- **状态筛选**: 按状态、类型等筛选
- **排序选项**: 多种排序方式

## 📈 数据统计 API

### 个人统计
- 学习数据统计 (题量、正确率、时长)
- 信誉变化趋势
- 排名历史记录
- 小桌参与情况

### 系统统计
- 用户增长统计
- 学习活跃度统计
- 小桌运营数据
- 公告浏览统计

## 🛠️ 开发集成

### Spring Boot 配置
```yaml
# JWT 配置
jwt:
  secret: your-secret-key
  expiration: 7200 # 2小时
  refresh-expiration: 604800 # 7天

# 分页配置
mybatis-plus:
  configuration:
    default-fetch-size: 100
    default-statement-timeout: 30
```

### 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse> handleValidation(ValidationException e) {
        // 统一错误响应处理
    }
}
```

### 权限控制注解
```java
@PreAuthorize("hasRole('ADMIN')")
@PreAuthorize("hasRole('USER') and @deskService.isMember(#deskId, authentication.name)")
```

## 📋 接口测试

### Postman 集合
每个模块都提供完整的 Postman 测试集合，包含：
- 环境变量配置
- 认证 Token 自动管理
- 完整的测试用例
- 自动化测试脚本

### Mock 数据
提供真实业务场景的 Mock 数据：
- 用户数据 (不同角色、信誉等级)
- 学习记录数据 (各种模块类型)
- 小桌数据 (不同状态、成员数量)
- 公告数据 (各种类型、地区)

## 🔄 版本控制

### API 版本策略
- **当前版本**: v1
- **版本格式**: `/api/v1/`
- **向后兼容**: 旧版本保持 6 个月支持
- **废弃通知**: 响应头添加 `Deprecated: true`

### 变更管理
- **新增接口**: 不影响现有版本
- **字段新增**: 向后兼容
- **字段删除**: 需要版本升级
- **行为变更**: 需要版本升级

## 📞 技术支持

### 相关文档
- 数据库设计: `database/database-design.md`
- 系统架构: `doc/system-architecture.md`
- 开发规范: `doc/development-standards.md`

### 开发工具
- **Swagger UI**: 在线接口文档和测试
- **Postman**: API 测试集合
- **Mock Server**: 基于 OpenAPI 的 Mock 服务

---

## 🎉 总结

这套 API 接口设计完整覆盖了考公刷题记录系统的所有功能需求，特别是**小桌系统**和**信誉体系**两个核心创新功能。通过 153 个精心设计的接口，为前端开发和移动端开发提供了完整的数据支持。

**核心优势**:
- ✅ **功能完整**: 覆盖所有业务场景
- ✅ **设计规范**: 遵循 RESTful 最佳实践
- ✅ **性能优化**: 支持缓存、分页、批量操作
- ✅ **安全可靠**: 完整的认证授权机制
- ✅ **易于集成**: 标准化的响应格式和错误处理
- ✅ **文档完善**: 详细的接口文档和测试用例

现在可以基于这套 API 设计开始后端开发工作，为考公刷题记录系统构建强大的数据服务层！
