import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Avatar,
  Descriptions,
  Spin,
  Toast,
  Modal,
  Input,
  Divider,
  Progress
} from '@douyinfe/semi-ui';
import {
  IconUser,
  IconUserGroup,
  IconCalendar,
  IconSetting,
  IconArrowLeft,
  IconStar,
  IconEdit,
  IconDelete
} from '@douyinfe/semi-icons';
import { useParams, useNavigate } from 'react-router-dom';
import { deskService, DeskResponse, ApplyDeskRequest } from '../services/deskService';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;

/**
 * 小桌详情页面
 * 显示小桌的详细信息，支持申请加入、管理等操作
 */
const DeskDetail: React.FC = () => {
  const { deskId } = useParams<{ deskId: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const [desk, setDesk] = useState<DeskResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [applyModalVisible, setApplyModalVisible] = useState(false);
  const [applyFormData, setApplyFormData] = useState<ApplyDeskRequest>({
    deskId: '',
    reason: '',
    studyPlan: ''
  });
  const [applyLoading, setApplyLoading] = useState(false);

  // 加载小桌详情
  const loadDeskDetail = async () => {
    if (!deskId) return;
    
    try {
      setLoading(true);
      const response = await deskService.getDeskById(deskId);
      setDesk(response);
    } catch (error: any) {
      console.error('加载小桌详情失败:', error);
      Toast.error(error.message || '加载小桌详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 申请加入小桌
  const handleApplyToJoin = async () => {
    if (!deskId) return;

    try {
      setApplyLoading(true);
      await deskService.applyToJoinDesk({
        deskId,
        reason: applyFormData.reason,
        studyPlan: applyFormData.studyPlan
      });

      Toast.success('申请提交成功，请等待桌长审核');
      setApplyModalVisible(false);
      setApplyFormData({
        deskId: '',
        reason: '',
        studyPlan: ''
      });
      loadDeskDetail(); // 重新加载详情
    } catch (error: any) {
      console.error('申请加入失败:', error);
      Toast.error(error.message || '申请加入失败');
    } finally {
      setApplyLoading(false);
    }
  };

  // 进入小桌管理
  const handleEnterDesk = () => {
    navigate(`/desks/${deskId}/dashboard`);
  };

  // 编辑小桌
  const handleEditDesk = () => {
    navigate(`/desks/${deskId}/edit`);
  };

  // 解散小桌
  const handleDissolveDesk = () => {
    Modal.confirm({
      title: '确认解散小桌',
      content: '解散后小桌将无法恢复，所有成员将被移除，确定要解散吗？',
      onOk: async () => {
        try {
          await deskService.dissolveDesk(deskId!);
          Toast.success('小桌已解散');
          navigate('/desks');
        } catch (error: any) {
          Toast.error(error.message || '解散小桌失败');
        }
      }
    });
  };

  // 返回列表
  const handleGoBack = () => {
    navigate('/desks');
  };

  // 初始化加载
  useEffect(() => {
    loadDeskDetail();
  }, [deskId]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!desk) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Text>小桌不存在或已被删除</Text>
      </div>
    );
  }

  return (
    <>
      <Navigation />
      <div style={{ padding: '24px', maxWidth: '1000px', margin: '0 auto', paddingTop: '104px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<IconArrowLeft />} 
            onClick={handleGoBack}
          >
            返回列表
          </Button>
        </Space>
      </div>

      {/* 小桌基本信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <Title heading={2} style={{ margin: 0, marginBottom: '8px' }}>
              {desk.name}
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {desk.description || '暂无描述'}
            </Text>
          </div>
          
          <div>
            <Space>
              {desk.isOwner && (
                <>
                  <Button 
                    icon={<IconEdit />}
                    onClick={handleEditDesk}
                  >
                    编辑
                  </Button>
                  <Button 
                    type="danger"
                    icon={<IconDelete />}
                    onClick={handleDissolveDesk}
                  >
                    解散
                  </Button>
                </>
              )}
              
              {desk.isMember ? (
                <Button 
                  theme="solid" 
                  type="primary"
                  icon={<IconSetting />}
                  onClick={handleEnterDesk}
                >
                  进入小桌
                </Button>
              ) : desk.hasApplied ? (
                <Button disabled>
                  已申请，等待审核
                </Button>
              ) : desk.canJoin ? (
                <Button 
                  theme="solid" 
                  type="primary"
                  onClick={() => setApplyModalVisible(true)}
                >
                  申请加入
                </Button>
              ) : (
                <Button disabled>
                  无法加入
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Card>

      {/* 详细信息 */}
      <Card title="小桌信息" style={{ marginBottom: '24px' }}>
        <Descriptions row>
          <Descriptions.Item itemKey="owner" label="桌长">
            <Space>
              <Avatar size="small">
                <IconUser />
              </Avatar>
              <Text>{desk.ownerNickname || desk.ownerUsername}</Text>
              {desk.isOwner && <Tag color="blue" size="small">我</Tag>}
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item itemKey="members" label="成员情况">
            <Space>
              <IconUserGroup />
              <Text>{desk.currentMembers}/{desk.maxMembers} 人</Text>
              <Tag 
                color={desk.availableSlots > 0 ? 'green' : 'red'}
                size="small"
              >
                {desk.availableSlots > 0 ? `还有${desk.availableSlots}个位置` : '已满员'}
              </Tag>
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item itemKey="created" label="创建时间">
            <Space>
              <IconCalendar />
              <Text>{new Date(desk.createdAt).toLocaleString()}</Text>
            </Space>
          </Descriptions.Item>
          
          <Descriptions.Item itemKey="status" label="状态">
            <Tag color={desk.status === 'active' ? 'green' : 'red'}>
              {desk.status === 'active' ? '活跃' : '已停用'}
            </Tag>
          </Descriptions.Item>
        </Descriptions>

        {/* 成员进度条 */}
        <div style={{ marginTop: '20px' }}>
          <Text strong>成员进度</Text>
          <Progress 
            percent={(desk.currentMembers / desk.maxMembers) * 100}
            showInfo
            format={() => `${desk.currentMembers}/${desk.maxMembers}`}
            style={{ marginTop: '8px' }}
          />
        </div>
      </Card>

      {/* 桌长专属信息 */}
      {desk.isOwner && desk.pendingApplications !== undefined && (
        <Card title="管理信息" style={{ marginBottom: '24px' }}>
          <Descriptions row>
            <Descriptions.Item itemKey="applications" label="待处理申请">
              <Space>
                <Text>{desk.pendingApplications} 个</Text>
                {desk.pendingApplications > 0 && (
                  <Button 
                    size="small" 
                    type="primary"
                    onClick={() => navigate(`/desks/${deskId}/applications`)}
                  >
                    去处理
                  </Button>
                )}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}

      {/* 申请加入模态框 */}
      <Modal
        title="申请加入小桌"
        visible={applyModalVisible}
        onCancel={() => setApplyModalVisible(false)}
        footer={null}
        width={500}
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <Typography.Text strong>申请理由</Typography.Text>
            <Input
              placeholder="请说明您为什么想加入这个小桌，以及您的学习目标"
              value={applyFormData.reason}
              onChange={(value) => setApplyFormData(prev => ({ ...prev, reason: value }))}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div style={{ marginBottom: '24px' }}>
            <Typography.Text strong>学习计划（可选）</Typography.Text>
            <Input
              placeholder="请简单描述您的学习计划和时间安排"
              value={applyFormData.studyPlan}
              onChange={(value) => setApplyFormData(prev => ({ ...prev, studyPlan: value }))}
              style={{ marginTop: '8px' }}
            />
          </div>

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setApplyModalVisible(false)}>
                取消
              </Button>
              <Button
                theme="solid"
                type="primary"
                onClick={handleApplyToJoin}
                loading={applyLoading}
                disabled={!applyFormData.reason.trim()}
              >
                提交申请
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
      </div>
    </>
  );
};

export default DeskDetail;
