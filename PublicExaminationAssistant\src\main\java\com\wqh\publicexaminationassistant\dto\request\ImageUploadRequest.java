package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 图片上传请求DTO
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
public class ImageUploadRequest {

    /**
     * 图片类型：question|answer|explanation
     */
    @NotBlank(message = "图片类型不能为空")
    @Pattern(regexp = "^(question|answer|explanation)$", 
             message = "图片类型必须是: question, answer, explanation")
    private String imageType;

    /**
     * 排序顺序
     */
    @Min(value = 0, message = "排序顺序不能为负数")
    private Integer sortOrder = 0;

    /**
     * 描述信息（可选）
     */
    private String description;
}
