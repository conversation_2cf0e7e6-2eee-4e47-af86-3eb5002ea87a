package com.wqh.publicexaminationassistant.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.CreateDeskRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateDeskRequest;
import com.wqh.publicexaminationassistant.dto.response.DeskResponse;
import com.wqh.publicexaminationassistant.entity.Desk;
import com.wqh.publicexaminationassistant.entity.DeskMember;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.mapper.DeskApplicationMapper;
import com.wqh.publicexaminationassistant.mapper.DeskMapper;
import com.wqh.publicexaminationassistant.mapper.DeskMemberMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小桌管理服务
 * 提供小桌的创建、查询、更新、解散等核心业务功能
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class DeskService {

    private final DeskMapper deskMapper;
    private final DeskMemberMapper deskMemberMapper;
    private final DeskApplicationMapper deskApplicationMapper;
    private final UserMapper userMapper;

    /**
     * 创建小桌
     */
    public DeskResponse createDesk(CreateDeskRequest request, String userId) {
        log.info("创建小桌: userId={}, name={}", userId, request.getName());

        // 1. 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 检查用户创建的小桌数量限制（最多创建3个）
        Long ownedDesksCount = deskMapper.countByOwnerId(userId);
        if (ownedDesksCount >= 3) {
            throw new BusinessException(ResultCode.DESK_MEMBER_LIMIT, "每个用户最多只能创建3个小桌");
        }

        // 3. 检查小桌名称是否重复
        QueryWrapper<Desk> nameQuery = new QueryWrapper<>();
        nameQuery.eq("name", request.getName()).eq("status", "active");
        if (deskMapper.selectCount(nameQuery) > 0) {
            throw new BusinessException(ResultCode.CONFLICT, "小桌名称已存在");
        }

        // 4. 创建小桌
        Desk desk = new Desk();
        BeanUtils.copyProperties(request, desk);
        desk.setOwnerId(userId);
        desk.setCurrentMembers(1); // 桌长自动成为第一个成员
        desk.setStatus("active");

        int result = deskMapper.insert(desk);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建小桌失败");
        }

        // 5. 将桌长添加为成员
        DeskMember ownerMember = new DeskMember();
        ownerMember.setDeskId(desk.getId());
        ownerMember.setUserId(userId);
        ownerMember.setRole("owner");
        ownerMember.setStatus("active");
        ownerMember.setJoinReason("创建小桌");

        int memberResult = deskMemberMapper.insert(ownerMember);
        if (memberResult <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "添加桌长成员关系失败");
        }

        log.info("小桌创建成功: deskId={}, name={}", desk.getId(), desk.getName());

        // 6. 返回响应
        return buildDeskResponse(desk, userId);
    }

    /**
     * 根据ID获取小桌详情
     */
    public DeskResponse getDeskById(String deskId, String userId) {
        log.info("获取小桌详情: deskId={}, userId={}", deskId, userId);

        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        return buildDeskResponse(desk, userId);
    }

    /**
     * 更新小桌信息
     */
    public DeskResponse updateDesk(String deskId, UpdateDeskRequest request, String userId) {
        log.info("更新小桌: deskId={}, userId={}", deskId, userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证权限（只有桌长可以更新）
        if (!desk.isOwner(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以更新小桌信息");
        }

        // 3. 检查小桌名称是否重复（如果修改了名称）
        if (StringUtils.hasText(request.getName()) && !request.getName().equals(desk.getName())) {
            QueryWrapper<Desk> nameQuery = new QueryWrapper<>();
            nameQuery.eq("name", request.getName()).eq("status", "active").ne("id", deskId);
            if (deskMapper.selectCount(nameQuery) > 0) {
                throw new BusinessException(ResultCode.CONFLICT, "小桌名称已存在");
            }
        }

        // 4. 检查最大成员数是否合理（不能小于当前成员数）
        if (request.getMaxMembers() != null && request.getMaxMembers() < desk.getCurrentMembers()) {
            throw new BusinessException(ResultCode.BAD_REQUEST,
                    "最大成员数不能小于当前成员数(" + desk.getCurrentMembers() + ")");
        }

        // 5. 更新小桌信息
        if (StringUtils.hasText(request.getName())) {
            desk.setName(request.getName());
        }
        if (StringUtils.hasText(request.getDescription())) {
            desk.setDescription(request.getDescription());
        }
        if (request.getMaxMembers() != null) {
            desk.setMaxMembers(request.getMaxMembers());
        }
        if (StringUtils.hasText(request.getAutoApproveRules())) {
            desk.setAutoApproveRules(request.getAutoApproveRules());
        }

        int result = deskMapper.updateById(desk);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新小桌失败");
        }

        log.info("小桌更新成功: deskId={}", deskId);

        return buildDeskResponse(desk, userId);
    }

    /**
     * 解散小桌
     */
    public void dissolveDesk(String deskId, String userId) {
        log.info("解散小桌: deskId={}, userId={}", deskId, userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证权限（只有桌长可以解散）
        if (!desk.isOwner(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以解散小桌");
        }

        // 3. 检查小桌状态
        if (desk.isDissolved()) {
            throw new BusinessException(ResultCode.CONFLICT, "小桌已经解散");
        }

        // 4. 更新小桌状态为已解散
        desk.setStatus("dissolved");
        int result = deskMapper.updateById(desk);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "解散小桌失败");
        }

        // 5. 将所有成员状态设置为非活跃
        QueryWrapper<DeskMember> memberQuery = new QueryWrapper<>();
        memberQuery.eq("desk_id", deskId).eq("status", "active");
        List<DeskMember> members = deskMemberMapper.selectList(memberQuery);
        
        for (DeskMember member : members) {
            member.setStatus("inactive");
            deskMemberMapper.updateById(member);
        }

        log.info("小桌解散成功: deskId={}, 影响成员数={}", deskId, members.size());
    }

    /**
     * 搜索小桌
     */
    public Page<DeskResponse> searchDesks(String keyword, int page, int size, String userId) {
        log.info("搜索小桌: keyword={}, page={}, size={}, userId={}", keyword, page, size, userId);

        Page<Desk> deskPage = new Page<>(page, size);
        
        if (StringUtils.hasText(keyword)) {
            // 按关键词搜索
            List<Desk> desks = deskMapper.searchByName(keyword);
            deskPage.setRecords(desks);
            deskPage.setTotal(desks.size());
        } else {
            // 查询所有活跃小桌
            deskPage = deskMapper.findActiveDesks(deskPage);
        }

        // 转换为响应DTO
        List<DeskResponse> responses = deskPage.getRecords().stream()
                .map(desk -> buildDeskResponse(desk, userId))
                .collect(Collectors.toList());

        Page<DeskResponse> responsePage = new Page<>(page, size);
        responsePage.setRecords(responses);
        responsePage.setTotal(deskPage.getTotal());
        responsePage.setPages(deskPage.getPages());

        return responsePage;
    }

    /**
     * 获取我的小桌（包括创建的和加入的）
     */
    public List<DeskResponse> getMyDesks(String userId) {
        log.info("获取我的小桌: userId={}", userId);

        List<Desk> desks = deskMapper.findDesksByUserId(userId);
        
        return desks.stream()
                .map(desk -> buildDeskResponse(desk, userId))
                .collect(Collectors.toList());
    }

    /**
     * 获取小桌统计信息
     */
    public Map<String, Object> getDeskStatistics() {
        log.info("获取小桌统计信息");
        return deskMapper.getDeskStatistics();
    }

    /**
     * 构建小桌响应DTO
     */
    private DeskResponse buildDeskResponse(Desk desk, String userId) {
        DeskResponse response = new DeskResponse();
        BeanUtils.copyProperties(desk, response);

        // 获取桌长信息
        User owner = userMapper.selectById(desk.getOwnerId());
        if (owner != null) {
            response.setOwnerUsername(owner.getUsername());
            response.setOwnerNickname(owner.getNickname());
        }

        // 计算剩余可加入人数
        response.setAvailableSlots(desk.getAvailableSlots());

        // 设置用户相关状态
        response.setIsOwner(desk.isOwner(userId));
        response.setIsMember(deskMemberMapper.countByDeskIdAndUserId(desk.getId(), userId) > 0);
        response.setHasApplied(deskApplicationMapper.countPendingByDeskIdAndUserId(desk.getId(), userId) > 0);
        response.setCanJoin(desk.canAcceptNewMember() && !response.getIsMember() && !response.getHasApplied());

        // 获取待处理申请数量（只有桌长可以看到）
        if (response.getIsOwner()) {
            response.setPendingApplications(deskApplicationMapper.countPendingByDeskId(desk.getId()));
        }

        return response;
    }
}
