package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.StudyRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 刷题记录表Mapper接口
 * 提供刷题记录数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface StudyRecordMapper extends BaseMapper<StudyRecord> {

    /**
     * 统计用户总学习天数
     */
    @Select("SELECT COUNT(DISTINCT study_date) FROM study_records WHERE user_id = #{userId}")
    Long countDistinctStudyDaysByUserId(@Param("userId") String userId);

    /**
     * 统计用户总题目数
     */
    @Select("SELECT COALESCE(SUM(question_count), 0) FROM study_records WHERE user_id = #{userId}")
    Long sumQuestionCountByUserId(@Param("userId") String userId);

    /**
     * 统计用户总正确数
     */
    @Select("SELECT COALESCE(SUM(correct_count), 0) FROM study_records WHERE user_id = #{userId}")
    Long sumCorrectCountByUserId(@Param("userId") String userId);

    /**
     * 统计用户总学习时长
     */
    @Select("SELECT COALESCE(SUM(study_duration), 0) FROM study_records WHERE user_id = #{userId}")
    Long sumStudyDurationByUserId(@Param("userId") String userId);

    /**
     * 获取用户最后学习日期
     */
    @Select("SELECT MAX(study_date) FROM study_records WHERE user_id = #{userId}")
    LocalDate findLastStudyDateByUserId(@Param("userId") String userId);

    /**
     * 获取用户指定日期范围内的学习记录
     */
    @Select("SELECT * FROM study_records WHERE user_id = #{userId} " +
            "AND study_date BETWEEN #{startDate} AND #{endDate} " +
            "ORDER BY study_date ASC")
    List<StudyRecord> findByUserIdAndDateRange(@Param("userId") String userId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    /**
     * 按模块类型统计用户学习数据
     */
    @Select("SELECT module_type, COUNT(*) as record_count, " +
            "SUM(question_count) as total_questions, " +
            "SUM(correct_count) as total_correct, " +
            "SUM(study_duration) as total_duration " +
            "FROM study_records WHERE user_id = #{userId} " +
            "AND (#{startDate} IS NULL OR study_date >= #{startDate}) " +
            "AND (#{endDate} IS NULL OR study_date <= #{endDate}) " +
            "GROUP BY module_type")
    List<Map<String, Object>> findModuleStatisticsByUserId(@Param("userId") String userId,
                                                            @Param("startDate") LocalDate startDate,
                                                            @Param("endDate") LocalDate endDate);

    /**
     * 获取用户学习日期列表（用于计算连续天数）
     */
    @Select("SELECT DISTINCT study_date FROM study_records WHERE user_id = #{userId} ORDER BY study_date DESC")
    List<LocalDate> findStudyDatesByUserIdOrderByDateDesc(@Param("userId") String userId);

    /**
     * 检查用户在指定日期是否有学习记录
     */
    @Select("SELECT COUNT(*) > 0 FROM study_records WHERE user_id = #{userId} AND study_date = #{studyDate}")
    boolean existsByUserIdAndStudyDate(@Param("userId") String userId, @Param("studyDate") LocalDate studyDate);

    /**
     * 检查用户在指定日期和模块是否已有记录
     */
    @Select("SELECT COUNT(*) > 0 FROM study_records WHERE user_id = #{userId} " +
            "AND study_date = #{studyDate} AND module_type = #{moduleType}")
    boolean existsByUserIdAndStudyDateAndModuleType(@Param("userId") String userId,
                                                    @Param("studyDate") LocalDate studyDate,
                                                    @Param("moduleType") String moduleType);

    /**
     * 查找用户指定模块的最近学习记录
     */
    @Select("SELECT * FROM study_records WHERE user_id = #{userId} AND module_type = #{moduleType} " +
            "ORDER BY study_date DESC, created_at DESC LIMIT 1")
    StudyRecord findRecentByUserIdAndModuleType(@Param("userId") String userId,
                                                @Param("moduleType") String moduleType);

    /**
     * 按用户统计指定时间范围内的刷题数量排名
     */
    @Select("SELECT user_id, SUM(question_count) as total_score " +
            "FROM study_records " +
            "WHERE study_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY user_id " +
            "HAVING total_score > 0 " +
            "ORDER BY total_score DESC")
    List<Map<String, Object>> getRankingByQuestionCount(@Param("startDate") LocalDate startDate,
                                                         @Param("endDate") LocalDate endDate);

    /**
     * 按用户统计指定时间范围内的正确率排名
     */
    @Select("SELECT user_id, " +
            "ROUND(SUM(correct_count) * 100.0 / SUM(question_count), 2) as total_score " +
            "FROM study_records " +
            "WHERE study_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY user_id " +
            "HAVING SUM(question_count) > 0 " +
            "ORDER BY total_score DESC")
    List<Map<String, Object>> getRankingByAccuracy(@Param("startDate") LocalDate startDate,
                                                    @Param("endDate") LocalDate endDate);

    /**
     * 按用户统计指定时间范围内的学习时长排名
     */
    @Select("SELECT user_id, SUM(study_duration) as total_score " +
            "FROM study_records " +
            "WHERE study_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY user_id " +
            "HAVING total_score > 0 " +
            "ORDER BY total_score DESC")
    List<Map<String, Object>> getRankingByStudyTime(@Param("startDate") LocalDate startDate,
                                                     @Param("endDate") LocalDate endDate);

    /**
     * 按用户统计指定时间范围内的综合排名
     * 综合分数 = 题目数量 * 0.3 + 正确率 * 0.3 + 学习时长 * 0.2 + 学习天数 * 20 * 0.2
     */
    @Select("SELECT user_id, " +
            "ROUND(" +
            "  SUM(question_count) * 0.3 + " +
            "  (SUM(correct_count) * 100.0 / SUM(question_count)) * 0.3 + " +
            "  SUM(study_duration) * 0.2 + " +
            "  COUNT(DISTINCT study_date) * 20 * 0.2" +
            ") as total_score " +
            "FROM study_records " +
            "WHERE study_date BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY user_id " +
            "HAVING SUM(question_count) > 0 " +
            "ORDER BY total_score DESC")
    List<Map<String, Object>> getRankingByComprehensive(@Param("startDate") LocalDate startDate,
                                                         @Param("endDate") LocalDate endDate);
}
