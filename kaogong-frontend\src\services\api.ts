import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { useAuthStore } from '../stores/useAuthStore'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080',
  timeout: 30000, // 增加到30秒，适应文件上传
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加JWT Token
api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 如果是FormData，删除Content-Type让浏览器自动设置
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type']
    }

    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器 - 处理统一响应格式和错误
api.interceptors.response.use(
  (response) => {
    // 检查业务状态码
    if (response.data.code && response.data.code !== 200) {
      const error = new Error(response.data.message || '请求失败');
      (error as any).code = response.data.code;
      return Promise.reject(error);
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 处理 401 未授权错误
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // 尝试刷新 token
        const refreshToken = useAuthStore.getState().refreshToken;
        if (refreshToken) {
          const response = await axios.post('/v1/auth/refresh', {
            refreshToken
          }, {
            baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080'
          });

          const { token: newToken, refreshToken: newRefreshToken } = response.data.data;
          useAuthStore.getState().setTokens(newToken, newRefreshToken);

          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return api.request(originalRequest);
        }
      } catch (refreshError) {
        console.error('Token 刷新失败:', refreshError);
        // 清除认证信息并跳转到登录页
        useAuthStore.getState().logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // 处理网络错误和其他HTTP错误
    if (!error.response) {
      error.message = '网络连接失败，请检查网络设置';
    } else {
      switch (error.response.status) {
        case 400:
          error.message = error.response.data?.message || '请求参数错误';
          break;
        case 403:
          error.message = '权限不足，无法访问该资源';
          break;
        case 404:
          error.message = '请求的资源不存在';
          break;
        case 500:
          error.message = '服务器内部错误，请稍后重试';
          break;
        default:
          error.message = error.response.data?.message || '请求失败';
      }
    }

    console.error('API 请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message
    });

    return Promise.reject(error);
  }
)

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.get(url, config).then(res => res.data.data),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, data, config).then(res => res.data.data),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.put(url, data, config).then(res => res.data.data),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.delete(url, config).then(res => res.data.data),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.patch(url, data, config).then(res => res.data.data),
};

// 导出 axios 实例供特殊情况使用
export default api;