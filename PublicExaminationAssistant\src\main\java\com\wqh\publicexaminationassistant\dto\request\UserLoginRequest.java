package com.wqh.publicexaminationassistant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户登录请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "用户登录请求")
public class UserLoginRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(max = 100, message = "用户名长度不能超过100字符")
    @Schema(description = "用户名或邮箱", example = "testuser")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(max = 50, message = "密码长度不能超过50字符")
    @Schema(description = "密码", example = "password123")
    private String password;
}
