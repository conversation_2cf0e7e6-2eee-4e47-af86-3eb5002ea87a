package com.wqh.publicexaminationassistant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 修改密码请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "修改密码请求")
public class ChangePasswordRequest {

    @NotBlank(message = "旧密码不能为空")
    @Size(max = 50, message = "旧密码长度不能超过50字符")
    @Schema(description = "旧密码", example = "oldpassword123")
    private String oldPassword;

    @NotBlank(message = "新密码不能为空")
    @Size(min = 8, max = 50, message = "新密码长度必须在8-50字符之间")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).+$", message = "新密码必须包含字母和数字")
    @Schema(description = "新密码", example = "newpassword123")
    private String newPassword;
}
