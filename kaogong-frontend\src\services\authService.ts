import { request } from './api';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  ValidateInviteCodeResponse,
  User
} from '../types/auth';

export const authService = {
  /**
   * 用户登录
   */
  async login(data: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await request.post<LoginResponse>('/v1/auth/login', data);
      return response;
    } catch (error: any) {
      console.error('登录失败:', error);
      throw new Error(error.message || '登录失败，请检查用户名和密码');
    }
  },

  /**
   * 用户注册
   */
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    try {
      const response = await request.post<RegisterResponse>('/v1/auth/register', data);
      return response;
    } catch (error: any) {
      console.error('注册失败:', error);
      throw new Error(error.message || '注册失败，请检查输入信息');
    }
  },

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await request.post('/v1/auth/logout');
    } catch (error: any) {
      console.error('登出失败:', error);
      // 即使登出接口失败，也要清除本地存储
    }
  },

  /**
   * 刷新Token
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      const response = await request.post<LoginResponse>('/v1/auth/refresh', {
        refreshToken
      });
      return response;
    } catch (error: any) {
      console.error('Token刷新失败:', error);
      throw new Error(error.message || 'Token刷新失败');
    }
  },

  /**
   * 获取用户个人资料
   */
  async getUserProfile(): Promise<User> {
    try {
      const response = await request.get<User>('/v1/users/profile');
      return response;
    } catch (error: any) {
      console.error('获取用户信息失败:', error);
      throw new Error(error.message || '获取用户信息失败');
    }
  },

  /**
   * 验证邀请码
   */
  async validateInviteCode(code: string): Promise<ValidateInviteCodeResponse> {
    try {
      const response = await request.post<ValidateInviteCodeResponse>(
        '/v1/invite-codes/validate',
        { code }
      );
      return response;
    } catch (error: any) {
      console.error('邀请码验证失败:', error);
      throw new Error(error.message || '邀请码验证失败');
    }
  },

  /**
   * 更新个人资料
   */
  async updateProfile(data: {
    nickname?: string;
    avatarUrl?: string;
    targetPosition?: string;
    phone?: string;
  }): Promise<User> {
    try {
      const response = await request.put<User>('/v1/users/profile', data);
      return response;
    } catch (error: any) {
      console.error('更新个人资料失败:', error);
      throw new Error(error.message || '更新个人资料失败');
    }
  },

  /**
   * 修改密码
   */
  async changePassword(data: {
    oldPassword: string;
    newPassword: string;
  }): Promise<void> {
    try {
      await request.put('/v1/users/password', data);
    } catch (error: any) {
      console.error('修改密码失败:', error);
      throw new Error(error.message || '修改密码失败');
    }
  },

  /**
   * 上传头像
   */
  async uploadAvatar(file: File): Promise<User> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await request.post<User>('/v1/users/avatar', formData);
      return response;
    } catch (error: any) {
      console.error('头像上传失败:', error);
      throw new Error(error.message || '头像上传失败');
    }
  }
};

// 表单验证工具函数
export const validateForm = {
  /**
   * 验证用户名
   */
  username(username: string): string | null {
    if (!username) return '请输入用户名';
    if (username.length < 3) return '用户名至少3个字符';
    if (username.length > 20) return '用户名不能超过20个字符';
    if (!/^[a-zA-Z0-9_]+$/.test(username)) return '用户名只能包含字母、数字和下划线';
    return null;
  },

  /**
   * 验证邮箱
   */
  email(email: string): string | null {
    if (!email) return '请输入邮箱';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return '请输入有效的邮箱地址';
    return null;
  },

  /**
   * 验证密码
   */
  password(password: string): string | null {
    if (!password) return '请输入密码';
    if (password.length < 6) return '密码至少6个字符';
    if (password.length > 50) return '密码不能超过50个字符';
    return null;
  },

  /**
   * 验证确认密码
   */
  confirmPassword(password: string, confirmPassword: string): string | null {
    if (!confirmPassword) return '请确认密码';
    if (password !== confirmPassword) return '两次输入的密码不一致';
    return null;
  },

  /**
   * 验证邀请码
   */
  inviteCode(inviteCode: string): string | null {
    if (!inviteCode) return '请输入邀请码';
    if (inviteCode.length !== 8) return '邀请码应为8位字符';
    return null;
  },

  /**
   * 验证昵称
   */
  nickname(nickname: string): string | null {
    if (nickname && nickname.length > 20) return '昵称不能超过20个字符';
    return null;
  }
};