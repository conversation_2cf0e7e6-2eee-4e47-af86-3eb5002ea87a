/**
 * 文件验证工具函数
 */

export interface FileValidationOptions {
  /** 允许的文件类型前缀，如 ['image/', 'video/'] */
  allowedTypes?: string[];
  /** 最大文件大小（字节） */
  maxSize?: number;
  /** 最小文件大小（字节） */
  minSize?: number;
  /** 允许的文件扩展名，如 ['.jpg', '.png'] */
  allowedExtensions?: string[];
}

export interface FileValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 错误信息 */
  errorMessage?: string;
  /** 文件信息 */
  fileInfo: {
    name: string;
    size: number;
    type: string;
    extension: string;
  };
}

/**
 * 验证文件是否符合要求
 */
export function validateFile(
  file: File,
  options: FileValidationOptions = {}
): FileValidationResult {
  const {
    allowedTypes = ['image/'],
    maxSize = 10 * 1024 * 1024, // 默认10MB
    minSize = 0,
    allowedExtensions = []
  } = options;

  // 获取文件信息
  const fileInfo = {
    name: file.name || '',
    size: file.size || 0,
    type: file.type || '',
    extension: getFileExtension(file.name || '')
  };

  // 检查文件是否存在
  if (!file) {
    return {
      isValid: false,
      errorMessage: '请选择一个文件',
      fileInfo
    };
  }

  // 检查文件名
  if (!fileInfo.name) {
    return {
      isValid: false,
      errorMessage: '文件名无效',
      fileInfo
    };
  }

  // 检查文件大小
  if (fileInfo.size === 0) {
    return {
      isValid: false,
      errorMessage: '文件大小无效',
      fileInfo
    };
  }

  if (fileInfo.size < minSize) {
    return {
      isValid: false,
      errorMessage: `文件大小不能小于 ${formatFileSize(minSize)}`,
      fileInfo
    };
  }

  if (fileInfo.size > maxSize) {
    return {
      isValid: false,
      errorMessage: `文件大小不能超过 ${formatFileSize(maxSize)}`,
      fileInfo
    };
  }

  // 检查文件类型
  if (allowedTypes.length > 0) {
    if (!fileInfo.type) {
      return {
        isValid: false,
        errorMessage: '无法识别文件类型，请选择有效的文件',
        fileInfo
      };
    }

    const isTypeAllowed = allowedTypes.some(allowedType => 
      fileInfo.type.startsWith(allowedType)
    );

    if (!isTypeAllowed) {
      const typeNames = allowedTypes.map(type => type.replace('/', '')).join('、');
      return {
        isValid: false,
        errorMessage: `只能上传 ${typeNames} 类型的文件`,
        fileInfo
      };
    }
  }

  // 检查文件扩展名
  if (allowedExtensions.length > 0) {
    const isExtensionAllowed = allowedExtensions.some(ext => 
      fileInfo.extension.toLowerCase() === ext.toLowerCase()
    );

    if (!isExtensionAllowed) {
      return {
        isValid: false,
        errorMessage: `只能上传 ${allowedExtensions.join('、')} 格式的文件`,
        fileInfo
      };
    }
  }

  return {
    isValid: true,
    fileInfo
  };
}

/**
 * 验证图片文件
 */
export function validateImageFile(
  file: File,
  maxSize: number = 10 * 1024 * 1024 // 默认10MB
): FileValidationResult {
  return validateFile(file, {
    allowedTypes: ['image/'],
    maxSize,
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  });
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查文件是否为图片
 */
export function isImageFile(file: File): boolean {
  const result = validateImageFile(file);
  return result.isValid;
}

/**
 * 获取文件的预览URL（用于图片预览）
 */
export function getFilePreviewUrl(file: File): string | null {
  if (!isImageFile(file)) {
    return null;
  }
  
  try {
    return URL.createObjectURL(file);
  } catch (error) {
    console.error('创建文件预览URL失败:', error);
    return null;
  }
}

/**
 * 释放文件预览URL
 */
export function revokeFilePreviewUrl(url: string): void {
  try {
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('释放文件预览URL失败:', error);
  }
}
