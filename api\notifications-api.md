# 通知系统模块 API 接口文档

## 模块概述
通知系统模块负责系统消息推送、站内信管理和用户通知设置，支持多种通知类型和推送方式。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 通知查询 | 获取通知列表 | `/api/v1/notifications` | GET | `page=1&size=20&type=string&isRead=boolean&sortBy=createdAt&sortOrder=desc` | `{"code": 200, "data": {"records": [...], "total": 50, "unreadCount": 15}}` | user | 个人通知列表 |
| 通知查询 | 获取通知详情 | `/api/v1/notifications/{notificationId}` | GET | 路径参数: `notificationId` | `{"code": 200, "data": {"id": "uuid", "type": "system", "title": "系统通知", "content": "通知内容", "metadata": {...}, "isRead": false, "createdAt": "datetime"}}` | user | 通知详细信息 |
| 通知查询 | 获取未读数量 | `/api/v1/notifications/unread-count` | GET | `type=string` | `{"code": 200, "data": {"total": 15, "byType": {"system": 5, "desk": 8, "reminder": 2}}}` | user | 未读通知统计 |
| 通知查询 | 获取最新通知 | `/api/v1/notifications/latest` | GET | `limit=5&type=string` | `{"code": 200, "data": [{"id": "uuid", "type": "system", "title": "最新通知", "createdAt": "datetime", "isRead": false}]}` | user | 最新通知摘要 |
| 通知操作 | 标记已读 | `/api/v1/notifications/{notificationId}/read` | POST | `{}` | `{"code": 200, "message": "标记成功"}` | user | 标记单个通知已读 |
| 通知操作 | 批量标记已读 | `/api/v1/notifications/batch-read` | POST | `{"notificationIds": ["uuid1", "uuid2"]}` | `{"code": 200, "message": "批量标记成功", "data": {"updated": 2}}` | user | 批量标记已读 |
| 通知操作 | 全部标记已读 | `/api/v1/notifications/read-all` | POST | `{"type": "string?"}` | `{"code": 200, "message": "全部标记成功", "data": {"updated": 15}}` | user | 标记所有通知已读 |
| 通知操作 | 删除通知 | `/api/v1/notifications/{notificationId}` | DELETE | 路径参数: `notificationId` | `{"code": 200, "message": "删除成功"}` | user | 删除单个通知 |
| 通知操作 | 批量删除 | `/api/v1/notifications/batch-delete` | POST | `{"notificationIds": ["uuid1", "uuid2"]}` | `{"code": 200, "message": "批量删除成功", "data": {"deleted": 2}}` | user | 批量删除通知 |
| 通知操作 | 清空通知 | `/api/v1/notifications/clear` | POST | `{"type": "string?", "olderThan": "datetime?"}` | `{"code": 200, "message": "清空成功", "data": {"deleted": 20}}` | user | 清空指定类型通知 |
| 通知设置 | 获取通知设置 | `/api/v1/notifications/settings` | GET | 无 | `{"code": 200, "data": {"system": {"enabled": true, "push": true, "email": false}, "desk": {"enabled": true, "push": true, "email": true}, "reminder": {"enabled": true, "push": true, "email": true}}}` | user | 个人通知设置 |
| 通知设置 | 更新通知设置 | `/api/v1/notifications/settings` | PUT | `{"system": {"enabled": true, "push": true, "email": false}, "desk": {"enabled": true, "push": true, "email": true}}` | `{"code": 200, "message": "设置更新成功"}` | user | 修改通知偏好 |
| 通知设置 | 获取推送设置 | `/api/v1/notifications/push-settings` | GET | 无 | `{"code": 200, "data": {"deviceToken": "string", "platform": "android|ios|web", "enabled": true, "quietHours": {"start": "22:00", "end": "08:00"}}}` | user | 推送相关设置 |
| 通知设置 | 更新推送设置 | `/api/v1/notifications/push-settings` | PUT | `{"deviceToken": "string", "platform": "android|ios|web", "enabled": true, "quietHours": {"start": "22:00", "end": "08:00"}}` | `{"code": 200, "message": "推送设置更新成功"}` | user | 更新推送配置 |
| 系统通知 | 发送系统通知 | `/api/v1/notifications/system` | POST | `{"title": "系统维护通知", "content": "系统将于今晚维护", "type": "system", "targetUsers": ["uuid1", "uuid2"], "priority": "high", "scheduledAt": "datetime?"}` | `{"code": 200, "message": "通知发送成功", "data": {"notificationId": "uuid", "sentCount": 2}}` | admin | 管理员发送系统通知 |
| 系统通知 | 广播通知 | `/api/v1/notifications/broadcast` | POST | `{"title": "全站通知", "content": "重要公告", "type": "announcement", "userFilter": {"reputationLevel": "expert", "region": "北京"}, "priority": "urgent"}` | `{"code": 200, "message": "广播发送成功", "data": {"targetUsers": 500, "sentCount": 500}}` | admin | 广播通知给所有用户 |
| 系统通知 | 定时通知 | `/api/v1/notifications/scheduled` | POST | `{"title": "定时提醒", "content": "考试报名即将截止", "type": "reminder", "targetUsers": ["uuid1"], "scheduledAt": "2024-02-01T09:00:00Z", "repeatType": "none"}` | `{"code": 200, "message": "定时通知创建成功", "data": {"scheduleId": "uuid"}}` | admin | 创建定时通知 |
| 系统通知 | 获取发送历史 | `/api/v1/notifications/send-history` | GET | `page=1&size=20&type=string&startDate=date&endDate=date` | `{"code": 200, "data": {"records": [...], "total": 100}}` | admin | 通知发送历史 |
| 小桌通知 | 发送小桌通知 | `/api/v1/notifications/desk` | POST | `{"deskId": "uuid", "title": "小桌通知", "content": "新成员加入", "type": "desk_member", "excludeUsers": ["uuid1"]}` | `{"code": 200, "message": "小桌通知发送成功", "data": {"sentCount": 7}}` | owner | 桌长发送小桌通知 |
| 小桌通知 | 申请通知 | `/api/v1/notifications/desk-application` | POST | `{"deskId": "uuid", "applicantId": "uuid", "type": "application_submitted", "metadata": {"applicationId": "uuid"}}` | `{"code": 200, "message": "申请通知发送成功"}` | system | 系统自动发送申请通知 |
| 小桌通知 | 审核结果通知 | `/api/v1/notifications/application-result` | POST | `{"applicantId": "uuid", "deskId": "uuid", "result": "approved|rejected", "reason": "string?", "metadata": {"deskName": "学霸小桌"}}` | `{"code": 200, "message": "审核结果通知发送成功"}` | system | 申请审核结果通知 |
| 提醒通知 | 考试提醒 | `/api/v1/notifications/exam-reminder` | POST | `{"userId": "uuid", "announcementId": "uuid", "reminderType": "registration_deadline", "targetDate": "2024-02-15", "advanceDays": 3}` | `{"code": 200, "message": "考试提醒发送成功"}` | system | 系统发送考试提醒 |
| 提醒通知 | 学习提醒 | `/api/v1/notifications/study-reminder` | POST | `{"userId": "uuid", "reminderType": "daily_goal", "metadata": {"targetQuestions": 100, "currentProgress": 60}}` | `{"code": 200, "message": "学习提醒发送成功"}` | system | 学习目标提醒 |
| 提醒通知 | 活跃度提醒 | `/api/v1/notifications/activity-reminder` | POST | `{"userId": "uuid", "deskId": "uuid", "activityScore": 45, "threshold": 60, "metadata": {"deskName": "学霸小桌"}}` | `{"code": 200, "message": "活跃度提醒发送成功"}` | system | 小桌活跃度提醒 |
| 统计分析 | 获取通知统计 | `/api/v1/notifications/stats` | GET | `period=month&type=string&groupBy=type` | `{"code": 200, "data": {"totalSent": 10000, "totalRead": 8500, "readRate": 85.0, "byType": [{"type": "system", "sent": 5000, "read": 4500}]}}` | admin | 通知发送统计 |
| 统计分析 | 获取用户统计 | `/api/v1/notifications/user-stats` | GET | `userId=uuid&period=month` | `{"code": 200, "data": {"received": 50, "read": 45, "readRate": 90.0, "averageReadTime": "2.5h", "preferredTypes": ["desk", "reminder"]}}` | admin | 用户通知行为统计 |
| 统计分析 | 获取推送统计 | `/api/v1/notifications/push-stats` | GET | `period=week&platform=android` | `{"code": 200, "data": {"totalPush": 5000, "delivered": 4800, "opened": 2400, "deliveryRate": 96.0, "openRate": 50.0}}` | admin | 推送效果统计 |

## 数据模型

### 通知模型 (Notification)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "type": "system|desk|reminder|announcement",
  "title": "系统通知",
  "content": "通知的详细内容",
  "metadata": {
    "deskId": "uuid",
    "deskName": "学霸小桌",
    "actionUrl": "/desks/uuid",
    "priority": "high"
  },
  "isRead": false,
  "readAt": null,
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### 通知设置模型 (NotificationSettings)
```json
{
  "userId": "uuid",
  "system": {
    "enabled": true,
    "push": true,
    "email": false
  },
  "desk": {
    "enabled": true,
    "push": true,
    "email": true,
    "memberJoin": true,
    "memberLeave": true,
    "applicationReceived": true
  },
  "reminder": {
    "enabled": true,
    "push": true,
    "email": true,
    "examReminder": true,
    "studyReminder": true,
    "activityReminder": true
  },
  "quietHours": {
    "enabled": true,
    "start": "22:00",
    "end": "08:00"
  }
}
```

### 推送设置模型 (PushSettings)
```json
{
  "userId": "uuid",
  "deviceToken": "device_token_string",
  "platform": "android|ios|web",
  "enabled": true,
  "appVersion": "1.0.0",
  "lastActiveAt": "datetime",
  "quietHours": {
    "start": "22:00",
    "end": "08:00"
  }
}
```

## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|---------|------|
| 5001 | 通知不存在 | 请求的通知ID无效 |
| 5002 | 通知已读 | 通知已经标记为已读 |
| 5003 | 推送设备未注册 | 设备Token无效或未注册 |
| 5004 | 通知类型不支持 | 不支持的通知类型 |
| 5005 | 目标用户不存在 | 通知目标用户无效 |
| 5006 | 小桌权限不足 | 没有权限发送小桌通知 |
| 5007 | 定时通知时间无效 | 定时通知时间不能是过去时间 |
| 5008 | 通知内容为空 | 通知标题或内容不能为空 |

## 业务规则

### 通知类型
1. **system**: 系统通知（维护、更新等）
2. **desk**: 小桌相关通知（成员变动、申请等）
3. **reminder**: 提醒通知（考试、学习、活跃度）
4. **announcement**: 公告通知（新公告发布）

### 推送规则
1. **静默时间**: 用户设置的静默时间内不推送
2. **频率限制**: 同类型通知1小时内最多推送3次
3. **优先级**: 紧急通知可以突破静默时间限制
4. **设备管理**: 支持多设备推送和设备管理

### 通知保留
1. **系统通知**: 保留30天
2. **小桌通知**: 保留15天
3. **提醒通知**: 保留7天
4. **已读通知**: 保留时间减半

### 权限控制
1. **个人通知**: 只能查看和操作自己的通知
2. **系统通知**: 只有管理员可以发送
3. **小桌通知**: 只有桌长可以发送小桌通知
4. **批量操作**: 有数量限制，单次最多100条
