import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Select,
  Space,
  Typography,
  Tag,
  Pagination,
  Toast,
  Spin,
  Empty,
  Modal,
  Divider,
  Tooltip,
  Form,
  Input,
  TextArea
} from '@douyinfe/semi-ui';
import {
  IconRefresh,
  IconFilter,
  IconBookStroked,
  IconTickCircle,
  IconClock,
  IconCrossCircleStroked,
  IconPlus,
  IconListView
} from '@douyinfe/semi-icons';
import { studyService, WrongQuestionResponse, PageResult, WrongQuestionRequest, imageService } from '../services/studyService';
import Navigation from '../components/Navigation';
import ImageUpload, { ImageInfo as UploadImageInfo } from '../components/ImageUpload';
import ImagePreview from '../components/ImagePreview';
import { useNavigate } from 'react-router-dom';
import '../styles/wrong-questions.css';
import '../styles/image-upload.css';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 动态图标组件
const DynamicIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => {
  const [IconComponent, setIconComponent] = React.useState<React.ComponentType<any> | null>(null);

  React.useEffect(() => {
    const loadIcon = async () => {
      try {
        const iconModule = await import('@douyinfe/semi-icons');
        const component = (iconModule as any)[name];
        if (component) {
          setIconComponent(() => component);
        }
      } catch (error) {
        console.error(`Failed to load icon: ${name}`, error);
      }
    };

    loadIcon();
  }, [name]);

  if (!IconComponent) {
    return null;
  }

  return React.createElement(IconComponent, props);
};

// 学习模块映射 - 与系统其他页面保持一致
const MODULE_TYPE_MAP = {
  'math': '数学运算',
  'logic': '逻辑推理',
  'language': '言语理解',
  'knowledge': '常识判断',
  'essay': '申论写作'
};

// 题目类型映射
const QUESTION_TYPE_MAP = {
  single_choice: '单选题',
  multiple_choice: '多选题',
  judgment: '判断题',
  fill_blank: '填空题',
  essay: '论述题'
};

// 掌握状态映射
const MASTERY_STATUS_MAP = {
  not_mastered: { text: '未掌握', color: 'red', icon: <IconCrossCircleStroked /> },
  reviewing: { text: '复习中', color: 'orange', icon: <IconClock /> },
  mastered: { text: '已掌握', color: 'green', icon: <IconTickCircle /> }
};

// 难度等级映射
const DIFFICULTY_LEVEL_MAP = {
  easy: { text: '简单', color: 'green' },
  medium: { text: '中等', color: 'orange' },
  hard: { text: '困难', color: 'red' }
};

const WrongQuestions: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [wrongQuestions, setWrongQuestions] = useState<PageResult<WrongQuestionResponse>>({
    records: [],
    total: 0,
    current: 1,
    size: 20,
    pages: 0
  });
  
  // 筛选参数
  const [filters, setFilters] = useState({
    page: 1,
    size: 20,
    masteryStatus: '',
    moduleType: ''
  });

  // 详情模态框
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<WrongQuestionResponse | null>(null);

  // 新建错题模态框
  const [createVisible, setCreateVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [formApi, setFormApi] = useState<any>(null);

  // 图片上传相关状态
  const [questionImages, setQuestionImages] = useState<UploadImageInfo[]>([]);
  const [answerImages, setAnswerImages] = useState<UploadImageInfo[]>([]);
  const [explanationImages, setExplanationImages] = useState<UploadImageInfo[]>([]);

  // 获取错题列表
  const fetchWrongQuestions = async () => {
    try {
      setLoading(true);
      const params = {
        ...filters,
        masteryStatus: filters.masteryStatus || undefined,
        moduleType: filters.moduleType || undefined
      };
      
      const result = await studyService.getWrongQuestions(params);
      setWrongQuestions(result);
    } catch (error: any) {
      console.error('获取错题列表失败:', error);
      Toast.error(error.message || '获取错题列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新掌握状态
  const updateMasteryStatus = async (questionId: string, masteryStatus: string) => {
    try {
      await studyService.updateMasteryStatus(questionId, { masteryStatus });
      Toast.success('状态更新成功');
      fetchWrongQuestions(); // 刷新列表
    } catch (error: any) {
      console.error('更新状态失败:', error);
      Toast.error(error.message || '更新状态失败');
    }
  };

  // 处理筛选变化
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // 重置到第一页
    }));
  };

  // 重置所有筛选器
  const handleResetFilters = () => {
    setFilters({
      page: 1,
      size: 20,
      masteryStatus: '',
      moduleType: ''
    });
    Toast.success('筛选条件已重置');
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    setFilters(prev => ({
      ...prev,
      page,
      size: size || prev.size
    }));
  };

  // 显示详情
  const showDetail = async (question: WrongQuestionResponse) => {
    try {
      setLoading(true);

      // 并行执行：获取详情数据 + 增加复习次数
      const [detailData] = await Promise.allSettled([
        studyService.getWrongQuestionDetail(question.id),
        studyService.incrementReviewCount(question.id)
      ]);

      if (detailData.status === 'fulfilled') {
        // 获取详情成功，使用最新数据
        const updatedQuestion = detailData.value;
        setSelectedQuestion(updatedQuestion);

        // 同步更新列表中的复习次数（避免需要刷新页面）
        setWrongQuestions(prev => ({
          ...prev,
          records: prev.records.map(item =>
            item.id === question.id
              ? { ...item, reviewCount: (item.reviewCount || 0) + 1 }
              : item
          )
        }));
      } else {
        // 获取详情失败，使用列表数据
        console.error('获取错题详情失败:', detailData.reason);
        setSelectedQuestion(question);
      }

      setDetailVisible(true);
    } catch (error: any) {
      console.error('显示错题详情失败:', error);
      Toast.error(error.message || '显示错题详情失败');
      // 如果所有操作都失败，仍然显示列表数据
      setSelectedQuestion(question);
      setDetailVisible(true);
    } finally {
      setLoading(false);
    }
  };

  // 删除错题
  const handleDeleteQuestion = (question: WrongQuestionResponse) => {
    Modal.confirm({
      title: '确认删除',
      content: (
        <div>
          <p>确定要删除这道错题吗？</p>
          <p style={{ color: 'var(--semi-color-warning)', fontSize: '14px' }}>
            删除后将无法恢复，同时会删除关联的所有图片文件。
          </p>
          <div style={{
            marginTop: '12px',
            padding: '8px',
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '4px',
            fontSize: '12px'
          }}>
            <strong>题目：</strong>{question.questionContent.length > 50
              ? question.questionContent.substring(0, 50) + '...'
              : question.questionContent}
          </div>
        </div>
      ),
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await studyService.deleteWrongQuestion(question.id);
          Toast.success('错题删除成功');
          fetchWrongQuestions(); // 刷新列表
        } catch (error: any) {
          console.error('删除错题失败:', error);
          Toast.error(error.message || '删除错题失败');
        }
      }
    });
  };

  // 新建错题
  const handleCreateWrongQuestion = () => {
    setCreateVisible(true);
  };

  // 提交创建错题
  const handleSubmitCreate = async (values: any) => {
    try {
      setCreateLoading(true);

      // 如果选择了学习模块，尝试关联到最近的学习记录
      let studyRecordId = undefined;
      if (values.moduleType) {
        try {
          // 这里可以调用API获取该模块最近的学习记录ID
          // 暂时留空，让后端处理
        } catch (error) {
          console.warn('获取学习记录失败，将创建独立错题');
        }
      }

      const requestData: WrongQuestionRequest = {
        studyRecordId: studyRecordId,
        moduleType: values.moduleType,
        questionType: values.questionType,
        difficultyLevel: values.difficultyLevel,
        questionContent: values.questionContent,
        userAnswer: values.userAnswer || '',
        correctAnswer: values.correctAnswer,
        explanation: values.explanation || '',
        masteryStatus: 'not_mastered'
      };

      // 1. 先创建错题
      const createdQuestion = await studyService.createWrongQuestion(requestData);

      // 2. 上传图片（如果有的话）
      const allImages = [
        ...questionImages.map(img => ({ ...img, imageType: 'question' as const })),
        ...answerImages.map(img => ({ ...img, imageType: 'answer' as const })),
        ...explanationImages.map(img => ({ ...img, imageType: 'explanation' as const }))
      ];

      if (allImages.length > 0) {
        const uploadPromises = allImages
          .filter(img => img.file) // 只上传有文件的图片
          .map((img, index) =>
            imageService.uploadWrongQuestionImage(
              createdQuestion.id,
              img.file!,
              img.imageType,
              img.sortOrder || index
            )
          );

        try {
          await Promise.all(uploadPromises);
          Toast.success(`错题和图片创建成功！已添加到【${MODULE_TYPE_MAP[values.moduleType as keyof typeof MODULE_TYPE_MAP]}】模块`);
        } catch (uploadError: any) {
          console.warn('图片上传部分失败:', uploadError);
          Toast.warning('错题创建成功，但部分图片上传失败');
        }
      } else {
        Toast.success(`错题创建成功！已添加到【${MODULE_TYPE_MAP[values.moduleType as keyof typeof MODULE_TYPE_MAP]}】模块`);
      }

      // 3. 清理状态并刷新列表
      setCreateVisible(false);
      resetCreateForm();
      fetchWrongQuestions();
    } catch (error: any) {
      console.error('创建错题失败:', error);
      Toast.error(error.message || '创建错题失败');
    } finally {
      setCreateLoading(false);
    }
  };

  // 重置创建表单
  const resetCreateForm = () => {
    if (formApi) {
      formApi.reset();
    }
    setQuestionImages([]);
    setAnswerImages([]);
    setExplanationImages([]);
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchWrongQuestions();
  }, [filters]);

  // 表格列定义
  const columns = [
    {
      title: '学习模块',
      dataIndex: 'moduleType',
      key: 'moduleType',
      width: 120,
      render: (moduleType: string, record: any) => {
        // 优先级：直接的moduleType > studyRecord.moduleType > 未分类
        let effectiveModuleType = moduleType;
        let effectiveModuleName = record.moduleName;

        if (!effectiveModuleType && record.studyRecord && record.studyRecord.moduleType) {
          effectiveModuleType = record.studyRecord.moduleType;
          effectiveModuleName = record.studyRecord.moduleName;
        }

        if (effectiveModuleType) {
          return (
            <Tag color="cyan" size="small">
              {effectiveModuleName || MODULE_TYPE_MAP[effectiveModuleType as keyof typeof MODULE_TYPE_MAP] || effectiveModuleType}
            </Tag>
          );
        }

        return <Text type="tertiary" size="small">未分类</Text>;
      }
    },
    {
      title: '难度',
      dataIndex: 'difficultyLevel',
      key: 'difficultyLevel',
      width: 80,
      render: (difficultyLevel: string) => {
        const difficulty = DIFFICULTY_LEVEL_MAP[difficultyLevel as keyof typeof DIFFICULTY_LEVEL_MAP];
        return (
          <Tag color={difficulty?.color || 'grey'} size="small">
            {difficulty?.text || difficultyLevel}
          </Tag>
        );
      }
    },
    {
      title: '题目内容',
      dataIndex: 'questionContent',
      key: 'questionContent',
      width: 250, // 固定宽度，防止过度扩展
      ellipsis: {
        showTitle: false, // 禁用默认title，使用自定义Tooltip
      },
      render: (content: string) => (
        <Tooltip content={content} position="topLeft">
          <div style={{
            maxWidth: '230px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            cursor: 'pointer'
          }}>
            {content || '暂无内容'}
          </div>
        </Tooltip>
      )
    },
    {
      title: '掌握状态',
      dataIndex: 'masteryStatus',
      key: 'masteryStatus',
      width: 140, // 增加宽度以容纳完整的状态文字
      render: (masteryStatus: string, record: WrongQuestionResponse) => {
        const status = MASTERY_STATUS_MAP[masteryStatus as keyof typeof MASTERY_STATUS_MAP];
        return (
          <Select
            value={masteryStatus}
            size="small"
            style={{ width: 120 }} // 增加Select宽度
            onChange={(value) => updateMasteryStatus(record.id, value as string)}
            optionList={Object.entries(MASTERY_STATUS_MAP).map(([key, value]) => ({
              value: key,
              label: (
                <Space size="small">
                  {value.icon}
                  <span>{value.text}</span>
                </Space>
              )
            }))}
          />
        );
      }
    },
    {
      title: '复习次数',
      dataIndex: 'reviewCount',
      key: 'reviewCount',
      width: 80,
      render: (count: number) => (
        <Text type="secondary">{count || 0}次</Text>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (createdAt: string) => (
        <Text type="secondary" size="small">
          {new Date(createdAt).toLocaleDateString()}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 100, // 减少宽度，使用图标按钮
      fixed: 'right', // 固定在右侧
      render: (_: any, record: WrongQuestionResponse) => (
        <Space size="small">
          <Tooltip content="查看详情">
            <Button
              type="tertiary"
              theme="borderless"
              size="small"
              icon={<DynamicIcon name="IconEyeOpened" />}
              onClick={() => showDetail(record)}
              style={{ padding: '4px' }}
            />
          </Tooltip>
          <Tooltip content="删除错题">
            <Button
              type="danger"
              theme="borderless"
              size="small"
              icon={<DynamicIcon name="IconDelete" />}
              onClick={() => handleDeleteQuestion(record)}
              style={{ padding: '4px' }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="wrong-questions-page">
      <Navigation />
      
      <div className="wrong-questions-container">
        <div className="wrong-questions-header">
          <div className="header-title">
            <IconBookStroked size="large" />
            <Title level={2} style={{ margin: 0 }}>错题本</Title>
            <Text type="secondary">管理和复习你的错题</Text>
          </div>

          <Space>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleCreateWrongQuestion}
              style={{
                borderRadius: '20px',
                border: '2px solid var(--ink-dark)',
                transform: 'rotate(-1deg)',
                transition: 'all 0.3s ease',
                fontFamily: 'var(--font-handwritten)',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'rotate(0deg) translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'rotate(-1deg)';
              }}
            >
              新建错题
            </Button>
            <Button
              icon={<IconRefresh />}
              onClick={fetchWrongQuestions}
              loading={loading}
              style={{
                borderRadius: '20px',
                border: '2px solid var(--ink-dark)',
                transform: 'rotate(1deg)',
                transition: 'all 0.3s ease',
                fontFamily: 'var(--font-handwritten)',
                fontWeight: '500'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'rotate(0deg) translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'rotate(1deg)';
              }}
            >
              刷新
            </Button>
          </Space>
        </div>

        {/* 筛选器 */}
        <Card className="filter-card">
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            alignItems: 'end'
          }}>
            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                学习模块
              </Text>
              <Select
                placeholder="选择学习模块"
                value={filters.moduleType || 'all'}
                onChange={(value) => handleFilterChange('moduleType', value === 'all' ? '' : value)}
                style={{ width: '100%' }}
              >
                <Option key="all" value="all">
                  <Space>
                    <IconListView />
                    全部模块
                  </Space>
                </Option>
                {Object.entries(MODULE_TYPE_MAP).map(([key, value]) => (
                  <Option key={key} value={key}>{value}</Option>
                ))}
              </Select>
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                掌握状态
              </Text>
              <Select
                placeholder="选择掌握状态"
                value={filters.masteryStatus || 'all'}
                onChange={(value) => handleFilterChange('masteryStatus', value === 'all' ? '' : value)}
                style={{ width: '100%' }}
              >
                <Option key="all" value="all">
                  <Space>
                    <IconListView />
                    全部状态
                  </Space>
                </Option>
                {Object.entries(MASTERY_STATUS_MAP).map(([key, value]) => (
                  <Option key={key} value={key}>
                    <Space>
                      {value.icon}
                      {value.text}
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                快速筛选
              </Text>
              <Space wrap>
                <Button
                  size="small"
                  type={!filters.masteryStatus ? 'primary' : 'tertiary'}
                  icon={<IconListView />}
                  onClick={() => handleFilterChange('masteryStatus', '')}
                >
                  全部
                </Button>
                <Button
                  size="small"
                  type={filters.masteryStatus === 'not_mastered' ? 'primary' : 'tertiary'}
                  icon={<IconCrossCircleStroked />}
                  onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'not_mastered' ? '' : 'not_mastered')}
                >
                  未掌握
                </Button>
                <Button
                  size="small"
                  type={filters.masteryStatus === 'reviewing' ? 'primary' : 'tertiary'}
                  icon={<IconClock />}
                  onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'reviewing' ? '' : 'reviewing')}
                >
                  复习中
                </Button>
                <Button
                  size="small"
                  type={filters.masteryStatus === 'mastered' ? 'primary' : 'tertiary'}
                  icon={<IconTickCircle />}
                  onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'mastered' ? '' : 'mastered')}
                >
                  已掌握
                </Button>
              </Space>
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                操作
              </Text>
              <Button
                size="small"
                type="tertiary"
                icon={<IconRefresh />}
                onClick={handleResetFilters}
                style={{ width: '100%' }}
              >
                重置筛选
              </Button>
            </div>
          </div>
        </Card>

        {/* 错题列表 */}
        <Card className="questions-card">
          <Spin spinning={loading}>
            {wrongQuestions.records.length > 0 ? (
              <>
                <Table
                  columns={columns}
                  dataSource={wrongQuestions.records}
                  pagination={false}
                  rowKey="id"
                  size="small"
                  scroll={{
                    x: 'max-content', // 水平滚动，确保在小屏幕上可用
                    y: 600 // 垂直滚动，限制表格高度
                  }}
                  style={{
                    minWidth: '800px' // 最小宽度确保基本可读性
                  }}
                />
                
                <div className="pagination-container">
                  <Pagination
                    total={wrongQuestions.total}
                    current={filters.page}
                    pageSize={filters.size}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                    onChange={handlePageChange}
                    onPageSizeChange={(size) => handleFilterChange('size', size)}
                  />
                </div>
              </>
            ) : (
              <Empty
                image={<IconBookStroked size="extra-large" />}
                title="暂无错题"
                description="开始刷题后，错题会自动记录在这里"
              />
            )}
          </Spin>
        </Card>
      </div>

      {/* 详情模态框 */}
      <Modal
        title="错题详情"
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
        className="question-detail-modal"
      >
        {selectedQuestion && (
          <div className="question-detail">
            <div className="detail-header">
              <Space>
                {/* 学习模块标签 */}
                {(() => {
                  // 优先级：直接的moduleType > studyRecord.moduleType > 未分类
                  let effectiveModuleType = selectedQuestion.moduleType;
                  let effectiveModuleName = selectedQuestion.moduleName;

                  if (!effectiveModuleType && selectedQuestion.studyRecord && selectedQuestion.studyRecord.moduleType) {
                    effectiveModuleType = selectedQuestion.studyRecord.moduleType;
                    effectiveModuleName = selectedQuestion.studyRecord.moduleName;
                  }

                  if (effectiveModuleType) {
                    return (
                      <Tag color="cyan">
                        {effectiveModuleName || MODULE_TYPE_MAP[effectiveModuleType as keyof typeof MODULE_TYPE_MAP] || effectiveModuleType}
                      </Tag>
                    );
                  }

                  return <Tag color="default">未分类</Tag>;
                })()}

                <Tag color="blue">
                  {QUESTION_TYPE_MAP[selectedQuestion.questionType as keyof typeof QUESTION_TYPE_MAP]}
                </Tag>
                <Tag color={DIFFICULTY_LEVEL_MAP[selectedQuestion.difficultyLevel as keyof typeof DIFFICULTY_LEVEL_MAP]?.color}>
                  {DIFFICULTY_LEVEL_MAP[selectedQuestion.difficultyLevel as keyof typeof DIFFICULTY_LEVEL_MAP]?.text}
                </Tag>
                <Tag color={MASTERY_STATUS_MAP[selectedQuestion.masteryStatus as keyof typeof MASTERY_STATUS_MAP]?.color}>
                  {MASTERY_STATUS_MAP[selectedQuestion.masteryStatus as keyof typeof MASTERY_STATUS_MAP]?.text}
                </Tag>
              </Space>
            </div>

            <Divider />

            <div className="detail-content">
              <div className="detail-section">
                <Text strong>题目内容：</Text>
                <Paragraph>{selectedQuestion.questionContent}</Paragraph>
              </div>

              {selectedQuestion.userAnswer && (
                <div className="detail-section">
                  <Text strong>我的答案：</Text>
                  <Paragraph type="danger">{selectedQuestion.userAnswer}</Paragraph>
                </div>
              )}

              <div className="detail-section">
                <Text strong>正确答案：</Text>
                <Paragraph type="success">{selectedQuestion.correctAnswer}</Paragraph>
              </div>

              {selectedQuestion.explanation && (
                <div className="detail-section">
                  <Text strong>解析：</Text>
                  <Paragraph>{selectedQuestion.explanation}</Paragraph>
                </div>
              )}

              {/* 图片显示区域 */}
              <div className="detail-section">
                <Divider />
                <Text strong style={{ marginBottom: '12px', display: 'block' }}>相关图片：</Text>
                {selectedQuestion.images && selectedQuestion.images.length > 0 ? (
                  <ImagePreview
                    images={selectedQuestion.images}
                    editable={false}
                    onDelete={undefined} // 详情页面不允许删除
                  />
                ) : (
                  <div style={{
                    textAlign: 'center',
                    padding: '24px',
                    backgroundColor: 'var(--semi-color-fill-0)',
                    borderRadius: '6px',
                    border: '1px dashed var(--semi-color-border)'
                  }}>
                    <Text type="tertiary">暂无图片</Text>
                  </div>
                )}
              </div>

              <div className="detail-section">
                <Space>
                  <Text type="secondary">复习次数：{selectedQuestion.reviewCount || 0}次</Text>
                  <Text type="secondary">创建时间：{new Date(selectedQuestion.createdAt).toLocaleString()}</Text>
                  {selectedQuestion.reviewedAt && (
                    <Text type="secondary">最后复习：{new Date(selectedQuestion.reviewedAt).toLocaleString()}</Text>
                  )}
                </Space>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 创建错题模态框 */}
      <Modal
        title="新建错题"
        visible={createVisible}
        onCancel={() => {
          setCreateVisible(false);
          resetCreateForm();
        }}
        footer={null}
        width={800}
        className="question-detail-modal"
      >
        <Form
          onSubmit={handleSubmitCreate}
          labelPosition="top"
          style={{ padding: '20px 0' }}
          getFormApi={(api) => setFormApi(api)}
        >
          {/* 第一行：学习模块（突出显示） */}
          <div style={{ marginBottom: '20px' }}>
            <Form.Select
              field="moduleType"
              label={
                <span style={{ fontSize: '16px', fontWeight: 'bold', color: 'var(--accent-blue)' }}>
                  📚 学习模块 *
                </span>
              }
              placeholder="选择错题所属的学习模块"
              rules={[{ required: true, message: '请选择学习模块' }]}
              style={{ width: '100%', height: '45px' }}
            >
              {Object.entries(MODULE_TYPE_MAP).map(([key, value]) => (
                <Form.Select.Option key={key} value={key}>
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>{value}</span>
                </Form.Select.Option>
              ))}
            </Form.Select>
          </div>

          {/* 第二行：题目类型和难度 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
            <Form.Select
              field="questionType"
              label="题目类型"
              placeholder="选择题目类型"
              rules={[{ required: true, message: '请选择题目类型' }]}
              style={{ width: '100%' }}
            >
              {Object.entries(QUESTION_TYPE_MAP).map(([key, value]) => (
                <Form.Select.Option key={key} value={key}>{value}</Form.Select.Option>
              ))}
            </Form.Select>

            <Form.Select
              field="difficultyLevel"
              label="难度等级"
              placeholder="选择难度等级"
              rules={[{ required: true, message: '请选择难度等级' }]}
              style={{ width: '100%' }}
            >
              {Object.entries(DIFFICULTY_LEVEL_MAP).map(([key, value]) => (
                <Form.Select.Option key={key} value={key}>{value.text}</Form.Select.Option>
              ))}
            </Form.Select>
          </div>

          <Form.TextArea
            field="questionContent"
            label="题目内容"
            placeholder="请输入题目内容"
            rules={[{ required: true, message: '请输入题目内容' }]}
            rows={4}
            style={{ marginBottom: '16px' }}
          />

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
            <Form.Input
              field="userAnswer"
              label="我的答案"
              placeholder="请输入你的答案（可选）"
            />

            <Form.Input
              field="correctAnswer"
              label="正确答案"
              placeholder="请输入正确答案"
              rules={[{ required: true, message: '请输入正确答案' }]}
            />
          </div>

          <Form.TextArea
            field="explanation"
            label="解析"
            placeholder="请输入题目解析（可选）"
            rows={3}
            style={{ marginBottom: '24px' }}
          />

          {/* 图片上传区域 */}
          <Divider margin="24px" />
          <div style={{ marginBottom: '24px' }}>
            <Typography.Title heading={6} style={{ marginBottom: '16px' }}>
              图片上传（可选）
            </Typography.Title>

            <Space direction="vertical" spacing="large" style={{ width: '100%' }}>
              {/* 题目图片 */}
              <div>
                <Typography.Text strong style={{ marginBottom: '8px', display: 'block' }}>
                  题目图片
                </Typography.Text>
                <ImageUpload
                  imageType="question"
                  maxCount={5}
                  value={questionImages}
                  onChange={setQuestionImages}
                  disabled={createLoading}
                />
              </div>

              {/* 答案图片 */}
              <div>
                <Typography.Text strong style={{ marginBottom: '8px', display: 'block' }}>
                  答案图片
                </Typography.Text>
                <ImageUpload
                  imageType="answer"
                  maxCount={3}
                  value={answerImages}
                  onChange={setAnswerImages}
                  disabled={createLoading}
                />
              </div>

              {/* 解析图片 */}
              <div>
                <Typography.Text strong style={{ marginBottom: '8px', display: 'block' }}>
                  解析图片
                </Typography.Text>
                <ImageUpload
                  imageType="explanation"
                  maxCount={5}
                  value={explanationImages}
                  onChange={setExplanationImages}
                  disabled={createLoading}
                />
              </div>
            </Space>
          </div>

          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
            <Button
              onClick={() => {
                setCreateVisible(false);
                resetCreateForm();
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={createLoading}
              style={{
                borderRadius: '20px',
                border: '2px solid var(--ink-dark)',
                fontFamily: 'var(--font-handwritten)',
                fontWeight: '500'
              }}
            >
              创建错题
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default WrongQuestions;
