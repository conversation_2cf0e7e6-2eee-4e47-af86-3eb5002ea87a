package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

/**
 * 小桌排行榜响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class DeskRankingResponse {

    /**
     * 排名
     */
    private Integer rank;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 信誉分数
     */
    private Integer reputationScore;

    /**
     * 学习天数
     */
    private Integer studyDays;

    /**
     * 总题目数
     */
    private Integer totalQuestions;

    /**
     * 总正确数
     */
    private Integer totalCorrect;

    /**
     * 正确率（百分比）
     */
    private Double accuracyRate;

    /**
     * 综合得分
     */
    private Double totalScore;

    /**
     * 是否为桌长
     */
    private Boolean isOwner;
}
