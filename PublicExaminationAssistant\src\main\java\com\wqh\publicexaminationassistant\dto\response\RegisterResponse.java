package com.wqh.publicexaminationassistant.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 注册响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "注册响应")
public class RegisterResponse {

    @Schema(description = "用户ID", example = "123e4567-e89b-12d3-a456-426614174000")
    private String userId;

    @Schema(description = "用户名", example = "testuser")
    private String username;
}
