package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小桌申请响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class DeskApplicationResponse {

    /**
     * 申请ID
     */
    private String id;

    /**
     * 小桌ID
     */
    private String deskId;

    /**
     * 小桌名称
     */
    private String deskName;

    /**
     * 小桌描述
     */
    private String deskDescription;

    /**
     * 申请者ID
     */
    private String userId;

    /**
     * 申请者用户名
     */
    private String username;

    /**
     * 申请者昵称
     */
    private String nickname;

    /**
     * 申请者信誉分数
     */
    private Integer reputationScore;

    /**
     * 申请者信誉等级
     */
    private String reputationLevel;

    /**
     * 申请理由
     */
    private String reason;

    /**
     * 学习计划
     */
    private String studyPlan;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 申请时间
     */
    private LocalDateTime appliedAt;

    /**
     * 处理时间
     */
    private LocalDateTime processedAt;

    /**
     * 处理人ID
     */
    private String processedBy;

    /**
     * 是否待处理
     */
    private Boolean isPending;

    /**
     * 是否已通过
     */
    private Boolean isApproved;

    /**
     * 是否已拒绝
     */
    private Boolean isRejected;
}
