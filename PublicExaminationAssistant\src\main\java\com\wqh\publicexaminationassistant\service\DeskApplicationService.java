package com.wqh.publicexaminationassistant.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.ApplyDeskRequest;
import com.wqh.publicexaminationassistant.dto.request.ProcessApplicationRequest;
import com.wqh.publicexaminationassistant.dto.response.DeskApplicationResponse;
import com.wqh.publicexaminationassistant.entity.Desk;
import com.wqh.publicexaminationassistant.entity.DeskApplication;
import com.wqh.publicexaminationassistant.entity.DeskMember;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.mapper.DeskApplicationMapper;
import com.wqh.publicexaminationassistant.mapper.DeskMapper;
import com.wqh.publicexaminationassistant.mapper.DeskMemberMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小桌申请管理服务
 * 提供申请加入、审核处理、自动审核等功能
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class DeskApplicationService {

    private final DeskApplicationMapper deskApplicationMapper;
    private final DeskMapper deskMapper;
    private final DeskMemberMapper deskMemberMapper;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;

    /**
     * 申请加入小桌
     */
    public DeskApplicationResponse applyToJoinDesk(ApplyDeskRequest request, String userId) {
        log.info("申请加入小桌: deskId={}, userId={}", request.getDeskId(), userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(request.getDeskId());
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证小桌状态
        if (!desk.isActive()) {
            throw new BusinessException(ResultCode.CONFLICT, "小桌已停用或解散，无法申请加入");
        }

        // 3. 验证小桌是否已满员
        if (desk.isFull()) {
            throw new BusinessException(ResultCode.DESK_MEMBER_LIMIT, "小桌已满员，无法申请加入");
        }

        // 4. 验证用户是否已是成员
        if (deskMemberMapper.countByDeskIdAndUserId(request.getDeskId(), userId) > 0) {
            throw new BusinessException(ResultCode.ALREADY_DESK_MEMBER, "您已经是该小桌的成员");
        }

        // 5. 验证用户是否已有待处理的申请
        if (deskApplicationMapper.countPendingByDeskIdAndUserId(request.getDeskId(), userId) > 0) {
            throw new BusinessException(ResultCode.CONFLICT, "您已有待处理的申请，请勿重复申请");
        }

        // 6. 验证用户加入的小桌数量限制（最多加入5个）
        Long joinedDesksCount = deskMemberMapper.countActiveDeskssByUserId(userId);
        if (joinedDesksCount >= 5) {
            throw new BusinessException(ResultCode.DESK_MEMBER_LIMIT, "每个用户最多只能加入5个小桌");
        }

        // 7. 创建申请
        DeskApplication application = new DeskApplication();
        application.setDeskId(request.getDeskId());
        application.setUserId(userId);
        application.setReason(request.getReason());
        application.setStudyPlan(request.getStudyPlan());
        application.setStatus("pending");

        int result = deskApplicationMapper.insert(application);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "提交申请失败");
        }

        // 8. 检查是否满足自动审核条件
        if (shouldAutoApprove(application, desk)) {
            log.info("满足自动审核条件，自动通过申请: applicationId={}", application.getId());
            processApplicationInternal(application.getId(), "approved", userId, "系统自动审核通过");
        }

        log.info("申请提交成功: applicationId={}, deskId={}, userId={}", 
                application.getId(), request.getDeskId(), userId);

        return buildApplicationResponse(application);
    }

    /**
     * 处理申请
     */
    public void processApplication(String applicationId, ProcessApplicationRequest request, String operatorId) {
        log.info("处理申请: applicationId={}, action={}, operatorId={}", 
                applicationId, request.getAction(), operatorId);

        processApplicationInternal(applicationId, request.getAction(), operatorId, request.getRemark());
    }

    /**
     * 内部处理申请方法
     */
    private void processApplicationInternal(String applicationId, String action, String operatorId, String remark) {
        // 1. 验证申请是否存在
        DeskApplication application = deskApplicationMapper.selectById(applicationId);
        if (application == null) {
            throw new BusinessException(ResultCode.APPLICATION_NOT_FOUND, "申请不存在");
        }

        // 2. 验证申请状态
        if (!application.canBeProcessed()) {
            throw new BusinessException(ResultCode.APPLICATION_ALREADY_PROCESSED, "申请已被处理，无法重复操作");
        }

        // 3. 验证小桌是否存在
        Desk desk = deskMapper.selectById(application.getDeskId());
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 4. 验证操作者权限（只有桌长可以处理申请）
        if (!desk.isOwner(operatorId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以处理申请");
        }

        // 5. 如果是通过申请，需要额外验证
        if ("approved".equals(action)) {
            // 验证小桌是否还有空位
            if (desk.isFull()) {
                throw new BusinessException(ResultCode.DESK_MEMBER_LIMIT, "小桌已满员，无法通过申请");
            }

            // 验证申请者是否已是成员
            if (deskMemberMapper.countByDeskIdAndUserId(application.getDeskId(), application.getUserId()) > 0) {
                throw new BusinessException(ResultCode.ALREADY_DESK_MEMBER, "申请者已是小桌成员");
            }

            // 添加成员
            addMemberToDesk(application);
        }

        // 6. 更新申请状态
        if ("approved".equals(action)) {
            application.approve(operatorId);
        } else {
            application.reject(operatorId);
        }

        int result = deskApplicationMapper.updateById(application);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "处理申请失败");
        }

        log.info("申请处理成功: applicationId={}, action={}, operatorId={}", 
                applicationId, action, operatorId);
    }

    /**
     * 获取小桌的申请列表
     */
    public List<DeskApplicationResponse> getApplicationsByDesk(String deskId, String userId, String status) {
        log.info("获取小桌申请列表: deskId={}, userId={}, status={}", deskId, userId, status);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证权限（只有桌长可以查看申请列表）
        if (!desk.isOwner(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以查看申请列表");
        }

        // 3. 查询申请列表
        List<Map<String, Object>> applicationMaps;
        if ("pending".equals(status)) {
            applicationMaps = deskApplicationMapper.findPendingApplicationsByDeskId(deskId);
        } else {
            applicationMaps = deskApplicationMapper.findApplicationsByDeskId(deskId);
        }

        // 4. 转换为响应DTO
        return applicationMaps.stream()
                .map(this::buildApplicationResponseFromMap)
                .collect(Collectors.toList());
    }

    /**
     * 获取我的申请历史
     */
    public List<DeskApplicationResponse> getMyApplications(String userId, String status) {
        log.info("获取我的申请历史: userId={}, status={}", userId, status);

        // 查询申请列表
        List<Map<String, Object>> applicationMaps;
        if ("pending".equals(status)) {
            applicationMaps = deskApplicationMapper.findPendingApplicationsByUserId(userId);
        } else {
            applicationMaps = deskApplicationMapper.findApplicationsByUserId(userId);
        }

        // 转换为响应DTO
        return applicationMaps.stream()
                .map(this::buildApplicationResponseFromMap)
                .collect(Collectors.toList());
    }

    /**
     * 取消申请
     */
    public void cancelApplication(String applicationId, String userId) {
        log.info("取消申请: applicationId={}, userId={}", applicationId, userId);

        // 1. 验证申请是否存在
        DeskApplication application = deskApplicationMapper.selectById(applicationId);
        if (application == null) {
            throw new BusinessException(ResultCode.APPLICATION_NOT_FOUND, "申请不存在");
        }

        // 2. 验证申请者
        if (!application.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只能取消自己的申请");
        }

        // 3. 验证申请状态
        if (!application.isPending()) {
            throw new BusinessException(ResultCode.APPLICATION_ALREADY_PROCESSED, "只能取消待处理的申请");
        }

        // 4. 删除申请
        int result = deskApplicationMapper.deleteById(applicationId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "取消申请失败");
        }

        log.info("申请取消成功: applicationId={}, userId={}", applicationId, userId);
    }

    /**
     * 检查是否满足自动审核条件
     */
    private boolean shouldAutoApprove(DeskApplication application, Desk desk) {
        // 如果没有设置自动审核规则，返回false
        if (!StringUtils.hasText(desk.getAutoApproveRules())) {
            return false;
        }

        try {
            // 解析自动审核规则
            JsonNode rules = objectMapper.readTree(desk.getAutoApproveRules());
            
            // 检查是否启用自动审核
            if (!rules.has("autoApprove") || !rules.get("autoApprove").asBoolean()) {
                return false;
            }

            // 获取申请者信息
            User applicant = userMapper.selectById(application.getUserId());
            if (applicant == null) {
                return false;
            }

            // 检查最低信誉分数要求
            if (rules.has("minReputationScore")) {
                int minScore = rules.get("minReputationScore").asInt();
                if (applicant.getReputationScore() < minScore) {
                    return false;
                }
            }

            // 其他自动审核条件可以在这里添加
            
            return true;
        } catch (JsonProcessingException e) {
            log.warn("解析自动审核规则失败: deskId={}, rules={}", desk.getId(), desk.getAutoApproveRules(), e);
            return false;
        }
    }

    /**
     * 添加成员到小桌
     */
    private void addMemberToDesk(DeskApplication application) {
        // 创建成员关系
        DeskMember member = new DeskMember();
        member.setDeskId(application.getDeskId());
        member.setUserId(application.getUserId());
        member.setRole("member");
        member.setStatus("active");
        member.setJoinReason(application.getReason());

        int result = deskMemberMapper.insert(member);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "添加成员失败");
        }

        // 更新小桌成员数量
        int currentMembers = deskMemberMapper.countActiveMembersByDeskId(application.getDeskId());
        deskMapper.updateMemberCount(application.getDeskId(), currentMembers);

        log.info("成员添加成功: deskId={}, userId={}, currentMembers={}", 
                application.getDeskId(), application.getUserId(), currentMembers);
    }

    /**
     * 构建申请响应DTO
     */
    private DeskApplicationResponse buildApplicationResponse(DeskApplication application) {
        DeskApplicationResponse response = new DeskApplicationResponse();
        
        response.setId(application.getId());
        response.setDeskId(application.getDeskId());
        response.setUserId(application.getUserId());
        response.setReason(application.getReason());
        response.setStudyPlan(application.getStudyPlan());
        response.setStatus(application.getStatus());
        response.setAppliedAt(application.getAppliedAt());
        response.setProcessedAt(application.getProcessedAt());
        response.setProcessedBy(application.getProcessedBy());
        
        response.setIsPending(application.isPending());
        response.setIsApproved(application.isApproved());
        response.setIsRejected(application.isRejected());
        
        return response;
    }

    /**
     * 从Map构建申请响应DTO
     */
    private DeskApplicationResponse buildApplicationResponseFromMap(Map<String, Object> applicationMap) {
        DeskApplicationResponse response = new DeskApplicationResponse();
        
        response.setId((String) applicationMap.get("id"));
        response.setDeskId((String) applicationMap.get("desk_id"));
        response.setDeskName((String) applicationMap.get("desk_name"));
        response.setDeskDescription((String) applicationMap.get("description"));
        response.setUserId((String) applicationMap.get("user_id"));
        response.setUsername((String) applicationMap.get("username"));
        response.setNickname((String) applicationMap.get("nickname"));
        response.setReputationScore((Integer) applicationMap.get("reputation_score"));
        response.setReputationLevel((String) applicationMap.get("reputation_level"));
        response.setReason((String) applicationMap.get("reason"));
        response.setStudyPlan((String) applicationMap.get("study_plan"));
        response.setStatus((String) applicationMap.get("status"));
        // 处理时间字段的类型转换
        Object appliedAtObj = applicationMap.get("applied_at");
        if (appliedAtObj instanceof java.sql.Timestamp) {
            response.setAppliedAt(((java.sql.Timestamp) appliedAtObj).toLocalDateTime());
        } else if (appliedAtObj instanceof LocalDateTime) {
            response.setAppliedAt((LocalDateTime) appliedAtObj);
        }

        Object processedAtObj = applicationMap.get("processed_at");
        if (processedAtObj instanceof java.sql.Timestamp) {
            response.setProcessedAt(((java.sql.Timestamp) processedAtObj).toLocalDateTime());
        } else if (processedAtObj instanceof LocalDateTime) {
            response.setProcessedAt((LocalDateTime) processedAtObj);
        }
        response.setProcessedBy((String) applicationMap.get("processed_by"));
        
        String status = response.getStatus();
        response.setIsPending("pending".equals(status));
        response.setIsApproved("approved".equals(status));
        response.setIsRejected("rejected".equals(status));
        
        return response;
    }
}
