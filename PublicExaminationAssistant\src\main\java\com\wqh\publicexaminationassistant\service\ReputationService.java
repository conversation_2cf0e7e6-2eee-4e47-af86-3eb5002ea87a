package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.entity.ReputationLog;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import com.wqh.publicexaminationassistant.mapper.ReputationLogMapper;
import com.wqh.publicexaminationassistant.mapper.UserReputationStatsMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 信誉管理服务
 * 负责用户信誉分数的增减、等级计算和限制检查
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Transactional
@Slf4j
public class ReputationService {

    @Autowired
    private ReputationLogMapper reputationLogMapper;
    
    @Autowired
    private UserReputationStatsMapper userReputationStatsMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserProtectionService userProtectionService;

    // 扣分限制常量
    private static final int DAILY_DEDUCT_LIMIT = 15;
    private static final int WEEKLY_DEDUCT_LIMIT = 50;
    private static final int MONTHLY_DEDUCT_LIMIT = 100;

    /**
     * 增加信誉分数
     * 
     * @param userId 用户ID
     * @param points 增加的分数
     * @param reason 增加原因
     * @param category 分类
     */
    public void addPoints(String userId, int points, String reason, String category) {
        addPoints(userId, points, reason, category, null, 0);
    }

    /**
     * 增加信誉分数（完整版本）
     * 
     * @param userId 用户ID
     * @param points 增加的分数
     * @param reason 增加原因
     * @param category 分类
     * @param relatedId 关联ID
     * @param consecutiveDays 连续天数
     */
    public void addPoints(String userId, int points, String reason, String category, 
                         String relatedId, int consecutiveDays) {
        try {
            if (points <= 0) {
                log.warn("尝试为用户 {} 增加非正数分数: {}", userId, points);
                return;
            }

            // 记录分数变更日志
            ReputationLog log = new ReputationLog();
            log.setUserId(userId);
            log.setChangeType(ReputationLog.ChangeType.EARN);
            log.setPoints(points);
            log.setReason(reason);
            log.setCategory(category);
            log.setRelatedId(relatedId);
            log.setConsecutiveDays(consecutiveDays);
            log.setProcessedBy(ReputationLog.ProcessedBy.SYSTEM);
            reputationLogMapper.insert(log);

            // 更新用户信誉统计
            updateUserReputationStats(userId, points, true);



        } catch (Exception e) {
            log.error("为用户 {} 增加分数失败", userId, e);
            throw new RuntimeException("增加信誉分数失败", e);
        }
    }

    /**
     * 扣除信誉分数
     * 
     * @param userId 用户ID
     * @param points 扣除的分数
     * @param reason 扣除原因
     * @param category 分类
     */
    public void deductPoints(String userId, int points, String reason, String category) {
        deductPoints(userId, points, reason, category, null, 0);
    }

    /**
     * 扣除信誉分数（完整版本）
     * 
     * @param userId 用户ID
     * @param points 扣除的分数
     * @param reason 扣除原因
     * @param category 分类
     * @param relatedId 关联ID
     * @param consecutiveDays 连续天数
     */
    public void deductPoints(String userId, int points, String reason, String category,
                           String relatedId, int consecutiveDays) {
        try {
            if (points <= 0) {
                log.warn("尝试为用户 {} 扣除非正数分数: {}", userId, points);
                return;
            }

            // 检查保护期
            if (userProtectionService.isInProtectionPeriod(userId)) {
                log.info("用户 {} 在保护期内，跳过扣分：{}", userId, reason);
                return;
            }

            // 检查扣分限制
            if (!canDeductPoints(userId, points)) {
                log.warn("用户 {} 达到扣分上限，跳过扣分：{}", userId, reason);
                return;
            }

            // 记录分数变更日志
            ReputationLog log = new ReputationLog();
            log.setUserId(userId);
            log.setChangeType(ReputationLog.ChangeType.DEDUCT);
            log.setPoints(points);
            log.setReason(reason);
            log.setCategory(category);
            log.setRelatedId(relatedId);
            log.setConsecutiveDays(consecutiveDays);
            log.setProcessedBy(ReputationLog.ProcessedBy.SYSTEM);
            reputationLogMapper.insert(log);

            // 更新用户信誉统计
            updateUserReputationStats(userId, points, false);



        } catch (Exception e) {
            log.error("为用户 {} 扣除分数失败", userId, e);
            throw new RuntimeException("扣除信誉分数失败", e);
        }
    }

    /**
     * 检查是否可以扣分（检查扣分限制）
     * 
     * @param userId 用户ID
     * @param points 要扣除的分数
     * @return true-可以扣分，false-不能扣分
     */
    private boolean canDeductPoints(String userId, int points) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                return true; // 如果没有统计记录，允许扣分
            }

            LocalDate today = LocalDate.now();

            // 检查每日扣分限制
            int todayDeducted = reputationLogMapper.getTodayDeductedPoints(userId, today);
            if (todayDeducted + points > DAILY_DEDUCT_LIMIT) {
                log.warn("用户 {} 今日扣分已达上限，今日已扣: {}, 尝试扣除: {}, 限制: {}", 
                    userId, todayDeducted, points, DAILY_DEDUCT_LIMIT);
                return false;
            }

            // 检查每周扣分限制
            int weeklyDeducted = stats.getWeeklyDeductPoints() != null ? stats.getWeeklyDeductPoints() : 0;
            if (weeklyDeducted + points > WEEKLY_DEDUCT_LIMIT) {
                log.warn("用户 {} 本周扣分已达上限，本周已扣: {}, 尝试扣除: {}, 限制: {}", 
                    userId, weeklyDeducted, points, WEEKLY_DEDUCT_LIMIT);
                return false;
            }

            // 检查每月扣分限制
            int monthlyDeducted = stats.getMonthlyDeductPoints() != null ? stats.getMonthlyDeductPoints() : 0;
            if (monthlyDeducted + points > MONTHLY_DEDUCT_LIMIT) {
                log.warn("用户 {} 本月扣分已达上限，本月已扣: {}, 尝试扣除: {}, 限制: {}", 
                    userId, monthlyDeducted, points, MONTHLY_DEDUCT_LIMIT);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查用户 {} 扣分限制失败", userId, e);
            return false;
        }
    }

    /**
     * 更新用户信誉统计
     * 
     * @param userId 用户ID
     * @param points 分数变化
     * @param isAdd true-增加，false-扣除
     */
    private void updateUserReputationStats(String userId, int points, boolean isAdd) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                // 如果不存在统计记录，创建一个
                userProtectionService.initUserProtection(userId);
                stats = userReputationStatsMapper.selectByUserId(userId);
            }

            // 更新分数
            int currentScore = stats.getCurrentScore() != null ? stats.getCurrentScore() : 100;
            int newScore = isAdd ? 
                currentScore + points : 
                Math.max(0, currentScore - points); // 分数不能低于0

            stats.setCurrentScore(newScore);

            // 更新累计统计
            if (isAdd) {
                int totalEarned = stats.getTotalEarned() != null ? stats.getTotalEarned() : 0;
                stats.setTotalEarned(totalEarned + points);
            } else {
                int totalDeducted = stats.getTotalDeducted() != null ? stats.getTotalDeducted() : 0;
                stats.setTotalDeducted(totalDeducted + points);
                updateDeductLimits(stats, points);
            }

            // 更新等级
            String newLevel = calculateLevel(newScore);
            String oldLevel = stats.getCurrentLevel();
            stats.setCurrentLevel(newLevel);

            // 更新时间戳
            stats.setLastScoreUpdate(LocalDateTime.now());
            userReputationStatsMapper.updateById(stats);

            // 同步更新users表
            updateUserTable(userId, newScore, newLevel);

            // 记录等级变化
            if (!newLevel.equals(oldLevel)) {
                log.info("用户 {} 等级变化：{} -> {}, 当前分数: {}", userId, oldLevel, newLevel, newScore);
            }

        } catch (Exception e) {
            log.error("更新用户 {} 信誉统计失败", userId, e);
            throw new RuntimeException("更新用户信誉统计失败", e);
        }
    }

    /**
     * 更新扣分限制统计
     * 
     * @param stats 用户信誉统计
     * @param points 扣除的分数
     */
    private void updateDeductLimits(UserReputationStats stats, int points) {
        LocalDate today = LocalDate.now();

        // 更新周扣分统计
        LocalDate weekStart = today.with(DayOfWeek.MONDAY);
        if (stats.getLastWeeklyReset() == null || !stats.getLastWeeklyReset().equals(weekStart)) {
            stats.setWeeklyDeductPoints(points);
            stats.setLastWeeklyReset(weekStart);
        } else {
            int weeklyDeducted = stats.getWeeklyDeductPoints() != null ? stats.getWeeklyDeductPoints() : 0;
            stats.setWeeklyDeductPoints(weeklyDeducted + points);
        }

        // 更新月扣分统计
        LocalDate monthStart = today.withDayOfMonth(1);
        if (stats.getLastMonthlyReset() == null || !stats.getLastMonthlyReset().equals(monthStart)) {
            stats.setMonthlyDeductPoints(points);
            stats.setLastMonthlyReset(monthStart);
        } else {
            int monthlyDeducted = stats.getMonthlyDeductPoints() != null ? stats.getMonthlyDeductPoints() : 0;
            stats.setMonthlyDeductPoints(monthlyDeducted + points);
        }
    }

    /**
     * 根据分数计算等级
     * 
     * @param score 当前分数
     * @return 等级字符串
     */
    private String calculateLevel(int score) {
        if (score < 100) return UserReputationStats.Level.NEWBIE;
        if (score < 300) return UserReputationStats.Level.BRONZE;
        if (score < 600) return UserReputationStats.Level.SILVER;
        if (score < 1000) return UserReputationStats.Level.GOLD;
        if (score < 2000) return UserReputationStats.Level.PLATINUM;
        if (score < 5000) return UserReputationStats.Level.DIAMOND;
        if (score < 10000) return UserReputationStats.Level.MASTER;
        return UserReputationStats.Level.GRANDMASTER;
    }

    /**
     * 同步更新users表中的信誉信息
     *
     * @param userId 用户ID
     * @param score 分数
     * @param level 等级
     */
    private void updateUserTable(String userId, int score, String level) {
        try {
            User user = userMapper.selectById(userId);
            if (user != null) {
                user.setReputationScore(score);
                user.setReputationLevel(level);
                userMapper.updateById(user);
            }
        } catch (Exception e) {
            log.error("同步更新用户 {} 的users表信誉信息失败", userId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取用户信誉统计信息
     *
     * @param userId 用户ID
     * @return 用户信誉统计
     */
    public UserReputationStats getUserReputationStats(String userId) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                log.warn("用户 {} 的信誉统计记录不存在", userId);
                return null;
            }
            return stats;
        } catch (Exception e) {
            log.error("获取用户 {} 信誉统计失败", userId, e);
            return null;
        }
    }

    /**
     * 手动调整用户信誉分数（管理员功能）
     *
     * @param userId 用户ID
     * @param points 调整的分数（正数为增加，负数为扣除）
     * @param reason 调整原因
     * @param adminId 管理员ID
     */
    public void adjustPoints(String userId, int points, String reason, String adminId) {
        try {
            if (points > 0) {
                ReputationLog reputationLog = new ReputationLog();
                reputationLog.setUserId(userId);
                reputationLog.setChangeType(ReputationLog.ChangeType.EARN);
                reputationLog.setPoints(points);
                reputationLog.setReason("管理员调整：" + reason);
                reputationLog.setCategory(ReputationLog.Category.RECOVERY);
                reputationLog.setProcessedBy(ReputationLog.ProcessedBy.ADMIN);
                reputationLogMapper.insert(reputationLog);

                updateUserReputationStats(userId, points, true);
                log.info("管理员 {} 为用户 {} 增加 {} 分，原因：{}", adminId, userId, points, reason);
            } else if (points < 0) {
                int deductPoints = Math.abs(points);
                ReputationLog reputationLog = new ReputationLog();
                reputationLog.setUserId(userId);
                reputationLog.setChangeType(ReputationLog.ChangeType.DEDUCT);
                reputationLog.setPoints(deductPoints);
                reputationLog.setReason("管理员调整：" + reason);
                reputationLog.setCategory(ReputationLog.Category.PLATFORM_VIOLATION);
                reputationLog.setProcessedBy(ReputationLog.ProcessedBy.ADMIN);
                reputationLogMapper.insert(reputationLog);

                // 管理员调整不受扣分限制
                updateUserReputationStatsForced(userId, deductPoints, false);
                log.info("管理员 {} 为用户 {} 扣除 {} 分，原因：{}", adminId, userId, deductPoints, reason);
            }
        } catch (Exception e) {
            log.error("管理员 {} 调整用户 {} 分数失败", adminId, userId, e);
            throw new RuntimeException("调整用户信誉分数失败", e);
        }
    }

    /**
     * 更新用户登录统计信息
     *
     * @param userId 用户ID
     * @param loginDate 登录日期
     * @param consecutiveLoginDays 连续登录天数
     */
    public void updateUserLoginStats(String userId, LocalDate loginDate, int consecutiveLoginDays) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats != null) {
                stats.setLastLoginDate(loginDate);
                stats.setConsecutiveLoginDays(consecutiveLoginDays);
                stats.setUpdatedAt(LocalDateTime.now());

                userReputationStatsMapper.updateById(stats);

                log.debug("更新用户 {} 登录统计成功: 登录日期={}, 连续天数={}", userId, loginDate, consecutiveLoginDays);
            } else {
                log.warn("用户 {} 信誉统计记录不存在，无法更新登录统计", userId);
            }
        } catch (Exception e) {
            log.error("更新用户 {} 登录统计失败", userId, e);
            throw new RuntimeException("更新用户登录统计失败", e);
        }
    }

    /**
     * 强制更新用户信誉统计（不受限制）
     *
     * @param userId 用户ID
     * @param points 分数变化
     * @param isAdd true-增加，false-扣除
     */
    private void updateUserReputationStatsForced(String userId, int points, boolean isAdd) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                userProtectionService.initUserProtection(userId);
                stats = userReputationStatsMapper.selectByUserId(userId);
            }

            // 更新分数
            int currentScore = stats.getCurrentScore() != null ? stats.getCurrentScore() : 100;
            int newScore = isAdd ?
                currentScore + points :
                Math.max(0, currentScore - points);

            stats.setCurrentScore(newScore);

            // 更新累计统计
            if (isAdd) {
                int totalEarned = stats.getTotalEarned() != null ? stats.getTotalEarned() : 0;
                stats.setTotalEarned(totalEarned + points);
            } else {
                int totalDeducted = stats.getTotalDeducted() != null ? stats.getTotalDeducted() : 0;
                stats.setTotalDeducted(totalDeducted + points);
                // 管理员调整不更新扣分限制
            }

            // 更新等级
            String newLevel = calculateLevel(newScore);
            stats.setCurrentLevel(newLevel);
            stats.setLastScoreUpdate(LocalDateTime.now());
            userReputationStatsMapper.updateById(stats);

            // 同步更新users表
            updateUserTable(userId, newScore, newLevel);

        } catch (Exception e) {
            log.error("强制更新用户 {} 信誉统计失败", userId, e);
            throw new RuntimeException("强制更新用户信誉统计失败", e);
        }
    }

    /**
     * 重置用户的扣分限制统计
     */
    public void resetDeductLimits() {
        try {
            LocalDate today = LocalDate.now();

            // 重置周扣分统计（每周一）
            if (today.getDayOfWeek() == DayOfWeek.MONDAY) {
                int weeklyResetCount = userReputationStatsMapper.resetWeeklyDeductPoints(today);
                log.info("重置周扣分统计，影响用户数: {}", weeklyResetCount);
            }

            // 重置月扣分统计（每月1号）
            if (today.getDayOfMonth() == 1) {
                int monthlyResetCount = userReputationStatsMapper.resetMonthlyDeductPoints(today);
                log.info("重置月扣分统计，影响用户数: {}", monthlyResetCount);
            }

        } catch (Exception e) {
            log.error("重置扣分限制统计失败", e);
        }
    }
}
