package com.wqh.publicexaminationassistant.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 创建刷题记录请求DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class CreateStudyRecordRequest {

    /**
     * 模块类型
     * 如: math(数学运算), logic(逻辑推理), language(言语理解), knowledge(常识判断), essay(申论)
     */
    @NotBlank(message = "模块类型不能为空")
    @Size(max = 50, message = "模块类型不能超过50个字符")
    @Pattern(regexp = "^(math|logic|language|knowledge|essay)$", 
             message = "模块类型必须是: math, logic, language, knowledge, essay")
    private String moduleType;

    /**
     * 题目数量
     */
    @NotNull(message = "题目数量不能为空")
    @Min(value = 1, message = "题目数量至少为1")
    @Max(value = 1000, message = "题目数量不能超过1000")
    private Integer questionCount;

    /**
     * 正确数量
     */
    @NotNull(message = "正确数量不能为空")
    @Min(value = 0, message = "正确数量不能为负数")
    private Integer correctCount;

    /**
     * 学习时长(分钟)
     */
    @NotNull(message = "学习时长不能为空")
    @Min(value = 1, message = "学习时长至少为1分钟")
    @Max(value = 1440, message = "学习时长不能超过1440分钟(24小时)")
    private Integer studyDuration;

    /**
     * 薄弱知识点 (JSON字符串格式)
     */
    @Size(max = 2000, message = "薄弱知识点内容不能超过2000个字符")
    private String weakPoints;

    /**
     * 学习日期
     */
    @NotNull(message = "学习日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate studyDate;

    /**
     * 学习笔记
     */
    @Size(max = 1000, message = "学习笔记不能超过1000个字符")
    private String notes;

    /**
     * 错题列表 (可选)
     */
    @Valid
    private List<WrongQuestionRequest> wrongQuestions;

    /**
     * 验证正确数量不能超过题目数量
     */
    @AssertTrue(message = "正确数量不能超过题目数量")
    public boolean isCorrectCountValid() {
        if (questionCount == null || correctCount == null) {
            return true; // 让其他验证处理null值
        }
        return correctCount <= questionCount;
    }

    /**
     * 验证学习日期不能是未来日期
     */
    @AssertTrue(message = "学习日期不能是未来日期")
    public boolean isStudyDateValid() {
        if (studyDate == null) {
            return true; // 让其他验证处理null值
        }
        return !studyDate.isAfter(LocalDate.now());
    }
}
