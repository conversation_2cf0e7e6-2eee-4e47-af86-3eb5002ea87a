import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button, Input, Form, Toast, Typography, Space } from '@douyinfe/semi-ui';
import { IconUser, IconLock, IconEyeOpened, IconEyeClosed } from '@douyinfe/semi-icons';
import { useAuthStore } from '../stores/useAuthStore';
import { validateForm } from '../services/authService';
import '../styles/theme.css';

const { Title, Text } = Typography;

interface LocationState {
  from?: string;
  message?: string;
}

export const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const state = location.state as LocationState;
  
  const { login, isLoading, error, clearError, isAuthenticated } = useAuthStore();
  
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  
  const [formErrors, setFormErrors] = useState({
    username: '',
    password: ''
  });
  
  const [showPassword, setShowPassword] = useState(false);

  // 如果已登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated) {
      const redirectTo = state?.from || '/dashboard';
      navigate(redirectTo, { replace: true });
    }
  }, [isAuthenticated, navigate, state]);

  // 显示来自其他页面的消息
  useEffect(() => {
    if (state?.message) {
      Toast.info(state.message);
    }
  }, [state]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      Toast.error(error);
      clearError();
    }
  }, [error, clearError]);

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 验证表单
  const validateFormData = () => {
    const errors = {
      username: '',
      password: ''
    };

    // 验证用户名（可以是用户名或邮箱）
    if (!formData.username) {
      errors.username = '请输入用户名或邮箱';
    }

    // 验证密码
    const passwordError = validateForm.password(formData.password);
    if (passwordError) {
      errors.password = passwordError;
    }

    setFormErrors(errors);
    return !errors.username && !errors.password;
  };

  // 处理登录提交
  const handleSubmit = async () => {
    if (!validateFormData()) {
      return;
    }

    try {
      await login(formData);
      Toast.success('登录成功！');
      
      // 登录成功后跳转
      const redirectTo = state?.from || '/dashboard';
      navigate(redirectTo, { replace: true });
    } catch (error) {
      // 错误已在 useEffect 中处理
      console.error('登录失败:', error);
    }
  };

  // 处理回车键登录
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <div className="auth-container">
      {/* 背景装饰 */}
      <div className="floating-emoji" style={{ top: '20%', left: '15%', animationDelay: '0s' }}>
        📖
      </div>
      <div className="floating-emoji" style={{ top: '60%', right: '20%', animationDelay: '1s' }}>
        ✏️
      </div>
      <div className="floating-emoji" style={{ bottom: '30%', left: '10%', animationDelay: '2s' }}>
        🎯
      </div>

      <div className="auth-card">
        <div className="sketch-card">
          {/* 标题 */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Title level={2} className="handwritten-title large" style={{ marginBottom: '8px' }}>
              欢迎回来！
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              继续你的考公学习之旅 🚀
            </Text>
          </div>

          {/* 登录表单 */}
          <Form onSubmit={handleSubmit}>
            <Space vertical style={{ width: '100%' }} spacing={20}>
              {/* 用户名输入 */}
              <div>
                <Input
                  size="large"
                  placeholder="请输入用户名或邮箱"
                  prefix={<IconUser />}
                  value={formData.username}
                  onChange={(value) => handleInputChange('username', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    fontFamily: 'var(--font-system)',
                    borderColor: formErrors.username ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.username && (
                  <div className="error-message">{formErrors.username}</div>
                )}
              </div>

              {/* 密码输入 */}
              <div>
                <Input
                  size="large"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码"
                  prefix={<IconLock />}
                  suffix={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      style={{ 
                        background: 'none', 
                        border: 'none', 
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {showPassword ? <IconEyeClosed /> : <IconEyeOpened />}
                    </button>
                  }
                  value={formData.password}
                  onChange={(value) => handleInputChange('password', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    fontFamily: 'var(--font-system)',
                    borderColor: formErrors.password ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.password && (
                  <div className="error-message">{formErrors.password}</div>
                )}
              </div>

              {/* 登录按钮 */}
              <Button
                size="large"
                type="primary"
                loading={isLoading}
                onClick={handleSubmit}
                className="sketch-button primary"
                style={{ 
                  width: '100%',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: '500'
                }}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>

              {/* 注册链接 */}
              <div style={{ textAlign: 'center', marginTop: '16px' }}>
                <Text type="secondary">
                  还没有账号？
                  <Link 
                    to="/register" 
                    style={{ 
                      color: 'var(--accent-blue)',
                      textDecoration: 'none',
                      fontWeight: '500',
                      marginLeft: '8px'
                    }}
                  >
                    立即注册
                  </Link>
                </Text>
              </div>
            </Space>
          </Form>
        </div>

        {/* 温馨提示 */}
        <div className="sticky-note" style={{ marginTop: '24px' }}>
          <Text style={{ fontSize: '14px', fontFamily: 'var(--font-handwritten)' }}>
            💡 小提示：忘记密码了？联系管理员重置密码哦～
          </Text>
        </div>
      </div>
    </div>
  );
};

export default Login;
