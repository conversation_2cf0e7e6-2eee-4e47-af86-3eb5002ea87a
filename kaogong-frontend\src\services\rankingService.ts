import { request } from './api';

// 排行榜类型
export type RankingType = 'study_questions' | 'study_accuracy' | 'study_time' | 'reputation_score' | 'comprehensive';

// 时间周期
export type RankingPeriod = 'daily' | 'weekly' | 'monthly' | 'all_time';

// 全站排行榜响应
export interface GlobalRankingResponse {
  rank: number;
  userId: string;
  username: string;
  nickname: string;
  avatarUrl?: string;
  score: number;
  reputationLevel: string;
  rankChange?: number;
  rankingType: string;
  period: string;
  calculatedAt: string;
  isCurrentUser: boolean;
  extraStats: {
    totalQuestions?: number;
    correctQuestions?: number;
    accuracyRate?: number;
    studyTime?: number;
    studyDays?: number;
    consecutiveStudyDays?: number;
    reputationScore?: number;
  };
}

// 用户个人排名响应
export interface UserRankResponse {
  currentRank?: number;
  totalParticipants: number;
  score: number;
  rankChange?: number;
  percentile: number;
  rankingType: string;
  period: string;
  calculatedAt: string;
  scoreGapToNext?: number;
  scoreGapToPrevious?: number;
  detailStats: {
    totalQuestions?: number;
    correctQuestions?: number;
    accuracyRate?: number;
    studyTime?: number;
    studyDays?: number;
    consecutiveStudyDays?: number;
    reputationScore?: number;
    comprehensiveScore?: number;
  };
  rankLevel: string;
}

// 排行榜统计信息
export interface RankingStatistics {
  rankingType: string;
  period: string;
  totalParticipants: number;
  averageScore: number;
  medianScore: number;
  maxScore: number;
  minScore: number;
  topPercentileScore: number;
  scoreDistribution: Array<{
    range: string;
    minScore: number;
    maxScore: number;
    count: number;
    percentage: number;
  }>;
  updatedAt: string;
  participantChange?: number;
  trendInfo: {
    averageScoreTrend: 'up' | 'down' | 'stable';
    participantTrend: 'up' | 'down' | 'stable';
    competitionLevel: 'high' | 'medium' | 'low';
    description: string;
  };
}

// 排行榜配置
export interface RankingConfig {
  types: Record<string, {
    name: string;
    description: string;
  }>;
  periods: Record<string, {
    name: string;
    description: string;
  }>;
  pagination: {
    defaultPageSize: number;
    maxPageSize: number;
    minPageSize: number;
  };
}

// 分页响应
export interface PageResponse<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 排行榜API服务
export const rankingService = {
  /**
   * 获取全站排行榜
   */
  async getGlobalRanking(
    type: RankingType,
    period: RankingPeriod,
    page: number = 1,
    size: number = 50
  ): Promise<PageResponse<GlobalRankingResponse>> {
    try {
      console.log('调用排行榜API:', { type, period, page, size });

      // request工具已经在api.ts中处理了response.data.data的提取
      const data = await request.get('/v1/rankings/global', {
        params: { type, period, page, size }
      });

      console.log('排行榜API返回数据:', data);

      // 如果没有数据，返回空的分页结构
      if (!data) {
        console.warn('排行榜API返回空数据');
        return { records: [], total: 0, current: 1, size: 50, pages: 0 };
      }

      return data;
    } catch (error) {
      console.error('getGlobalRanking 调用失败:', error);
      throw error;
    }
  },

  /**
   * 获取用户个人排名
   */
  async getMyRank(
    type: RankingType,
    period: RankingPeriod
  ): Promise<UserRankResponse> {
    try {
      console.log('调用个人排名API:', { type, period });

      // request工具已经在api.ts中处理了response.data.data的提取
      const data = await request.get('/v1/rankings/my-rank', {
        params: { type, period }
      });

      console.log('个人排名API返回数据:', data);

      return data;
    } catch (error) {
      console.error('getMyRank 调用失败:', error);
      throw error;
    }
  },

  /**
   * 获取排行榜统计信息
   */
  async getRankingStatistics(
    type: RankingType,
    period: RankingPeriod
  ): Promise<RankingStatistics> {
    const data = await request.get('/v1/rankings/statistics', {
      params: { type, period }
    });
    return data;
  },

  /**
   * 获取排行榜配置
   */
  async getRankingConfig(): Promise<RankingConfig> {
    const data = await request.get('/v1/rankings/config');
    return data;
  },

  /**
   * 获取排行榜更新状态
   */
  async getUpdateStatus(type?: RankingType, period?: RankingPeriod) {
    const data = await request.get('/v1/rankings/update-status', {
      params: { type, period }
    });
    return data;
  },

  /**
   * 手动触发排行榜计算（管理员功能）
   */
  async calculateRanking(type: RankingType, period: RankingPeriod) {
    const data = await request.post('/v1/rankings/calculate', null, {
      params: { type, period }
    });
    return data;
  }
};

// 排行榜工具函数
export const rankingUtils = {
  /**
   * 获取排行榜类型显示名称
   */
  getTypeDisplayName(type: RankingType): string {
    const typeNames: Record<RankingType, string> = {
      study_questions: '刷题数量',
      study_accuracy: '正确率',
      study_time: '学习时长',
      reputation_score: '信誉分数',
      comprehensive: '综合排名'
    };
    return typeNames[type] || type;
  },

  /**
   * 获取时间周期显示名称
   */
  getPeriodDisplayName(period: RankingPeriod): string {
    const periodNames: Record<RankingPeriod, string> = {
      daily: '日榜',
      weekly: '周榜',
      monthly: '月榜',
      all_time: '总榜'
    };
    return periodNames[period] || period;
  },

  /**
   * 获取排名变化图标
   */
  getRankChangeIcon(change?: number): string {
    if (!change || change === 0) return '➖';
    return change > 0 ? '⬆️' : '⬇️';
  },

  /**
   * 获取排名变化颜色
   */
  getRankChangeColor(change?: number): string {
    if (!change || change === 0) return '#666';
    return change > 0 ? '#52c41a' : '#ff4d4f';
  },

  /**
   * 格式化分数显示
   */
  formatScore(score: number, type: RankingType): string {
    switch (type) {
      case 'study_accuracy':
        return `${score.toFixed(1)}%`;
      case 'study_time':
        return `${Math.floor(score / 60)}h${score % 60}m`;
      default:
        return score.toLocaleString();
    }
  },

  /**
   * 获取排名等级颜色
   */
  getRankLevelColor(rank?: number, total?: number): string {
    if (!rank || !total) return '#666';
    const percentage = (rank / total) * 100;
    
    if (percentage <= 1) return '#ff6b6b'; // 王者
    if (percentage <= 5) return '#4ecdc4'; // 大师
    if (percentage <= 10) return '#45b7d1'; // 钻石
    if (percentage <= 25) return '#96ceb4'; // 铂金
    if (percentage <= 50) return '#feca57'; // 黄金
    if (percentage <= 75) return '#c0c0c0'; // 白银
    return '#cd7f32'; // 青铜
  },

  /**
   * 获取信誉等级颜色
   */
  getReputationLevelColor(level: string): string {
    const colors: Record<string, string> = {
      newbie: '#87d068',
      bronze: '#CD7F32',
      silver: '#C0C0C0',
      gold: '#FFD700',
      platinum: '#E5E4E2',
      diamond: '#B9F2FF',
      master: '#FF6B6B',
      grandmaster: '#8A2BE2'
    };
    return colors[level] || '#666';
  }
};
