package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.WrongQuestionImage;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 错题图片数据访问层
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Mapper
public interface WrongQuestionImageMapper extends BaseMapper<WrongQuestionImage> {

    /**
     * 根据错题ID查询图片列表
     * 
     * @param wrongQuestionId 错题ID
     * @return 图片列表
     */
    @Select("SELECT * FROM wrong_question_images WHERE wrong_question_id = #{wrongQuestionId} ORDER BY sort_order ASC, created_at ASC")
    List<WrongQuestionImage> findByWrongQuestionId(@Param("wrongQuestionId") String wrongQuestionId);

    /**
     * 根据错题ID和图片类型查询图片列表
     * 
     * @param wrongQuestionId 错题ID
     * @param imageType 图片类型
     * @return 图片列表
     */
    @Select("SELECT * FROM wrong_question_images WHERE wrong_question_id = #{wrongQuestionId} AND image_type = #{imageType} ORDER BY sort_order ASC, created_at ASC")
    List<WrongQuestionImage> findByWrongQuestionIdAndImageType(@Param("wrongQuestionId") String wrongQuestionId, 
                                                               @Param("imageType") String imageType);

    /**
     * 删除错题关联的所有图片
     * 
     * @param wrongQuestionId 错题ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM wrong_question_images WHERE wrong_question_id = #{wrongQuestionId}")
    int deleteByWrongQuestionId(@Param("wrongQuestionId") String wrongQuestionId);

    /**
     * 根据图片ID和用户ID查询图片（用于权限验证）
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     * @return 图片信息
     */
    @Select("<script>" +
            "SELECT wqi.* FROM wrong_question_images wqi " +
            "INNER JOIN wrong_questions wq ON wqi.wrong_question_id = wq.id " +
            "WHERE wqi.id = #{imageId} AND wq.user_id = #{userId}" +
            "</script>")
    WrongQuestionImage findByIdAndUserId(@Param("imageId") String imageId, @Param("userId") String userId);

    /**
     * 统计错题的图片数量
     * 
     * @param wrongQuestionId 错题ID
     * @return 图片数量
     */
    @Select("SELECT COUNT(*) FROM wrong_question_images WHERE wrong_question_id = #{wrongQuestionId}")
    int countByWrongQuestionId(@Param("wrongQuestionId") String wrongQuestionId);

    /**
     * 统计错题指定类型的图片数量
     * 
     * @param wrongQuestionId 错题ID
     * @param imageType 图片类型
     * @return 图片数量
     */
    @Select("SELECT COUNT(*) FROM wrong_question_images WHERE wrong_question_id = #{wrongQuestionId} AND image_type = #{imageType}")
    int countByWrongQuestionIdAndImageType(@Param("wrongQuestionId") String wrongQuestionId, 
                                          @Param("imageType") String imageType);

    /**
     * 更新图片排序
     * 
     * @param imageId 图片ID
     * @param sortOrder 新的排序值
     * @return 更新的记录数
     */
    @Update("UPDATE wrong_question_images SET sort_order = #{sortOrder} WHERE id = #{imageId}")
    int updateSortOrder(@Param("imageId") String imageId, @Param("sortOrder") Integer sortOrder);

    /**
     * 批量更新图片排序
     * 
     * @param wrongQuestionId 错题ID
     * @param imageType 图片类型
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE wrong_question_images SET sort_order = " +
            "CASE id " +
            "<foreach collection='imageIds' item='imageId' index='index'>" +
            "WHEN #{imageId} THEN #{index} " +
            "</foreach>" +
            "END " +
            "WHERE wrong_question_id = #{wrongQuestionId} AND image_type = #{imageType} " +
            "AND id IN " +
            "<foreach collection='imageIds' item='imageId' open='(' separator=',' close=')'>" +
            "#{imageId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateSortOrder(@Param("wrongQuestionId") String wrongQuestionId, 
                            @Param("imageType") String imageType, 
                            @Param("imageIds") List<String> imageIds);
}
