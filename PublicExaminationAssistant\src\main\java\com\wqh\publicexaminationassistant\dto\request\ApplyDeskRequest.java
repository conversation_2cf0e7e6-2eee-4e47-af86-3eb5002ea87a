package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 申请加入小桌请求DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class ApplyDeskRequest {

    /**
     * 小桌ID
     */
    @NotBlank(message = "小桌ID不能为空")
    private String deskId;

    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    @Size(min = 10, max = 200, message = "申请理由长度必须在10-200个字符之间")
    private String reason;

    /**
     * 学习计划
     */
    @Size(max = 500, message = "学习计划不能超过500个字符")
    private String studyPlan;
}
