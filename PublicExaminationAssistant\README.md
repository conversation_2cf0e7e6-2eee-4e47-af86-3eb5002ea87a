# 考公刷题记录系统

基于小桌系统和信誉体系的考公刷题记录平台，帮助考生高效备考、互相监督、共同进步。

## 🎯 项目概述

考公刷题记录系统是一个专为公务员考试备考人员设计的学习记录和互助平台，核心创新功能包括：

- **小桌系统** 🌟：6-8 人小组排行榜机制，通过桌长审核、成员互动、小桌排行等功能增强用户参与感和学习动力
- **信誉体系** 🌟：通过用户行为记录和信誉分数计算，建立质量保障机制，提升用户参与质量和系统可信度

## 🛠️ 技术栈

### 后端技术栈

- **基础框架**: Spring Boot 2.6.13
- **数据库**: MySQL 8.0
- **ORM 框架**: MyBatis Plus 3.5.1
- **缓存**: Redis + Caffeine
- **认证授权**: Spring Security + JWT
- **数据库迁移**: Flyway
- **API 文档**: Swagger 3.0
- **工具库**: Hutool、Lombok

### 前端技术栈

- **框架**: Vue 3
- **UI 组件库**: Semi Design / Ant Design
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP 客户端**: Axios
- **构建工具**: Vite

## 🚀 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 本地开发

1. **克隆项目**

   ```bash
   git clone https://github.com/yourusername/public-examination-assistant.git
   cd public-examination-assistant
   ```

2. **配置数据库**

   ```bash
   # 创建数据库
   mysql -u root -p -e "CREATE DATABASE kaogong_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

   # 导入初始数据
   mysql -u root -p kaogong_db < database/database-schema.sql
   ```

3. **配置应用**

   ```bash
   # 修改配置文件
   vim src/main/resources/application.yml
   ```

4. **构建运行**

   ```bash
   # 构建项目
   mvn clean package -DskipTests

   # 运行应用
   java -jar target/public-examination-assistant-0.0.1-SNAPSHOT.jar
   ```

5. **访问应用**
   - API 服务: http://localhost:8080/api
   - Swagger 文档: http://localhost:8080/swagger-ui/

## 📋 项目结构

```
public-examination-assistant/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/wqh/publicexaminationassistant/
│   │   │       ├── config/           # 配置类
│   │   │       ├── controller/       # 控制器层
│   │   │       ├── service/          # 服务层
│   │   │       ├── mapper/           # 数据访问层
│   │   │       ├── entity/           # 实体类
│   │   │       ├── dto/              # 数据传输对象
│   │   │       ├── common/           # 公共组件
│   │   │       ├── security/         # 安全相关
│   │   │       └── PublicExaminationAssistantApplication.java
│   │   └── resources/
│   │       ├── application.yml       # 应用配置
│   │       ├── application-dev.yml   # 开发环境配置
│   │       ├── application-prod.yml  # 生产环境配置
│   │       ├── mapper/               # MyBatis XML映射文件
│   │       └── db/migration/         # Flyway迁移脚本
│   └── test/                         # 测试代码
├── database/                         # 数据库相关
│   ├── database-design.md            # 数据库设计文档
│   ├── database-schema.sql           # 数据库建表脚本
│   └── database-indexes.sql          # 索引优化脚本
├── api/                              # API文档
│   ├── user-management-api.md        # 用户管理API
│   ├── study-records-api.md          # 刷题记录API
│   └── ...                           # 其他模块API
├── pom.xml                           # Maven配置
└── README.md                         # 项目说明
```

## 📚 核心功能模块

### 1. 用户管理模块

- 用户注册、登录、个人资料管理
- 邀请码系统
- 角色权限控制

### 2. 刷题记录模块

- 学习记录管理
- 错题本功能
- 学习计划管理
- 学习统计分析

### 3. 小桌系统模块 🌟

- 小桌创建与管理
- 成员申请与审核
- 小桌内排行榜
- 成员互动功能
- 活跃度管理

### 4. 信誉体系模块 🌟

- 信誉分数计算
- 行为记录与分析
- 等级特权管理
- 质量保障机制

### 5. 排行榜模块

- 全站排行榜
- 小桌排行榜
- 多维度排名
- 历史排名趋势

### 6. 考试公告模块

- 公告发布与管理
- 全文搜索功能
- 时间提醒功能

### 7. 通知系统模块

- 系统通知
- 小桌通知
- 提醒通知
- 通知设置

## 📝 开发规范

### 代码风格

- 遵循阿里巴巴 Java 开发手册
- 使用 Lombok 简化代码
- 统一的异常处理
- 完整的注释文档

### API 设计

- RESTful API 设计规范
- 统一的响应格式
- 完整的参数验证
- 详细的 API 文档

### 数据库规范

- 表名使用小写字母，单词间用下划线分隔
- 主键使用 UUID
- 创建/更新时间字段
- 合理的索引设计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件
