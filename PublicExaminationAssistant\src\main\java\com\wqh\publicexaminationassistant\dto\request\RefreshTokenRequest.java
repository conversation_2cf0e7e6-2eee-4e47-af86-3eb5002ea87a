package com.wqh.publicexaminationassistant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 刷新Token请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "刷新Token请求")
public class RefreshTokenRequest {

    @NotBlank(message = "刷新Token不能为空")
    @Schema(description = "刷新Token", example = "eyJhbGciOiJIUzI1NiJ9...")
    private String refreshToken;
}
