package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 错题图片实体类
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@TableName("wrong_question_images")
public class WrongQuestionImage {

    /**
     * 图片ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 关联的错题ID
     */
    @TableField("wrong_question_id")
    private String wrongQuestionId;

    /**
     * 图片类型：question|answer|explanation
     */
    @TableField("image_type")
    private String imageType;

    /**
     * 原始文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件存储路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * MIME类型
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 图片宽度（像素）
     */
    @TableField("width")
    private Integer width;

    /**
     * 图片高度（像素）
     */
    @TableField("height")
    private Integer height;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否已压缩
     */
    @TableField("is_compressed")
    private Boolean isCompressed;

    /**
     * 缩略图路径
     */
    @TableField("thumbnail_path")
    private String thumbnailPath;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
