package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 信誉记录表实体类
 * 记录信誉分数的变化历史和原因
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("reputation_logs")
public class ReputationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 变化类型: earn-获得, deduct-扣除
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 分数变化
     */
    @TableField("points")
    private Integer points;

    /**
     * 变化原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 分类: daily_login, daily_study, study_quality, desk_performance,
     * desk_violation, platform_violation, inactive, recovery
     */
    @TableField("category")
    private String category;

    /**
     * 关联ID(如小桌ID、学习记录ID等)
     */
    @TableField("related_id")
    private String relatedId;

    /**
     * 连续天数(用于连续学习/未学习记录)
     */
    @TableField("consecutive_days")
    private Integer consecutiveDays;

    /**
     * 处理方式: system-系统, manual-手动, admin-管理员
     */
    @TableField("processed_by")
    private String processedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    // 常量定义
    public static class ChangeType {
        public static final String EARN = "earn";
        public static final String DEDUCT = "deduct";
    }

    public static class Category {
        public static final String DAILY_LOGIN = "daily_login";
        public static final String DAILY_STUDY = "daily_study";
        public static final String STUDY_QUALITY = "study_quality";
        public static final String DESK_PERFORMANCE = "desk_performance";
        public static final String DESK_VIOLATION = "desk_violation";
        public static final String PLATFORM_VIOLATION = "platform_violation";
        public static final String INACTIVE = "inactive";
        public static final String RECOVERY = "recovery";
    }

    public static class ProcessedBy {
        public static final String SYSTEM = "system";
        public static final String MANUAL = "manual";
        public static final String ADMIN = "admin";
    }
}
