<html lang="zh-CN"><head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>考公刷题记录系统 - 手绘温馨风格</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&amp;display=swap" rel="stylesheet">
  <style>
      :root {
          --paper-bg: #fefdf8;
          --ink-dark: #2d3748;
          --ink-medium: #4a5568;
          --ink-light: #718096;
          --accent-blue: #4299e1;
          --accent-green: #48bb78;
          --accent-orange: #ed8936;
          --accent-purple: #9f7aea;
          --accent-pink: #ed64a6;
      }
      
      body {
          background: var(--paper-bg);
          font-family: 'Kalam', cursive;
          background-image: 
              radial-gradient(circle at 20px 50px, #ffeaa7 2px, transparent 3px),
              radial-gradient(circle at 40px 80px, #fab1a0 1px, transparent 2px),
              radial-gradient(circle at 90px 30px, #fd79a8 1px, transparent 2px);
          background-size: 100px 100px;
      }
      
      .paper-card {
          background: var(--paper-bg);
          border: 2px solid var(--ink-dark);
          border-radius: 15px;
          position: relative;
          transform: rotate(-0.5deg);
          box-shadow: 3px 3px 0px rgba(45, 55, 72, 0.1);
      }
      
      .paper-card:nth-child(even) {
          transform: rotate(0.5deg);
      }
      
      .paper-card:hover {
          transform: rotate(0deg) translateY(-3px);
          transition: all 0.3s ease;
          box-shadow: 5px 5px 0px rgba(45, 55, 72, 0.15);
      }
      
      .doodle-border {
          border: 3px dashed var(--ink-medium);
          border-radius: 20px;
          position: relative;
      }
      
      .doodle-border::before {
          content: '';
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          border: 2px solid var(--accent-blue);
          border-radius: 25px;
          opacity: 0.3;
      }
      
      .sketch-button {
          background: var(--paper-bg);
          border: 2px solid var(--ink-dark);
          border-radius: 25px;
          padding: 12px 24px;
          font-weight: 600;
          position: relative;
          transform: rotate(-1deg);
          transition: all 0.2s ease;
          cursor: pointer;
      }
      
      .sketch-button:hover {
          transform: rotate(0deg) translateY(-2px);
          box-shadow: 3px 3px 0px var(--accent-blue);
      }
      
      .sketch-button:active {
          transform: rotate(0deg) translateY(0px);
          box-shadow: 1px 1px 0px var(--accent-blue);
      }
      
      .handwritten {
          font-family: 'Kalam', cursive;
          font-weight: 400;
          letter-spacing: 0.5px;
      }
      
      .handwritten-title {
          font-family: 'Kalam', cursive;
          font-weight: 700;
          letter-spacing: 1px;
          position: relative;
      }
      
      .underline-sketch {
          position: relative;
      }
      
      .underline-sketch::after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 3px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 10'%3E%3Cpath d='M0,5 Q25,2 50,5 T100,5' stroke='%234299e1' stroke-width='2' fill='none'/%3E%3C/svg%3E");
          background-size: 100% 100%;
      }
      
      .doodle-icon {
          display: inline-block;
          font-size: 1.5em;
          transform: rotate(-5deg);
          animation: wiggle 2s ease-in-out infinite;
      }
      
      @keyframes wiggle {
          0%, 100% { transform: rotate(-5deg); }
          25% { transform: rotate(-8deg); }
          75% { transform: rotate(-2deg); }
      }
      
      .progress-sketch {
          background: var(--paper-bg);
          border: 2px solid var(--ink-medium);
          border-radius: 20px;
          height: 20px;
          position: relative;
          overflow: hidden;
      }
      
      .progress-fill-sketch {
          background: linear-gradient(45deg, var(--accent-green), var(--accent-blue));
          height: 100%;
          border-radius: 18px;
          position: relative;
      }
      
      .progress-fill-sketch::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 20'%3E%3Cpath d='M0,10 Q25,5 50,10 T100,10' stroke='white' stroke-width='1' fill='none' opacity='0.5'/%3E%3C/svg%3E");
          background-size: 50px 20px;
      }
      
      .sticky-note {
          background: #ffeaa7;
          border: 1px solid #fdcb6e;
          border-radius: 5px;
          transform: rotate(2deg);
          box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
          position: relative;
      }
      
      .sticky-note::before {
          content: '';
          position: absolute;
          top: -5px;
          right: 10px;
          width: 20px;
          height: 10px;
          background: #fdcb6e;
          border-radius: 0 0 5px 5px;
      }
      
      .desk-doodle {
          background: var(--paper-bg);
          border: 3px solid var(--accent-purple);
          border-radius: 20px;
          position: relative;
          transform: rotate(-1deg);
      }
      
      .desk-doodle::before {
          content: '✨';
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 20px;
          animation: sparkle 1.5s ease-in-out infinite;
      }
      
      @keyframes sparkle {
          0%, 100% { transform: scale(1) rotate(0deg); }
          50% { transform: scale(1.2) rotate(180deg); }
      }
      
      .avatar-sketch {
          border: 3px solid var(--ink-dark);
          border-radius: 50%;
          position: relative;
          background: var(--paper-bg);
      }
      
      .avatar-sketch::after {
          content: '';
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border: 2px dashed var(--accent-pink);
          border-radius: 50%;
          animation: rotate 10s linear infinite;
      }
      
      @keyframes rotate {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
      }
      
      .floating-doodle {
          position: absolute;
          font-size: 24px;
          opacity: 0.3;
          animation: float-doodle 4s ease-in-out infinite;
      }
      
      @keyframes float-doodle {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-15px) rotate(5deg); }
      }
      
      .ranking-medal {
          display: inline-block;
          font-size: 1.2em;
          animation: bounce 2s ease-in-out infinite;
      }
      
      @keyframes bounce {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-5px); }
      }
  </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.mx-auto{margin-left:auto;margin-right:auto}.mb-2{margin-bottom:0.5rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-2{margin-left:0.5rem}.ml-3{margin-left:0.75rem}.ml-4{margin-left:1rem}.mr-3{margin-right:0.75rem}.mr-4{margin-right:1rem}.inline-block{display:inline-block}.flex{display:flex}.grid{display:grid}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-24{height:6rem}.h-8{height:2rem}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-24{width:6rem}.w-8{width:2rem}.max-w-7xl{max-width:80rem}.-rotate-1{--tw-rotate:-1deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-rotate-2{--tw-rotate:-2deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-rotate-3{--tw-rotate:-3deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.rotate-1{--tw-rotate:1deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.rotate-2{--tw-rotate:2deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.-space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(-0.5rem * var(--tw-space-x-reverse));margin-left:calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-6 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1.5rem * var(--tw-space-x-reverse));margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.space-y-8 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(2rem * var(--tw-space-y-reverse))}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-xl{border-radius:0.75rem}.border-2{border-width:2px}.border-4{border-width:4px}.border-dashed{border-style:dashed}.border-blue-300{--tw-border-opacity:1;border-color:rgb(147 197 253 / var(--tw-border-opacity, 1))}.border-blue-400{--tw-border-opacity:1;border-color:rgb(96 165 250 / var(--tw-border-opacity, 1))}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.border-green-300{--tw-border-opacity:1;border-color:rgb(134 239 172 / var(--tw-border-opacity, 1))}.border-green-400{--tw-border-opacity:1;border-color:rgb(74 222 128 / var(--tw-border-opacity, 1))}.border-orange-400{--tw-border-opacity:1;border-color:rgb(251 146 60 / var(--tw-border-opacity, 1))}.border-purple-400{--tw-border-opacity:1;border-color:rgb(192 132 252 / var(--tw-border-opacity, 1))}.border-yellow-300{--tw-border-opacity:1;border-color:rgb(253 224 71 / var(--tw-border-opacity, 1))}.bg-blue-200{--tw-bg-opacity:1;background-color:rgb(191 219 254 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-200{--tw-bg-opacity:1;background-color:rgb(187 247 208 / var(--tw-bg-opacity, 1))}.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity, 1))}.bg-orange-50{--tw-bg-opacity:1;background-color:rgb(255 247 237 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-yellow-200{--tw-bg-opacity:1;background-color:rgb(254 240 138 / var(--tw-bg-opacity, 1))}.bg-yellow-50{--tw-bg-opacity:1;background-color:rgb(254 252 232 / var(--tw-bg-opacity, 1))}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-purple-400{--tw-gradient-from:#c084fc var(--tw-gradient-from-position);--tw-gradient-to:rgb(192 132 252 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-pink-400{--tw-gradient-to:#f472b6 var(--tw-gradient-to-position)}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.text-center{text-align:center}.text-right{text-align:right}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1))}.text-blue-700{--tw-text-opacity:1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-green-600{--tw-text-opacity:1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-green-700{--tw-text-opacity:1;color:rgb(21 128 61 / var(--tw-text-opacity, 1))}.text-orange-600{--tw-text-opacity:1;color:rgb(234 88 12 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-yellow-600{--tw-text-opacity:1;color:rgb(202 138 4 / var(--tw-text-opacity, 1))}.hover\:text-gray-800:hover{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}@media (min-width: 768px){.md\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:col-span-2{grid-column:span 2 / span 2}.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}</style></head>

<body>
  <!-- 浮动涂鸦装饰 -->
  <div class="floating-doodle" style="top: 10%; left: 5%;">📚</div>
  <div class="floating-doodle" style="top: 20%; right: 10%; animation-delay: 1s;">✏️</div>
  <div class="floating-doodle" style="bottom: 30%; left: 8%; animation-delay: 2s;">🎯</div>
  <div class="floating-doodle" style="bottom: 10%; right: 15%; animation-delay: 3s;">⭐</div>

  <div class="min-h-screen p-4">
      <!-- 顶部导航 -->
      <nav class="paper-card p-4 mb-8">
          <div class="max-w-7xl mx-auto flex justify-between items-center">
              <div class="flex items-center">
                  <div class="doodle-icon mr-4 text-3xl">📖</div>
                  <h1 class="handwritten-title text-3xl text-gray-800 underline-sketch">考公刷题记录</h1>
              </div>
              <div class="flex items-center space-x-4">
                  <div class="sticky-note px-4 py-2 handwritten">
                      <div class="flex items-center space-x-2">
                          <span class="text-lg">🏆</span>
                          <span class="text-sm font-medium">学霸 · 信誉分：85</span>
                      </div>
                  </div>
                  <div class="avatar-sketch w-12 h-12 flex items-center justify-center">
                      <span class="text-2xl">😊</span>
                  </div>
              </div>
          </div>
      </nav>

      <div class="max-w-7xl mx-auto">
          <!-- 欢迎区域 -->
          <div class="doodle-border p-8 mb-8 text-center">
              <h2 class="handwritten-title text-4xl text-gray-800 mb-4">
                  早上好，小明同学！ <span class="doodle-icon">🌅</span>
              </h2>
              <p class="handwritten text-xl text-gray-600 mb-6">
                  今天也要加油学习哦～距离考试还有 
                  <span class="inline-block bg-yellow-200 px-3 py-1 rounded-full font-bold text-orange-600 transform -rotate-1">45天</span>
              </p>
              <div class="flex justify-center space-x-6">
                  <button class="sketch-button handwritten text-lg text-gray-800">
                      🚀 开始刷题
                  </button>
                  <button class="sketch-button handwritten text-lg text-gray-800">
                      📋 查看计划
                  </button>
              </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <!-- 左侧主要内容 -->
              <div class="lg:col-span-2 space-y-8">
                  <!-- 今日学习统计 -->
                  <div class="paper-card p-6">
                      <h3 class="handwritten-title text-2xl text-gray-800 mb-6 flex items-center">
                          <span class="doodle-icon mr-3">📊</span>
                          今日学习统计
                      </h3>
                      <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                          <div class="text-center">
                              <div class="w-24 h-24 mx-auto mb-4 border-4 border-blue-400 rounded-full flex items-center justify-center bg-blue-50 transform rotate-2">
                                  <div class="text-center">
                                      <div class="handwritten-title text-2xl text-blue-600">75%</div>
                                      <div class="handwritten text-xs text-gray-600">完成度</div>
                                  </div>
                              </div>
                          </div>
                          <div class="text-center">
                              <div class="w-24 h-24 mx-auto mb-4 border-4 border-green-400 rounded-full flex items-center justify-center bg-green-50 transform -rotate-1">
                                  <div class="text-center">
                                      <div class="handwritten-title text-2xl text-green-600">120</div>
                                      <div class="handwritten text-xs text-gray-600">题数</div>
                                  </div>
                              </div>
                          </div>
                          <div class="text-center">
                              <div class="w-24 h-24 mx-auto mb-4 border-4 border-purple-400 rounded-full flex items-center justify-center bg-purple-50 transform rotate-1">
                                  <div class="text-center">
                                      <div class="handwritten-title text-2xl text-purple-600">85%</div>
                                      <div class="handwritten text-xs text-gray-600">正确率</div>
                                  </div>
                              </div>
                          </div>
                          <div class="text-center">
                              <div class="w-24 h-24 mx-auto mb-4 border-4 border-orange-400 rounded-full flex items-center justify-center bg-orange-50 transform -rotate-2">
                                  <div class="text-center">
                                      <div class="handwritten-title text-2xl text-orange-600">2.5h</div>
                                      <div class="handwritten text-xs text-gray-600">时长</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>

                  <!-- 我的小桌 -->
                  <div class="paper-card p-6">
                      <div class="flex justify-between items-center mb-6">
                          <h3 class="handwritten-title text-2xl text-gray-800 flex items-center">
                              <span class="doodle-icon mr-3">🪑</span>
                              我的小桌
                          </h3>
                          <span class="handwritten text-gray-600 cursor-pointer hover:text-gray-800">查看全部 →</span>
                      </div>
                      <div class="desk-doodle p-6">
                          <div class="flex justify-between items-center mb-4">
                              <div class="flex items-center">
                                  <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center text-white font-bold text-xl transform -rotate-3">
                                      桌
                                  </div>
                                  <div class="ml-4">
                                      <h4 class="handwritten-title text-xl text-gray-800">冲刺国考小桌</h4>
                                      <p class="handwritten text-gray-600">6/8人 · 桌长：学霸君 👑</p>
                                  </div>
                              </div>
                              <div class="text-right">
                                  <div class="handwritten-title text-lg text-gray-800">排名 #3 🥉</div>
                                  <div class="handwritten text-gray-600">本周积分：240</div>
                              </div>
                          </div>
                          <div class="flex justify-between items-center">
                              <div class="flex -space-x-2">
                                  <div class="avatar-sketch w-10 h-10 flex items-center justify-center text-lg">😎</div>
                                  <div class="avatar-sketch w-10 h-10 flex items-center justify-center text-lg">🤓</div>
                                  <div class="avatar-sketch w-10 h-10 flex items-center justify-center text-lg">😊</div>
                                  <div class="avatar-sketch w-10 h-10 flex items-center justify-center text-lg">🧐</div>
                                  <div class="avatar-sketch w-10 h-10 flex items-center justify-center text-sm handwritten font-bold">+2</div>
                              </div>
                              <button class="sketch-button handwritten text-gray-800">
                                  进入小桌 🚪
                              </button>
                          </div>
                      </div>
                  </div>

                  <!-- 最近学习记录 -->
                  <div class="paper-card p-6">
                      <h3 class="handwritten-title text-2xl text-gray-800 mb-6 flex items-center">
                          <span class="doodle-icon mr-3">📝</span>
                          最近学习记录
                      </h3>
                      <div class="space-y-4">
                          <div class="border-2 border-dashed border-blue-300 rounded-xl p-4 bg-blue-50 transform rotate-1">
                              <div class="flex justify-between items-center">
                                  <div class="flex items-center">
                                      <div class="w-12 h-12 bg-blue-200 rounded-full flex items-center justify-center mr-4 transform -rotate-2">
                                          <span class="handwritten-title text-blue-700">言</span>
                                      </div>
                                      <div>
                                          <div class="handwritten-title text-gray-800">言语理解与表达</div>
                                          <div class="handwritten text-gray-600">50题 · 正确率 88% ✨</div>
                                      </div>
                                  </div>
                                  <div class="text-right">
                                      <div class="handwritten-title text-gray-800">45分钟</div>
                                      <div class="handwritten text-gray-600">2小时前</div>
                                  </div>
                              </div>
                          </div>
                          <div class="border-2 border-dashed border-green-300 rounded-xl p-4 bg-green-50 transform -rotate-1">
                              <div class="flex justify-between items-center">
                                  <div class="flex items-center">
                                      <div class="w-12 h-12 bg-green-200 rounded-full flex items-center justify-center mr-4 transform rotate-2">
                                          <span class="handwritten-title text-green-700">数</span>
                                      </div>
                                      <div>
                                          <div class="handwritten-title text-gray-800">数量关系</div>
                                          <div class="handwritten text-gray-600">30题 · 正确率 72% 💪</div>
                                      </div>
                                  </div>
                                  <div class="text-right">
                                      <div class="handwritten-title text-gray-800">38分钟</div>
                                      <div class="handwritten text-gray-600">昨天</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- 右侧边栏 -->
              <div class="space-y-8">
                  <!-- 小桌排行榜 -->
                  <div class="paper-card p-6">
                      <h3 class="handwritten-title text-xl text-gray-800 mb-4 flex items-center">
                          <span class="doodle-icon mr-3">🏆</span>
                          小桌排行榜
                      </h3>
                      <div class="space-y-3">
                          <div class="border-2 border-yellow-300 rounded-xl p-3 bg-yellow-50 transform rotate-1">
                              <div class="flex justify-between items-center">
                                  <div class="flex items-center">
                                      <span class="ranking-medal">🥇</span>
                                      <div class="avatar-sketch w-8 h-8 ml-3 flex items-center justify-center text-sm">👑</div>
                                      <span class="ml-2 handwritten-title text-gray-800">王学霸</span>
                                  </div>
                                  <span class="handwritten-title text-yellow-600">320分</span>
                              </div>
                          </div>
                          <div class="border-2 border-gray-300 rounded-xl p-3 bg-gray-50 transform -rotate-1">
                              <div class="flex justify-between items-center">
                                  <div class="flex items-center">
                                      <span class="ranking-medal">🥈</span>
                                      <div class="avatar-sketch w-8 h-8 ml-3 flex items-center justify-center text-sm">😎</div>
                                      <span class="ml-2 handwritten-title text-gray-800">李努力</span>
                                  </div>
                                  <span class="handwritten-title text-gray-600">280分</span>
                              </div>
                          </div>
                          <div class="border-4 border-orange-400 rounded-xl p-3 bg-orange-50 transform rotate-1">
                              <div class="flex justify-between items-center">
                                  <div class="flex items-center">
                                      <span class="ranking-medal">🥉</span>
                                      <div class="avatar-sketch w-8 h-8 ml-3 flex items-center justify-center text-sm">😊</div>
                                      <span class="ml-2 handwritten-title text-gray-800">你</span>
                                  </div>
                                  <span class="handwritten-title text-orange-600">240分</span>
                              </div>
                          </div>
                      </div>
                  </div>

                  <!-- 学习提醒 -->
                  <div class="sticky-note p-6">
                      <div class="flex items-center mb-4">
                          <span class="doodle-icon mr-3 text-2xl">✨</span>
                          <span class="handwritten-title text-xl text-gray-800">今日目标</span>
                      </div>
                      <div class="handwritten text-gray-700 mb-4 space-y-2">
                          <div>📚 完成100道行测题目</div>
                          <div>⏰ 学习时长达到2小时</div>
                          <div>🎯 正确率保持80%以上</div>
                      </div>
                      <div class="progress-sketch mb-2">
                          <div class="progress-fill-sketch" style="width: 75%"></div>
                      </div>
                      <div class="handwritten text-gray-600 text-sm">进度：75% (3/4项已完成) 🎉</div>
                  </div>
              </div>
          </div>
      </div>
  </div>


</body></html>