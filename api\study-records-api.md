# 刷题记录模块 API 接口文档

## 模块概述
刷题记录模块负责用户学习数据的记录、统计和分析，包括刷题记录、错题本、学习计划等功能。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 刷题记录 | 创建学习记录 | `/api/v1/study-records` | POST | `{"moduleType": "string", "questionCount": 50, "correctCount": 42, "studyDuration": 120, "weakPoints": ["知识点1", "知识点2"], "studyDate": "2024-01-15", "notes": "string?"}` | `{"code": 200, "message": "记录创建成功", "data": {"id": "uuid", "accuracyRate": 84.0}}` | user | 自动计算正确率 |
| 刷题记录 | 获取学习记录列表 | `/api/v1/study-records` | GET | `page=1&size=20&moduleType=string&startDate=date&endDate=date&sortBy=studyDate&sortOrder=desc` | `{"code": 200, "data": {"records": [...], "total": 100, "current": 1, "size": 20}}` | user | 只能查看自己的记录 |
| 刷题记录 | 获取学习记录详情 | `/api/v1/study-records/{recordId}` | GET | 路径参数: `recordId` | `{"code": 200, "data": {"id": "uuid", "moduleType": "math", "questionCount": 50, "correctCount": 42, "accuracyRate": 84.0, "studyDuration": 120, "weakPoints": [...], "studyDate": "2024-01-15", "notes": "string", "createdAt": "datetime"}}` | user | 只能查看自己的记录 |
| 刷题记录 | 更新学习记录 | `/api/v1/study-records/{recordId}` | PUT | `{"notes": "string?", "weakPoints": ["知识点1", "知识点2"]}` | `{"code": 200, "message": "更新成功"}` | user | 只能更新备注和薄弱点 |
| 刷题记录 | 删除学习记录 | `/api/v1/study-records/{recordId}` | DELETE | 路径参数: `recordId` | `{"code": 200, "message": "删除成功"}` | user | 软删除，保留统计数据 |
| 学习统计 | 获取学习统计概览 | `/api/v1/study-records/stats/overview` | GET | `period=week|month|year` | `{"code": 200, "data": {"totalQuestions": 1000, "totalCorrect": 800, "averageAccuracy": 80.0, "totalStudyTime": 3600, "studyDays": 15, "currentStreak": 5}}` | user | 个人学习数据统计 |
| 学习统计 | 获取模块统计 | `/api/v1/study-records/stats/modules` | GET | `startDate=date&endDate=date` | `{"code": 200, "data": [{"moduleType": "math", "questionCount": 500, "correctCount": 400, "accuracyRate": 80.0, "studyTime": 1800}]}` | user | 按模块分类统计 |
| 学习统计 | 获取学习趋势 | `/api/v1/study-records/stats/trends` | GET | `period=week|month&granularity=day|week` | `{"code": 200, "data": [{"date": "2024-01-15", "questionCount": 50, "accuracyRate": 84.0, "studyTime": 120}]}` | user | 学习趋势数据 |
| 学习统计 | 获取薄弱知识点 | `/api/v1/study-records/stats/weak-points` | GET | `limit=10&moduleType=string` | `{"code": 200, "data": [{"point": "知识点1", "frequency": 15, "lastAppeared": "2024-01-15"}]}` | user | 薄弱知识点统计 |
| 错题本 | 添加错题 | `/api/v1/wrong-questions` | POST | `{"studyRecordId": "uuid?", "questionType": "single_choice", "difficultyLevel": "medium", "questionContent": "题目内容", "userAnswer": "A", "correctAnswer": "B", "explanation": "解析内容"}` | `{"code": 200, "message": "错题添加成功", "data": {"id": "uuid"}}` | user | 可关联学习记录 |
| 错题本 | 获取错题列表 | `/api/v1/wrong-questions` | GET | `page=1&size=20&questionType=string&difficultyLevel=string&masteryStatus=string&sortBy=createdAt` | `{"code": 200, "data": {"records": [...], "total": 50}}` | user | 支持多种筛选条件 |
| 错题本 | 获取错题详情 | `/api/v1/wrong-questions/{questionId}` | GET | 路径参数: `questionId` | `{"code": 200, "data": {"id": "uuid", "questionType": "single_choice", "difficultyLevel": "medium", "questionContent": "题目内容", "userAnswer": "A", "correctAnswer": "B", "explanation": "解析", "masteryStatus": "not_mastered", "reviewCount": 3, "createdAt": "datetime", "reviewedAt": "datetime"}}` | user | 错题详细信息 |
| 错题本 | 更新错题状态 | `/api/v1/wrong-questions/{questionId}` | PUT | `{"masteryStatus": "mastered|reviewing|not_mastered", "userAnswer": "string?", "explanation": "string?"}` | `{"code": 200, "message": "更新成功"}` | user | 更新掌握状态 |
| 错题本 | 标记错题复习 | `/api/v1/wrong-questions/{questionId}/review` | POST | `{}` | `{"code": 200, "message": "复习记录成功"}` | user | 增加复习次数 |
| 错题本 | 删除错题 | `/api/v1/wrong-questions/{questionId}` | DELETE | 路径参数: `questionId` | `{"code": 200, "message": "删除成功"}` | user | 物理删除 |
| 错题本 | 获取错题统计 | `/api/v1/wrong-questions/stats` | GET | `moduleType=string&period=week|month` | `{"code": 200, "data": {"totalCount": 100, "masteredCount": 60, "reviewingCount": 30, "notMasteredCount": 10, "masteryRate": 60.0}}` | user | 错题掌握情况统计 |
| 学习计划 | 创建学习计划 | `/api/v1/study-plans` | POST | `{"title": "备考计划", "description": "详细描述", "targetExamDate": "2024-06-15", "dailyTargetQuestions": 100, "dailyTargetTime": 180, "modules": [{"type": "math", "weight": 0.4}, {"type": "logic", "weight": 0.3}]}` | `{"code": 200, "message": "计划创建成功", "data": {"id": "uuid"}}` | user | JSON存储模块配置 |
| 学习计划 | 获取学习计划列表 | `/api/v1/study-plans` | GET | `page=1&size=20&status=active|completed|paused` | `{"code": 200, "data": {"records": [...], "total": 10}}` | user | 个人学习计划列表 |
| 学习计划 | 获取学习计划详情 | `/api/v1/study-plans/{planId}` | GET | 路径参数: `planId` | `{"code": 200, "data": {"id": "uuid", "title": "备考计划", "description": "详细描述", "targetExamDate": "2024-06-15", "dailyTargetQuestions": 100, "dailyTargetTime": 180, "modules": [...], "status": "active", "progress": 65.5, "createdAt": "datetime"}}` | user | 包含进度计算 |
| 学习计划 | 更新学习计划 | `/api/v1/study-plans/{planId}` | PUT | `{"title": "string?", "description": "string?", "targetExamDate": "date?", "dailyTargetQuestions": 100, "dailyTargetTime": 180, "modules": [...], "status": "active|completed|paused"}` | `{"code": 200, "message": "更新成功"}` | user | 可更新所有字段 |
| 学习计划 | 删除学习计划 | `/api/v1/study-plans/{planId}` | DELETE | 路径参数: `planId` | `{"code": 200, "message": "删除成功"}` | user | 软删除 |
| 学习计划 | 获取计划执行情况 | `/api/v1/study-plans/{planId}/progress` | GET | `startDate=date&endDate=date` | `{"code": 200, "data": {"targetDays": 30, "actualDays": 25, "completionRate": 83.3, "dailyProgress": [{"date": "2024-01-15", "targetQuestions": 100, "actualQuestions": 85, "targetTime": 180, "actualTime": 150}]}}` | user | 计划执行详情 |

## 数据模型

### 学习记录模型 (StudyRecord)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "moduleType": "math|logic|language|knowledge|essay",
  "questionCount": 50,
  "correctCount": 42,
  "accuracyRate": 84.0,
  "studyDuration": 120,
  "weakPoints": ["知识点1", "知识点2"],
  "studyDate": "2024-01-15",
  "notes": "学习笔记",
  "createdAt": "datetime"
}
```

### 错题模型 (WrongQuestion)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "studyRecordId": "uuid",
  "questionType": "single_choice|multiple_choice|judgment|fill_blank|essay",
  "difficultyLevel": "easy|medium|hard",
  "questionContent": "题目内容",
  "userAnswer": "用户答案",
  "correctAnswer": "正确答案",
  "explanation": "解析内容",
  "masteryStatus": "not_mastered|reviewing|mastered",
  "reviewCount": 3,
  "createdAt": "datetime",
  "reviewedAt": "datetime"
}
```

### 学习计划模型 (StudyPlan)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "title": "备考计划",
  "description": "详细描述",
  "targetExamDate": "2024-06-15",
  "dailyTargetQuestions": 100,
  "dailyTargetTime": 180,
  "modules": [
    {"type": "math", "weight": 0.4},
    {"type": "logic", "weight": 0.3}
  ],
  "status": "active|completed|paused",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```
