package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.entity.ScheduledTaskLog;
import com.wqh.publicexaminationassistant.mapper.ScheduledTaskLogMapper;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.RankingCalculationTask;
import com.wqh.publicexaminationassistant.service.RankingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collections;

/**
 * 排行榜管理控制器
 * 提供排行榜计算任务的管理和监控功能
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/v1/rankings/management")
@RequiredArgsConstructor
@Tag(name = "排行榜管理", description = "排行榜计算任务管理接口")
public class RankingManagementController {

    private final RankingCalculationTask rankingCalculationTask;
    private final ScheduledTaskLogMapper scheduledTaskLogMapper;
    private final RankingService rankingService;

    /**
     * 手动触发排行榜计算
     */
    @PostMapping("/trigger")
    @Operation(summary = "手动触发排行榜计算", description = "立即执行所有排行榜的计算任务")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> triggerRankingCalculation(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户手动触发排行榜计算: userId={}", userId);

        try {
            int totalUpdated = rankingCalculationTask.manualCalculateRankings();

            Map<String, Object> result = new HashMap<>();
            result.put("totalUpdated", totalUpdated);
            result.put("triggeredBy", userId);
            result.put("triggeredAt", java.time.LocalDateTime.now());
            result.put("stats", rankingCalculationTask.getRankingCalculationStats());

            return ApiResponse.success(result, "排行榜计算完成");
        } catch (Exception e) {
            log.error("手动触发排行榜计算失败", e);
            return ApiResponse.error("排行榜计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算指定排行榜
     */
    @PostMapping("/calculate-specific")
    @Operation(summary = "计算指定排行榜", description = "计算指定类型和周期的排行榜")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> calculateSpecificRanking(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户计算指定排行榜: userId={}, type={}, period={}", userId, type, period);

        try {
            int updated = rankingCalculationTask.calculateSpecificRanking(type, period);

            Map<String, Object> result = new HashMap<>();
            result.put("type", type);
            result.put("period", period);
            result.put("updated", updated);
            result.put("triggeredBy", userId);
            result.put("triggeredAt", java.time.LocalDateTime.now());

            return ApiResponse.success(result, "指定排行榜计算完成");
        } catch (Exception e) {
            log.error("计算指定排行榜失败: type={}, period={}", type, period, e);
            return ApiResponse.error("计算指定排行榜失败: " + e.getMessage());
        }
    }

    /**
     * 获取排行榜计算任务历史
     */
    @GetMapping("/task-history")
    @Operation(summary = "获取任务历史", description = "获取排行榜计算任务的执行历史")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<List<ScheduledTaskLog>> getTaskHistory(
            @Parameter(description = "开始日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @Parameter(description = "记录数量限制") @RequestParam(defaultValue = "20") int limit,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        try {
            LocalDate finalStartDate = startDate != null ? startDate : LocalDate.now().minusDays(7);
            LocalDate finalEndDate = endDate != null ? endDate : LocalDate.now();

            // 这里需要在ScheduledTaskLogMapper中添加按任务名称和日期范围查询的方法
            // 暂时返回空列表 (JDK 1.8兼容)
            List<ScheduledTaskLog> taskHistory = Collections.emptyList();

            return ApiResponse.success(taskHistory, "获取任务历史成功");
        } catch (Exception e) {
            log.error("获取任务历史失败", e);
            return ApiResponse.error("获取任务历史失败");
        }
    }

    /**
     * 获取排行榜计算统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取计算统计", description = "获取排行榜计算的统计信息")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> getCalculationStats(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        try {
            Map<String, Object> stats = new HashMap<>();
            stats.put("calculationStats", rankingCalculationTask.getRankingCalculationStats());
            stats.put("lastCalculationTime", getLastCalculationTime());
            stats.put("nextScheduledTime", getNextScheduledTime());
            stats.put("taskStatus", getTaskStatus());

            return ApiResponse.success(stats, "获取统计信息成功");
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return ApiResponse.error("获取统计信息失败");
        }
    }

    /**
     * 检查排行榜计算任务状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查任务状态", description = "检查排行榜计算任务的当前状态")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> getTaskStatus(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        try {
            Map<String, Object> status = new HashMap<>();
            
            // 检查今日是否已执行
            boolean isCompletedToday = scheduledTaskLogMapper.isTaskCompletedToday(
                    ScheduledTaskLog.TaskName.RANKING_CALCULATION, LocalDate.now());
            
            // 获取最新的任务记录
            ScheduledTaskLog latestLog = scheduledTaskLogMapper.selectByTaskAndDate(
                    ScheduledTaskLog.TaskName.RANKING_CALCULATION, LocalDate.now());

            status.put("isCompletedToday", isCompletedToday);
            status.put("latestLog", latestLog);
            status.put("scheduledTime", "每日凌晨2:00");
            status.put("cleanupTime", "每日凌晨3:30");
            status.put("currentTime", java.time.LocalDateTime.now());

            return ApiResponse.success(status, "获取任务状态成功");
        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            return ApiResponse.error("获取任务状态失败");
        }
    }

    /**
     * 获取最后计算时间
     */
    private String getLastCalculationTime() {
        try {
            ScheduledTaskLog latestLog = scheduledTaskLogMapper.selectByTaskAndDate(
                    ScheduledTaskLog.TaskName.RANKING_CALCULATION, LocalDate.now());
            return latestLog != null ? latestLog.getStartTime().toString() : "未执行";
        } catch (Exception e) {
            return "获取失败";
        }
    }

    /**
     * 获取下次调度时间
     */
    private String getNextScheduledTime() {
        // 下次执行时间是明天凌晨2点
        return LocalDate.now().plusDays(1).atTime(2, 0).toString();
    }

    /**
     * 获取任务状态
     */
    private String getTaskStatus() {
        try {
            boolean isCompletedToday = scheduledTaskLogMapper.isTaskCompletedToday(
                    ScheduledTaskLog.TaskName.RANKING_CALCULATION, LocalDate.now());
            return isCompletedToday ? "已完成" : "待执行";
        } catch (Exception e) {
            return "状态未知";
        }
    }

    /**
     * 清除排行榜缓存
     */
    @PostMapping("/clear-cache")
    @Operation(summary = "清除排行榜缓存", description = "清除所有排行榜相关的Redis缓存")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> clearRankingCache(
            @Parameter(description = "排行榜类型（可选）") @RequestParam(required = false) String type,
            @Parameter(description = "时间周期（可选）") @RequestParam(required = false) String period,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户清除排行榜缓存: userId={}, type={}, period={}", userId, type, period);

        try {
            if (type != null && period != null) {
                // 清除指定类型和周期的缓存
                rankingService.clearRankingCache(type, period);
            } else {
                // 清除所有排行榜缓存
                rankingService.clearAllRankingCache();
            }

            Map<String, Object> result = new HashMap<>();
            result.put("clearedBy", userId);
            result.put("clearedAt", java.time.LocalDateTime.now());
            result.put("type", type);
            result.put("period", period);
            result.put("scope", (type != null && period != null) ? "指定类型" : "全部缓存");

            return ApiResponse.success(result, "排行榜缓存清除成功");
        } catch (Exception e) {
            log.error("清除排行榜缓存失败", e);
            return ApiResponse.error("清除排行榜缓存失败: " + e.getMessage());
        }
    }
}
