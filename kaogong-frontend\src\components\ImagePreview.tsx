import React, { useState } from 'react';
import { 
  Modal, 
  Button, 
  Space, 
  Typography, 
  Image,
  Card,
  Tooltip
} from '@douyinfe/semi-ui';
import {
  IconArrowLeft,
  IconArrowRight,
  IconDownload
} from '@douyinfe/semi-icons';

const { Text } = Typography;

// 动态图标组件
const DynamicIcon: React.FC<{ name: string; [key: string]: any }> = ({ name, ...props }) => {
  const [IconComponent, setIconComponent] = React.useState<React.ComponentType<any> | null>(null);

  React.useEffect(() => {
    const loadIcon = async () => {
      try {
        const iconModule = await import('@douyinfe/semi-icons');
        const component = (iconModule as any)[name];
        if (component) {
          setIconComponent(() => component);
        }
      } catch (error) {
        console.error(`Failed to load icon: ${name}`, error);
      }
    };

    loadIcon();
  }, [name]);

  if (!IconComponent) {
    return null;
  }

  return React.createElement(IconComponent, props);
};

export interface ImageInfo {
  id: string;
  imageType: 'question' | 'answer' | 'explanation';
  fileName: string;
  fileUrl: string;
  fileSize: number;
  width?: number;
  height?: number;
  sortOrder: number;
  thumbnailUrl?: string;
  createdAt: string;
}

interface ImagePreviewProps {
  images: ImageInfo[];
  editable?: boolean;
  onDelete?: (imageId: string) => void;
  onReorder?: (images: ImageInfo[]) => void;
  className?: string;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  images = [],
  editable = false,
  onDelete,
  onReorder,
  className = ''
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // 图片类型映射
  const imageTypeMap = {
    question: '题目图片',
    answer: '答案图片',
    explanation: '解析图片'
  };

  // 按类型分组图片
  const groupedImages = images.reduce((groups, image) => {
    if (!groups[image.imageType]) {
      groups[image.imageType] = [];
    }
    groups[image.imageType].push(image);
    return groups;
  }, {} as Record<string, ImageInfo[]>);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 打开图片预览
  const handleImageClick = (imageIndex: number) => {
    setCurrentImageIndex(imageIndex);
    setPreviewVisible(true);
  };

  // 切换到上一张图片
  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  // 切换到下一张图片
  const handleNextImage = () => {
    setCurrentImageIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  // 下载图片
  const handleDownloadImage = (image: ImageInfo) => {
    const link = document.createElement('a');
    link.href = image.fileUrl;
    link.download = image.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 删除图片
  const handleDeleteImage = (imageId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这张图片吗？删除后无法恢复。',
      onOk: () => {
        onDelete?.(imageId);
      }
    });
  };

  if (images.length === 0) {
    return (
      <div className={`image-preview-empty ${className}`}>
        <Text type="tertiary">暂无图片</Text>
      </div>
    );
  }

  return (
    <div className={`image-preview-container ${className}`}>
      {/* 按类型显示图片 */}
      {Object.entries(groupedImages).map(([imageType, typeImages]) => (
        <div key={imageType} style={{ marginBottom: '20px' }}>
          <div style={{ marginBottom: '8px' }}>
            <Text strong>{imageTypeMap[imageType as keyof typeof imageTypeMap]}</Text>
            <Text type="tertiary" size="small" style={{ marginLeft: '8px' }}>
              ({typeImages.length} 张)
            </Text>
          </div>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
            gap: '12px'
          }}>
            {typeImages
              .sort((a, b) => a.sortOrder - b.sortOrder)
              .map((image, index) => {
                const globalIndex = images.findIndex(img => img.id === image.id);
                return (
                  <Card
                    key={image.id}
                    style={{ 
                      position: 'relative',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                    bodyStyle={{ padding: '8px' }}
                    hoverable
                    onClick={() => handleImageClick(globalIndex)}
                  >
                    {/* 图片 */}
                    <div style={{ 
                      width: '100%', 
                      height: '100px', 
                      overflow: 'hidden',
                      borderRadius: '4px',
                      position: 'relative'
                    }}>
                      <img
                        src={image.thumbnailUrl || image.fileUrl}
                        alt={image.fileName}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                        loading="lazy"
                      />
                      
                      {/* 悬浮操作按钮 */}
                      <div 
                        className="image-overlay"
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          backgroundColor: 'rgba(0, 0, 0, 0.5)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          opacity: 0,
                          transition: 'opacity 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.opacity = '1';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.opacity = '0';
                        }}
                      >
                        <Space>
                          <Tooltip content="查看大图">
                            <Button
                              icon={<DynamicIcon name="IconEyeOpened" />}
                              theme="borderless"
                              style={{ color: 'white' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleImageClick(globalIndex);
                              }}
                            />
                          </Tooltip>
                          
                          <Tooltip content="下载">
                            <Button
                              icon={<IconDownload />}
                              theme="borderless"
                              style={{ color: 'white' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownloadImage(image);
                              }}
                            />
                          </Tooltip>
                          
                          {editable && (
                            <Tooltip content="删除">
                              <Button
                                icon={<DynamicIcon name="IconDelete" />}
                                theme="borderless"
                                style={{ color: 'white' }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteImage(image.id);
                                }}
                              />
                            </Tooltip>
                          )}
                        </Space>
                      </div>
                    </div>

                    {/* 图片信息 */}
                    <div style={{ marginTop: '8px' }}>
                      <Text size="small" ellipsis={{ showTooltip: true }}>
                        {image.fileName}
                      </Text>
                      <div style={{ marginTop: '2px' }}>
                        <Text size="small" type="tertiary">
                          {formatFileSize(image.fileSize)}
                          {image.width && image.height && (
                            <span> • {image.width}×{image.height}</span>
                          )}
                        </Text>
                      </div>
                    </div>
                  </Card>
                );
              })}
          </div>
        </div>
      ))}

      {/* 图片预览模态框 */}
      <Modal
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width="90vw"
        style={{ maxWidth: '1200px' }}
        bodyStyle={{ 
          padding: '20px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}
        closeOnEsc
      >
        {images[currentImageIndex] && (
          <>
            {/* 图片导航 */}
            <div style={{ 
              width: '100%', 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <Button
                icon={<IconArrowLeft />}
                onClick={handlePrevImage}
                disabled={images.length <= 1}
              >
                上一张
              </Button>
              
              <div style={{ textAlign: 'center' }}>
                <Text strong>{images[currentImageIndex].fileName}</Text>
                <div>
                  <Text type="tertiary" size="small">
                    {currentImageIndex + 1} / {images.length}
                  </Text>
                </div>
              </div>
              
              <Button
                icon={<IconArrowRight />}
                iconPosition="right"
                onClick={handleNextImage}
                disabled={images.length <= 1}
              >
                下一张
              </Button>
            </div>

            {/* 大图显示 */}
            <div style={{ 
              maxWidth: '100%', 
              maxHeight: '70vh', 
              overflow: 'hidden',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}>
              <img
                src={images[currentImageIndex].fileUrl}
                alt={images[currentImageIndex].fileName}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
            </div>

            {/* 图片信息 */}
            <div style={{ 
              marginTop: '20px', 
              textAlign: 'center',
              width: '100%'
            }}>
              <Space direction="vertical" align="center">
                <Text type="tertiary">
                  {formatFileSize(images[currentImageIndex].fileSize)}
                  {images[currentImageIndex].width && images[currentImageIndex].height && (
                    <span> • {images[currentImageIndex].width}×{images[currentImageIndex].height}</span>
                  )}
                </Text>
                
                <Space>
                  <Button
                    icon={<IconDownload />}
                    onClick={() => handleDownloadImage(images[currentImageIndex])}
                  >
                    下载
                  </Button>
                  
                  {editable && (
                    <Button
                      type="danger"
                      icon={<DynamicIcon name="IconDelete" />}
                      onClick={() => {
                        handleDeleteImage(images[currentImageIndex].id);
                        setPreviewVisible(false);
                      }}
                    >
                      删除
                    </Button>
                  )}
                </Space>
              </Space>
            </div>
          </>
        )}
      </Modal>
    </div>
  );
};

export default ImagePreview;
