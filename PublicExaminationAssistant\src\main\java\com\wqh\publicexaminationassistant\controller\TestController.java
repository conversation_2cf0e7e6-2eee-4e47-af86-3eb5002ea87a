package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于验证系统基本功能和API文档
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Tag(name = "系统测试", description = "系统基本功能测试接口")
@RestController
@RequestMapping("/v1/test")
@RequiredArgsConstructor
@Slf4j
public class TestController extends BaseController {

    private final DataSource dataSource;
    private final UserMapper userMapper;

    /**
     * 系统健康检查
     */
    @Operation(summary = "系统健康检查", description = "检查系统是否正常运行")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "考公刷题记录系统");
        healthInfo.put("version", "1.0.0");
        
        return success(healthInfo, "系统运行正常");
    }

    /**
     * 获取系统信息
     */
    @Operation(summary = "获取系统信息", description = "获取系统基本信息和配置")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("applicationName", "考公刷题记录系统");
        systemInfo.put("version", "1.0.0");
        systemInfo.put("description", "基于小桌系统和信誉体系的考公刷题记录平台");
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("springBootVersion", "2.6.13");
        systemInfo.put("buildTime", LocalDateTime.now());
        
        return success(systemInfo);
    }

    /**
     * 测试认证接口
     */
    @Operation(summary = "测试认证接口", description = "需要认证才能访问的测试接口")
    @GetMapping("/auth-test")
    public ApiResponse<Map<String, Object>> authTest() {
        Map<String, Object> authInfo = new HashMap<>();
        authInfo.put("message", "认证测试成功");
        authInfo.put("userId", getCurrentUserId());
        authInfo.put("username", getCurrentUsername());
        authInfo.put("timestamp", LocalDateTime.now());
        
        return success(authInfo, "认证测试通过");
    }

    /**
     * 测试管理员接口
     */
    @Operation(summary = "测试管理员接口", description = "需要管理员权限才能访问的测试接口")
    @GetMapping("/admin-test")
    public ApiResponse<Map<String, Object>> adminTest() {
        log.info("访问管理员测试接口");

        // 开发测试阶段，暂时注释掉权限检查
        // if (!isAdmin()) {
        //     return error("权限不足，需要管理员权限");
        // }

        Map<String, Object> adminInfo = new HashMap<>();
        adminInfo.put("message", "管理员权限测试成功");
        adminInfo.put("role", "admin");
        adminInfo.put("userId", getCurrentUserId());
        adminInfo.put("username", getCurrentUsername());
        adminInfo.put("isAdmin", isAdmin());
        adminInfo.put("isSuperAdmin", isSuperAdmin());
        adminInfo.put("timestamp", LocalDateTime.now());

        log.info("管理员测试接口返回成功");
        return success(adminInfo, "管理员权限测试通过");
    }

    /**
     * 数据库连接测试
     */
    @Operation(summary = "数据库连接测试", description = "测试数据库连接是否正常")
    @GetMapping("/db-connection")
    public ApiResponse<Map<String, Object>> testDatabaseConnection() {
        Map<String, Object> dbInfo = new HashMap<>();

        try {
            // 测试数据源连接
            try (Connection connection = dataSource.getConnection()) {
                dbInfo.put("connectionStatus", "SUCCESS");
                dbInfo.put("databaseUrl", connection.getMetaData().getURL());
                dbInfo.put("databaseProduct", connection.getMetaData().getDatabaseProductName());
                dbInfo.put("databaseVersion", connection.getMetaData().getDatabaseProductVersion());
                dbInfo.put("driverName", connection.getMetaData().getDriverName());
                dbInfo.put("driverVersion", connection.getMetaData().getDriverVersion());
            }

            // 测试MyBatis Plus查询
            Long userCount = userMapper.selectCount(null);
            dbInfo.put("userTableCount", userCount);
            dbInfo.put("mybatisPlusStatus", "SUCCESS");

            dbInfo.put("timestamp", LocalDateTime.now());

            return success(dbInfo, "数据库连接测试成功");

        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            dbInfo.put("connectionStatus", "FAILED");
            dbInfo.put("errorMessage", e.getMessage());
            dbInfo.put("timestamp", LocalDateTime.now());

            return ApiResponse.error("数据库连接测试失败：" + e.getMessage());
        }
    }

    /**
     * 字符编码测试
     */
    @Operation(summary = "字符编码测试", description = "测试数据库中文字符存储和读取")
    @PostMapping("/charset")
    public ApiResponse<Map<String, Object>> testCharset() {
        Map<String, Object> charsetInfo = new HashMap<>();

        try {
            // 创建测试用户验证中文字符
            User testUser = new User();
            testUser.setUsername("测试用户_" + System.currentTimeMillis());
            testUser.setNickname("中文昵称测试🎯");
            testUser.setEmail("<EMAIL>");
            testUser.setPasswordHash("test_password");
            testUser.setReputationScore(100);
            testUser.setReputationLevel("newbie");
            testUser.setSystemRole("user");
            testUser.setIsActive(true);

            // 插入测试数据
            userMapper.insert(testUser);
            charsetInfo.put("insertStatus", "SUCCESS");
            charsetInfo.put("insertedUserId", testUser.getId());

            // 查询验证中文字符
            User savedUser = userMapper.selectById(testUser.getId());
            charsetInfo.put("retrievedUsername", savedUser.getUsername());
            charsetInfo.put("retrievedNickname", savedUser.getNickname());
            charsetInfo.put("charsetTestResult", "SUCCESS");

            // 清理测试数据
            userMapper.deleteById(testUser.getId());
            charsetInfo.put("cleanupStatus", "SUCCESS");

            charsetInfo.put("timestamp", LocalDateTime.now());

            return success(charsetInfo, "字符编码测试成功");

        } catch (Exception e) {
            log.error("字符编码测试失败", e);
            charsetInfo.put("charsetTestResult", "FAILED");
            charsetInfo.put("errorMessage", e.getMessage());
            charsetInfo.put("timestamp", LocalDateTime.now());

            return ApiResponse.error("字符编码测试失败：" + e.getMessage());
        }
    }

    /**
     * JWT Token调试
     */
    @Operation(summary = "JWT Token调试", description = "调试JWT Token解析和用户认证信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/jwt-debug")
    public ApiResponse<Map<String, Object>> jwtDebug(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            debugInfo.put("userDetailsNull", userDetails == null);

            if (userDetails != null) {
                debugInfo.put("userId", userDetails.getId());
                debugInfo.put("username", userDetails.getUsername());
                debugInfo.put("authorities", userDetails.getAuthorities());
                debugInfo.put("enabled", userDetails.isEnabled());
                debugInfo.put("accountNonExpired", userDetails.isAccountNonExpired());
                debugInfo.put("accountNonLocked", userDetails.isAccountNonLocked());
                debugInfo.put("credentialsNonExpired", userDetails.isCredentialsNonExpired());
            }

            debugInfo.put("timestamp", LocalDateTime.now());

            return success(debugInfo, "JWT Token调试信息");

        } catch (Exception e) {
            log.error("JWT Token调试失败", e);
            debugInfo.put("error", e.getMessage());
            debugInfo.put("timestamp", LocalDateTime.now());

            return ApiResponse.error("JWT Token调试失败：" + e.getMessage());
        }
    }

    /**
     * 时间戳转换测试
     */
    @Operation(summary = "时间戳转换测试", description = "测试Timestamp到LocalDateTime的转换")
    @GetMapping("/timestamp-conversion")
    public ApiResponse<Map<String, Object>> testTimestampConversion() {
        Map<String, Object> data = new HashMap<>();

        try {
            // 模拟数据库返回的Timestamp
            java.sql.Timestamp timestamp = new java.sql.Timestamp(System.currentTimeMillis());

            // 测试转换
            LocalDateTime localDateTime = timestamp.toLocalDateTime();

            data.put("originalTimestamp", timestamp.toString());
            data.put("convertedLocalDateTime", localDateTime.toString());
            data.put("conversionSuccess", true);
            data.put("timestamp", LocalDateTime.now());

            return success(data, "时间戳转换测试成功");

        } catch (Exception e) {
            log.error("时间戳转换测试失败", e);
            data.put("conversionSuccess", false);
            data.put("errorMessage", e.getMessage());
            data.put("timestamp", LocalDateTime.now());

            return ApiResponse.error("时间戳转换测试失败：" + e.getMessage());
        }
    }
}
