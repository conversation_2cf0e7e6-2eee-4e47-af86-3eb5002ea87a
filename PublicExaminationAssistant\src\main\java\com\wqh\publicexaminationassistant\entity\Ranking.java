package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 排行榜表实体类
 * 存储各种维度的排行榜数据
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("rankings")
public class Ranking implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 排行记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 小桌ID(NULL为全站排行)
     */
    @TableField("desk_id")
    private String deskId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 排行类型
     */
    @TableField("ranking_type")
    private String rankingType;

    /**
     * 排行分数
     */
    @TableField("score")
    private Integer score;

    /**
     * 排名位置
     */
    @TableField("rank_position")
    private Integer rankPosition;

    /**
     * 计算时间
     */
    @TableField(value = "calculated_at", fill = FieldFill.INSERT)
    private LocalDateTime calculatedAt;
}
