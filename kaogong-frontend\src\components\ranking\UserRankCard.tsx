import React from 'react';
import { Card, Typography, Space, Tag, Tooltip, Progress, Skeleton } from '@douyinfe/semi-ui';
import { IconArrowUp, IconArrowDown, IconMinus, IconUser, IconStar } from '@douyinfe/semi-icons';
import { UserRankResponse, RankingType, rankingUtils } from '../../services/rankingService';
import './UserRankCard.css';

const { Title, Text } = Typography;

interface UserRankCardProps {
  userRank?: UserRankResponse;
  loading?: boolean;
  type: RankingType;
}

const UserRankCard: React.FC<UserRankCardProps> = ({
  userRank,
  loading = false,
  type
}) => {
  if (loading) {
    return (
      <Card className="user-rank-card" bodyStyle={{ padding: '24px' }}>
        <Skeleton placeholder={<Skeleton.Title />} loading={true}>
          <div style={{ height: '200px' }} />
        </Skeleton>
      </Card>
    );
  }

  if (!userRank || !userRank.currentRank) {
    return (
      <Card className="user-rank-card no-rank" bodyStyle={{ padding: '24px' }}>
        <div className="no-rank-content">
          <IconUser size="large" style={{ color: 'var(--ink-medium)', marginBottom: '12px' }} />
          <Title heading={5} style={{ margin: '0 0 8px 0', color: 'var(--ink-medium)' }}>
            暂未上榜
          </Title>
          <Text type="tertiary">
            继续努力学习，争取早日上榜！
          </Text>
        </div>
      </Card>
    );
  }

  const getRankChangeIcon = () => {
    if (!userRank.rankChange || userRank.rankChange === 0) {
      return <IconMinus style={{ color: '#666' }} />;
    }
    return userRank.rankChange > 0 ?
      <IconArrowUp style={{ color: '#52c41a' }} /> :
      <IconArrowDown style={{ color: '#ff4d4f' }} />;
  };

  const getRankChangeText = () => {
    if (!userRank.rankChange || userRank.rankChange === 0) {
      return '无变化';
    }
    const change = Math.abs(userRank.rankChange);
    return userRank.rankChange > 0 ? `上升 ${change} 位` : `下降 ${change} 位`;
  };

  const getRankLevelColor = () => {
    return rankingUtils.getRankLevelColor(userRank.currentRank, userRank.totalParticipants);
  };

  const getPercentileText = () => {
    return `超越了 ${userRank.percentile.toFixed(1)}% 的用户`;
  };

  return (
    <Card className="user-rank-card" bodyStyle={{ padding: '24px' }}>
      {/* 装饰性元素 */}
      <div className="rank-decoration">
        <IconStar className="star star-1" />
        <IconStar className="star star-2" />
        <IconStar className="star star-3" />
      </div>

      {/* 排名标题 */}
      <div className="rank-header">
        <Title heading={4} style={{ margin: 0, color: 'var(--ink-dark)' }}>
          🏆 我的排名
        </Title>
        <Tag 
          color={getRankLevelColor()} 
          size="large"
          style={{ fontFamily: 'var(--font-handwritten)', fontWeight: 'bold' }}
        >
          {userRank.rankLevel}
        </Tag>
      </div>

      {/* 主要排名信息 */}
      <div className="rank-main">
        <div className="rank-number">
          <span className="rank-value">#{userRank.currentRank}</span>
          <span className="rank-total">/ {userRank.totalParticipants.toLocaleString()}</span>
        </div>
        
        <div className="rank-change">
          {getRankChangeIcon()}
          <Text 
            type={userRank.rankChange && userRank.rankChange !== 0 ? 'secondary' : 'tertiary'}
            style={{ marginLeft: '4px' }}
          >
            {getRankChangeText()}
          </Text>
        </div>
      </div>

      {/* 分数信息 */}
      <div className="score-info">
        <div className="score-main">
          <Text strong style={{ fontSize: '24px', color: 'var(--accent-blue)' }}>
            {rankingUtils.formatScore(userRank.score, type)}
          </Text>
          <Text type="tertiary" style={{ marginLeft: '8px' }}>
            当前分数
          </Text>
        </div>
        
        {/* 分数差距 */}
        <Space>
          {userRank.scoreGapToNext && (
            <Tooltip content="距离上一名的分数差距">
              <Text size="small" type="tertiary">
                ⬆️ 差距 {userRank.scoreGapToNext}
              </Text>
            </Tooltip>
          )}
          {userRank.scoreGapToPrevious && (
            <Tooltip content="领先下一名的分数">
              <Text size="small" type="tertiary">
                ⬇️ 领先 {userRank.scoreGapToPrevious}
              </Text>
            </Tooltip>
          )}
        </Space>
      </div>

      {/* 百分位进度条 */}
      <div className="percentile-progress">
        <div className="progress-header">
          <Text strong>排名百分位</Text>
          <Text type="secondary" size="small">
            {getPercentileText()}
          </Text>
        </div>
        <Progress
          percent={userRank.percentile}
          showInfo={false}
          stroke={getRankLevelColor()}
          style={{ marginTop: '8px' }}
        />
      </div>

      {/* 详细统计 */}
      <div className="detail-stats">
        <Title heading={6} style={{ margin: '16px 0 8px 0' }}>
          详细数据
        </Title>
        <div className="stats-grid">
          {userRank.detailStats.totalQuestions !== undefined && (
            <div className="stat-item">
              <Text type="tertiary" size="small">题目数</Text>
              <Text strong>{userRank.detailStats.totalQuestions.toLocaleString()}</Text>
            </div>
          )}
          {userRank.detailStats.accuracyRate !== undefined && (
            <div className="stat-item">
              <Text type="tertiary" size="small">正确率</Text>
              <Text strong>{userRank.detailStats.accuracyRate.toFixed(1)}%</Text>
            </div>
          )}
          {userRank.detailStats.studyTime !== undefined && (
            <div className="stat-item">
              <Text type="tertiary" size="small">学习时长</Text>
              <Text strong>{Math.floor(userRank.detailStats.studyTime / 60)}h{userRank.detailStats.studyTime % 60}m</Text>
            </div>
          )}
          {userRank.detailStats.studyDays !== undefined && (
            <div className="stat-item">
              <Text type="tertiary" size="small">学习天数</Text>
              <Text strong>{userRank.detailStats.studyDays}</Text>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default UserRankCard;
