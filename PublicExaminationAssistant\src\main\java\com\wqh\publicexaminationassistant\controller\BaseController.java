package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.common.result.PageResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.RestController;

/**
 * 控制器基类
 * 提供统一的响应格式和分页处理
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@RestController
public abstract class BaseController {

    /**
     * 返回成功结果
     */
    protected <T> ApiResponse<T> success(T data) {
        return ApiResponse.success(data);
    }

    /**
     * 返回成功结果（无数据）
     */
    protected ApiResponse<Void> success() {
        return ApiResponse.success();
    }

    /**
     * 返回成功结果（带消息）
     */
    protected <T> ApiResponse<T> success(T data, String message) {
        return ApiResponse.success(data, message);
    }

    /**
     * 返回失败结果
     */
    protected <T> ApiResponse<T> error(String message) {
        return ApiResponse.error(message);
    }

    /**
     * 返回失败结果（带错误码）
     */
    protected <T> ApiResponse<T> error(int code, String message) {
        return ApiResponse.error(code, message);
    }

    /**
     * 转换分页结果
     */
    protected <T> ApiResponse<PageResult<T>> pageSuccess(Page<T> page) {
        PageResult<T> pageResult = PageResult.<T>builder()
                .records(page.getRecords())
                .total(page.getTotal())
                .current(page.getCurrent())
                .size(page.getSize())
                .pages(page.getPages())
                .build();
        return success(pageResult);
    }

    /**
     * 获取当前用户ID
     * TODO: 从SecurityContext中获取当前用户ID
     */
    protected String getCurrentUserId() {
        // 开发阶段临时返回测试用户ID
        return "test-user-id-123456";
    }

    /**
     * 获取当前用户名
     * TODO: 从SecurityContext中获取当前用户名
     */
    protected String getCurrentUsername() {
        // 开发阶段临时返回测试用户名
        return "test-admin-user";
    }

    /**
     * 检查是否为管理员
     * TODO: 从SecurityContext中获取用户角色
     */
    protected boolean isAdmin() {
        // 开发阶段临时返回true，方便测试
        // 生产环境中应该从SecurityContext中获取用户角色
        return true;
    }

    /**
     * 检查是否为超级管理员
     * TODO: 从SecurityContext中获取用户角色
     */
    protected boolean isSuperAdmin() {
        // 开发阶段临时返回true，方便测试
        // 生产环境中应该从SecurityContext中获取用户角色
        return true;
    }
}
