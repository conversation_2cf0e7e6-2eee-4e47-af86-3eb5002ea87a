# 考公刷题系统 UI 设计规范

## 设计理念：手绘温馨风格

### 核心设计原则

1. **减压友好** - 缓解考试压力，营造轻松学习氛围
2. **温馨人文** - 手绘元素增加亲和力和温度感
3. **鼓励性** - 通过视觉设计传递正能量和支持感
4. **不完美美学** - 故意的不对称和手工感，更贴近人性

## 色彩方案

### 主色调 - 温馨纸质感
```css
--paper-bg: #fefdf8;           /* 纸质背景 */
--paper-warm: #faf9f4;         /* 温暖纸质 */
--paper-cream: #f7f6f0;        /* 奶油纸质 */
```

### 墨水色彩
```css
--ink-dark: #2d3748;           /* 深色墨水 */
--ink-medium: #4a5568;         /* 中等墨水 */
--ink-light: #718096;          /* 浅色墨水 */
--ink-lighter: #a0aec0;        /* 更浅墨水 */
```

### 强调色彩 - 温馨活泼
```css
--accent-blue: #4299e1;        /* 强调蓝 - 主要操作 */
--accent-green: #48bb78;       /* 成功绿 - 正确答案 */
--accent-orange: #ed8936;      /* 警告橙 - 提醒信息 */
--accent-purple: #9f7aea;      /* 紫色点缀 - 特殊功能 */
--accent-pink: #ed64a6;        /* 粉色点缀 - 装饰元素 */
```

## 字体系统

### 主要字体
- **手写字体**: Kalam (Google Fonts 开源字体)
- **系统字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif

### 字体使用场景
- **标题和重要信息**: 使用手写字体 (Kalam)
- **正文和数据**: 使用系统字体
- **装饰性文字**: 使用手写字体

### 字体大小层级
```css
--font-size-xs: 12px;    /* 辅助信息 */
--font-size-sm: 14px;    /* 次要文字 */
--font-size-base: 16px;  /* 正文 */
--font-size-lg: 18px;    /* 小标题 */
--font-size-xl: 20px;    /* 中标题 */
--font-size-2xl: 24px;   /* 大标题 */
--font-size-3xl: 30px;   /* 主标题 */
```

## 组件设计风格

### 1. 卡片组件 (sketch-card)
**特点**: 手绘边框、轻微旋转、悬浮效果
```css
.sketch-card {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 15px;
  transform: rotate(-0.5deg);
  box-shadow: 3px 3px 0px var(--shadow-light);
  transition: all 0.3s ease;
}

.sketch-card:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 5px 5px 0px var(--shadow-medium);
}
```

### 2. 按钮组件 (sketch-button)
**特点**: 圆角设计、手绘感边框、旋转效果
```css
.sketch-button {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 25px;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  font-family: var(--font-handwritten);
}

.sketch-button:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 3px 3px 0px var(--shadow-medium);
}
```

### 3. 输入框组件 (sketch-input)
**特点**: 虚线边框、纸质背景、聚焦动画
```css
.sketch-input {
  background: var(--paper-warm);
  border: 2px dashed var(--border-dark);
  border-radius: 12px;
  transform: rotate(-0.2deg);
  font-family: var(--font-handwritten);
}

.sketch-input:focus {
  border-style: solid;
  border-color: var(--accent-blue);
  transform: rotate(0deg);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}
```

### 4. 便利贴样式 (sticky-note)
**特点**: 黄色便利贴效果、折角设计
```css
.sticky-note {
  background: linear-gradient(135deg, #fff59d 0%, #fff176 100%);
  border-radius: 8px;
  transform: rotate(2deg);
  box-shadow: 2px 2px 8px var(--shadow-light);
  position: relative;
}

.sticky-note::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-left: 15px solid transparent;
  border-top: 15px solid #f57f17;
}
```

## 装饰元素

### 1. Emoji 图标
**使用场景**: 增加亲和力，减少严肃感
- 📚 学习相关
- ✏️ 答题相关
- 🎯 目标相关
- ⭐ 成就相关
- 🏆 排行榜相关

### 2. 浮动装饰
```css
.floating-emoji {
  position: absolute;
  font-size: 24px;
  animation: float 3s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.6;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}
```

### 3. 涂鸦边框
```css
.doodle-border {
  border: 3px solid var(--ink-dark);
  border-radius: 16px;
  position: relative;
}

.doodle-border::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px dashed var(--accent-blue);
  border-radius: 20px;
  opacity: 0.3;
}
```

## 动画效果

### 1. 基础动画
- **摆动动画**: 图标轻微摆动 (wiggle)
- **浮动效果**: 装饰元素上下浮动 (float)
- **旋转交互**: 悬浮时轻微旋转
- **弹跳效果**: 成功操作时的弹跳反馈

### 2. 交互动画
```css
/* 悬浮效果 */
.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

/* 点击反馈 */
.click-feedback:active {
  transform: scale(0.95);
}

/* 弹跳进入 */
.bounce-in {
  animation: bounceIn 0.6s ease-out;
}
```

## 页面布局原则

### 1. 空间布局
- **留白充足**: 避免拥挤感，营造轻松氛围
- **不规则对齐**: 适度的不对称增加手工感
- **层次分明**: 通过卡片和阴影建立视觉层次

### 2. 信息组织
- **卡片化设计**: 将信息分组到不同的手绘卡片中
- **渐进式展示**: 重要信息优先，次要信息折叠
- **视觉引导**: 通过颜色和动画引导用户注意力

### 3. 响应式适配
- **移动端优化**: 在小屏幕上减少旋转效果
- **触摸友好**: 增大点击区域，优化手势操作
- **性能考虑**: 在低性能设备上简化动画

## 情感化设计

### 1. 鼓励性元素
- **正向反馈**: 使用绿色和笑脸表情
- **进度可视化**: 手绘风格的进度条
- **成就展示**: 奖牌和星星装饰

### 2. 减压设计
- **温暖色调**: 避免冷色调和高对比度
- **柔和边缘**: 圆角和手绘线条
- **友好提示**: 使用温馨的文案和表情

### 3. 人性化细节
- **加载状态**: 手绘风格的加载动画
- **错误提示**: 温和的错误信息展示
- **空状态**: 有趣的空状态插画

这个设计规范确保了整个考公刷题系统具有一致的温馨手绘风格，既能缓解用户的考试压力，又能提供良好的用户体验。
