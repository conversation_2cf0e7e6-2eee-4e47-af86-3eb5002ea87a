package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 小桌申请表实体类
 * 管理加入小桌的申请流程
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("desk_applications")
public class DeskApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 小桌ID
     */
    @TableField("desk_id")
    private String deskId;

    /**
     * 申请者ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 申请理由
     */
    @TableField("reason")
    private String reason;

    /**
     * 学习计划
     */
    @TableField("study_plan")
    private String studyPlan;

    /**
     * 申请状态
     */
    @TableField("status")
    private String status;

    /**
     * 申请时间
     */
    @TableField(value = "applied_at", fill = FieldFill.INSERT)
    private LocalDateTime appliedAt;

    /**
     * 处理时间
     */
    @TableField("processed_at")
    private LocalDateTime processedAt;

    /**
     * 处理人ID
     */
    @TableField("processed_by")
    private String processedBy;

    /**
     * 默认构造函数
     */
    public DeskApplication() {
        this.status = "pending";
    }

    /**
     * 构造函数
     */
    public DeskApplication(String deskId, String userId, String reason, String studyPlan) {
        this();
        this.deskId = deskId;
        this.userId = userId;
        this.reason = reason;
        this.studyPlan = studyPlan;
    }

    /**
     * 检查申请是否待处理
     */
    public boolean isPending() {
        return "pending".equals(this.status);
    }

    /**
     * 检查申请是否已通过
     */
    public boolean isApproved() {
        return "approved".equals(this.status);
    }

    /**
     * 检查申请是否已拒绝
     */
    public boolean isRejected() {
        return "rejected".equals(this.status);
    }

    /**
     * 通过申请
     */
    public void approve(String processedBy) {
        this.status = "approved";
        this.processedBy = processedBy;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * 拒绝申请
     */
    public void reject(String processedBy) {
        this.status = "rejected";
        this.processedBy = processedBy;
        this.processedAt = LocalDateTime.now();
    }

    /**
     * 检查申请是否已处理
     */
    public boolean isProcessed() {
        return !isPending();
    }

    /**
     * 检查申请是否可以被处理
     */
    public boolean canBeProcessed() {
        return isPending();
    }
}
