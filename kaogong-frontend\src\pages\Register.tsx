import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button, Input, Form, Toast, Typography, Space, Spin } from '@douyinfe/semi-ui';
import { IconUser, IconMail, IconLock, IconEyeOpened, IconEyeClosed, IconKey } from '@douyinfe/semi-icons';
import { useAuthStore } from '../stores/useAuthStore';
import { authService, validateForm } from '../services/authService';
import '../styles/theme.css';

const { Title, Text } = Typography;

export const Register: React.FC = () => {
  const navigate = useNavigate();
  const { register, isLoading, error, clearError, isAuthenticated } = useAuthStore();
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    inviteCode: '',
    nickname: ''
  });
  
  const [formErrors, setFormErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    inviteCode: '',
    nickname: ''
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isValidatingInviteCode, setIsValidatingInviteCode] = useState(false);
  const [inviteCodeValid, setInviteCodeValid] = useState<boolean | null>(null);

  // 如果已登录，重定向到仪表板
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  // 显示错误信息
  useEffect(() => {
    if (error) {
      Toast.error(error);
      clearError();
    }
  }, [error, clearError]);

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除对应字段的错误
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }

    // 重置邀请码验证状态
    if (field === 'inviteCode') {
      setInviteCodeValid(null);
    }
  };

  // 验证邀请码
  const validateInviteCode = async (code: string) => {
    if (!code || code.length !== 8) {
      setInviteCodeValid(null);
      return;
    }

    setIsValidatingInviteCode(true);
    try {
      const result = await authService.validateInviteCode(code);
      setInviteCodeValid(result.valid);
      if (!result.valid) {
        setFormErrors(prev => ({ 
          ...prev, 
          inviteCode: result.message || '邀请码无效' 
        }));
      }
    } catch (error: any) {
      setInviteCodeValid(false);
      setFormErrors(prev => ({ 
        ...prev, 
        inviteCode: error.message || '邀请码验证失败' 
      }));
    } finally {
      setIsValidatingInviteCode(false);
    }
  };

  // 邀请码输入防抖验证
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.inviteCode) {
        validateInviteCode(formData.inviteCode);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.inviteCode]);

  // 验证表单
  const validateFormData = () => {
    const errors = {
      username: validateForm.username(formData.username) || '',
      email: validateForm.email(formData.email) || '',
      password: validateForm.password(formData.password) || '',
      confirmPassword: validateForm.confirmPassword(formData.password, formData.confirmPassword) || '',
      inviteCode: validateForm.inviteCode(formData.inviteCode) || '',
      nickname: validateForm.nickname(formData.nickname) || ''
    };

    // 检查邀请码是否已验证
    if (inviteCodeValid === false) {
      errors.inviteCode = '请输入有效的邀请码';
    } else if (inviteCodeValid === null && formData.inviteCode) {
      errors.inviteCode = '请等待邀请码验证完成';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => !error) && inviteCodeValid === true;
  };

  // 处理注册提交
  const handleSubmit = async () => {
    if (!validateFormData()) {
      return;
    }

    try {
      await register({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        inviteCode: formData.inviteCode,
        nickname: formData.nickname || undefined
      });
      
      Toast.success('注册成功！请登录您的账号');
      navigate('/login', { 
        state: { message: '注册成功，请使用新账号登录' }
      });
    } catch (error) {
      // 错误已在 useEffect 中处理
      console.error('注册失败:', error);
    }
  };

  // 处理回车键提交
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <div className="auth-container">
      {/* 背景装饰 */}
      <div className="floating-emoji" style={{ top: '15%', left: '10%', animationDelay: '0s' }}>
        🌟
      </div>
      <div className="floating-emoji" style={{ top: '70%', right: '15%', animationDelay: '1.5s' }}>
        📝
      </div>
      <div className="floating-emoji" style={{ bottom: '20%', left: '20%', animationDelay: '3s' }}>
        🎓
      </div>

      <div className="auth-card">
        <div className="sketch-card">
          {/* 标题 */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Title level={2} className="handwritten-title large" style={{ marginBottom: '8px' }}>
              加入我们！
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              开始你的考公学习之旅 ✨
            </Text>
          </div>

          {/* 注册表单 */}
          <Form onSubmit={handleSubmit}>
            <Space vertical style={{ width: '100%' }} spacing={16}>
              {/* 用户名输入 */}
              <div>
                <Input
                  size="large"
                  placeholder="请输入用户名（3-20个字符）"
                  prefix={<IconUser />}
                  value={formData.username}
                  onChange={(value) => handleInputChange('username', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.username ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.username && (
                  <div className="error-message">{formErrors.username}</div>
                )}
              </div>

              {/* 邮箱输入 */}
              <div>
                <Input
                  size="large"
                  placeholder="请输入邮箱地址"
                  prefix={<IconMail />}
                  value={formData.email}
                  onChange={(value) => handleInputChange('email', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.email ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.email && (
                  <div className="error-message">{formErrors.email}</div>
                )}
              </div>

              {/* 密码输入 */}
              <div>
                <Input
                  size="large"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入密码（至少6个字符）"
                  prefix={<IconLock />}
                  suffix={
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      style={{ 
                        background: 'none', 
                        border: 'none', 
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {showPassword ? <IconEyeClosed /> : <IconEyeOpened />}
                    </button>
                  }
                  value={formData.password}
                  onChange={(value) => handleInputChange('password', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.password ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.password && (
                  <div className="error-message">{formErrors.password}</div>
                )}
              </div>

              {/* 确认密码输入 */}
              <div>
                <Input
                  size="large"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="请再次输入密码"
                  prefix={<IconLock />}
                  suffix={
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      style={{ 
                        background: 'none', 
                        border: 'none', 
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {showConfirmPassword ? <IconEyeClosed /> : <IconEyeOpened />}
                    </button>
                  }
                  value={formData.confirmPassword}
                  onChange={(value) => handleInputChange('confirmPassword', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.confirmPassword ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.confirmPassword && (
                  <div className="error-message">{formErrors.confirmPassword}</div>
                )}
              </div>

              {/* 邀请码输入 */}
              <div>
                <Input
                  size="large"
                  placeholder="请输入8位邀请码"
                  prefix={<IconKey />}
                  suffix={
                    isValidatingInviteCode ? (
                      <Spin size="small" />
                    ) : inviteCodeValid === true ? (
                      <span style={{ color: 'var(--accent-green)' }}>✓</span>
                    ) : inviteCodeValid === false ? (
                      <span style={{ color: '#e53e3e' }}>✗</span>
                    ) : null
                  }
                  value={formData.inviteCode}
                  onChange={(value) => handleInputChange('inviteCode', value.toUpperCase())}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.inviteCode ? '#e53e3e' : 
                                inviteCodeValid === true ? 'var(--accent-green)' : undefined
                  }}
                />
                {formErrors.inviteCode && (
                  <div className="error-message">{formErrors.inviteCode}</div>
                )}
                {inviteCodeValid === true && (
                  <div className="success-message">邀请码验证成功！</div>
                )}
              </div>

              {/* 昵称输入（可选） */}
              <div>
                <Input
                  size="large"
                  placeholder="请输入昵称（可选）"
                  prefix={<span>😊</span>}
                  value={formData.nickname}
                  onChange={(value) => handleInputChange('nickname', value)}
                  onKeyPress={handleKeyPress}
                  className="sketch-input"
                  style={{ 
                    borderColor: formErrors.nickname ? '#e53e3e' : undefined
                  }}
                />
                {formErrors.nickname && (
                  <div className="error-message">{formErrors.nickname}</div>
                )}
              </div>

              {/* 注册按钮 */}
              <Button
                size="large"
                type="primary"
                loading={isLoading}
                onClick={handleSubmit}
                disabled={inviteCodeValid !== true}
                className="sketch-button primary"
                style={{ 
                  width: '100%',
                  height: '48px',
                  fontSize: '16px',
                  fontWeight: '500',
                  marginTop: '8px'
                }}
              >
                {isLoading ? '注册中...' : '注册账号'}
              </Button>

              {/* 登录链接 */}
              <div style={{ textAlign: 'center', marginTop: '16px' }}>
                <Text type="secondary">
                  已有账号？
                  <Link 
                    to="/login" 
                    style={{ 
                      color: 'var(--accent-blue)',
                      textDecoration: 'none',
                      fontWeight: '500',
                      marginLeft: '8px'
                    }}
                  >
                    立即登录
                  </Link>
                </Text>
              </div>
            </Space>
          </Form>
        </div>

        {/* 温馨提示 */}
        <div className="sticky-note" style={{ marginTop: '24px' }}>
          <Text style={{ fontSize: '14px', fontFamily: 'var(--font-handwritten)' }}>
            🎯 小提示：没有邀请码？联系已注册的朋友获取邀请码吧～
          </Text>
        </div>
      </div>
    </div>
  );
};

export default Register;
