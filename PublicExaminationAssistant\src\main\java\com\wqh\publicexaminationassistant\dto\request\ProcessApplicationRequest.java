package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 处理申请请求DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class ProcessApplicationRequest {

    /**
     * 处理结果（approved/rejected）
     */
    @NotBlank(message = "处理结果不能为空")
    @Pattern(regexp = "^(approved|rejected)$", message = "处理结果只能是approved或rejected")
    private String action;

    /**
     * 处理备注
     */
    private String remark;
}
