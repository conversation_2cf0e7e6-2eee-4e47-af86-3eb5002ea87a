package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 错题本表实体类
 * 存储用户的错题信息和复习状态
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wrong_questions")
public class WrongQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错题ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 关联的学习记录ID
     */
    @TableField("study_record_id")
    private String studyRecordId;

    /**
     * 学习模块类型
     */
    @TableField("module_type")
    private String moduleType;

    /**
     * 题目类型
     */
    @TableField("question_type")
    private String questionType;

    /**
     * 难度等级
     */
    @TableField("difficulty_level")
    private String difficultyLevel;

    /**
     * 题目内容
     */
    @TableField("question_content")
    private String questionContent;

    /**
     * 用户答案
     */
    @TableField("user_answer")
    private String userAnswer;

    /**
     * 正确答案
     */
    @TableField("correct_answer")
    private String correctAnswer;

    /**
     * 解析
     */
    @TableField("explanation")
    private String explanation;

    /**
     * 掌握状态
     */
    @TableField("mastery_status")
    private String masteryStatus;

    /**
     * 复习次数
     */
    @TableField("review_count")
    private Integer reviewCount;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 最后复习时间
     */
    @TableField("reviewed_at")
    private LocalDateTime reviewedAt;
}
