import React, { useState } from 'react';
import { Card, Button, Space, InputNumber, Select, Toast } from '@douyinfe/semi-ui';
import { IconStar, IconRefresh, IconBell } from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';
import { ReputationNotification, useReputationNotification } from '../components/ReputationNotification';

const ReputationTest: React.FC = () => {
  const { notification, showNotification, clearNotification } = useReputationNotification();
  const [testPoints, setTestPoints] = useState<number>(10);
  const [testChangeType, setTestChangeType] = useState<'earn' | 'deduct'>('earn');
  const [testReason, setTestReason] = useState<string>('测试奖励');
  const [currentScore, setCurrentScore] = useState<number>(150);
  const [currentLevel, setCurrentLevel] = useState<string>('bronze');

  const levelThresholds = {
    'newbie': { min: 0, max: 99, next: 'bronze' },
    'bronze': { min: 100, max: 299, next: 'silver' },
    'silver': { min: 300, max: 599, next: 'gold' },
    'gold': { min: 600, max: 999, next: 'platinum' },
    'platinum': { min: 1000, max: 1999, next: 'diamond' },
    'diamond': { min: 2000, max: 4999, next: 'master' },
    'master': { min: 5000, max: 9999, next: 'grandmaster' },
    'grandmaster': { min: 10000, max: 999999, next: null }
  };

  const calculateNewLevel = (score: number): string => {
    for (const [level, threshold] of Object.entries(levelThresholds)) {
      if (score >= threshold.min && score <= threshold.max) {
        return level;
      }
    }
    return 'newbie';
  };

  const handleTestNotification = () => {
    const newScore = testChangeType === 'earn' 
      ? currentScore + testPoints 
      : Math.max(0, currentScore - testPoints);
    
    const newLevel = calculateNewLevel(newScore);
    const levelChanged = newLevel !== currentLevel;

    showNotification({
      id: Date.now().toString(),
      changeType: testChangeType,
      points: testPoints,
      reason: testReason,
      category: 'test',
      newScore,
      newLevel: levelChanged ? newLevel : undefined,
      levelChanged
    });

    // 更新当前状态
    setCurrentScore(newScore);
    if (levelChanged) {
      setCurrentLevel(newLevel);
    }

    Toast.success('测试通知已发送！');
  };

  const handleLevelUpTest = () => {
    const newScore = 300; // 升级到银级
    const newLevel = 'silver';
    
    showNotification({
      id: Date.now().toString(),
      changeType: 'earn',
      points: newScore - currentScore,
      reason: '等级升级测试',
      category: 'test',
      newScore,
      newLevel,
      levelChanged: true
    });

    setCurrentScore(newScore);
    setCurrentLevel(newLevel);
  };

  const resetTestData = () => {
    setCurrentScore(150);
    setCurrentLevel('bronze');
    setTestPoints(10);
    setTestChangeType('earn');
    setTestReason('测试奖励');
    Toast.success('测试数据已重置！');
  };

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'var(--paper-bg)',
      paddingTop: '80px'
    }}>
      <Navigation />
      
      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto', 
        padding: '20px' 
      }}>
        <h1 style={{
          fontFamily: 'var(--font-handwritten)',
          fontSize: '32px',
          color: 'var(--ink-dark)',
          textAlign: 'center',
          marginBottom: '30px',
          transform: 'rotate(-1deg)'
        }}>
          🧪 信誉系统测试页面
        </h1>

        {/* 当前状态显示 */}
        <Card 
          title="当前状态"
          style={{
            background: 'var(--paper-warm)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            marginBottom: '25px',
            transform: 'rotate(0.3deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)'
          }}
        >
          <Space>
            <div>
              <strong>当前分数:</strong> {currentScore}
            </div>
            <div>
              <strong>当前等级:</strong> {currentLevel}
            </div>
          </Space>
        </Card>

        {/* 通知测试控制面板 */}
        <Card 
          title="通知测试控制面板"
          style={{
            background: 'var(--paper-bg)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            marginBottom: '25px',
            transform: 'rotate(-0.2deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)'
          }}
        >
          <Space direction="vertical" spacing="medium" style={{ width: '100%' }}>
            <div style={{ display: 'flex', gap: '15px', alignItems: 'center', flexWrap: 'wrap' }}>
              <div>
                <label style={{ marginRight: '8px' }}>变更类型:</label>
                <Select
                  value={testChangeType}
                  onChange={setTestChangeType}
                  style={{ width: 120 }}
                >
                  <Select.Option value="earn">获得</Select.Option>
                  <Select.Option value="deduct">扣除</Select.Option>
                </Select>
              </div>
              
              <div>
                <label style={{ marginRight: '8px' }}>分数:</label>
                <InputNumber
                  value={testPoints}
                  onChange={setTestPoints}
                  min={1}
                  max={100}
                  style={{ width: 100 }}
                />
              </div>
              
              <div>
                <label style={{ marginRight: '8px' }}>原因:</label>
                <Select
                  value={testReason}
                  onChange={setTestReason}
                  style={{ width: 150 }}
                >
                  <Select.Option value="测试奖励">测试奖励</Select.Option>
                  <Select.Option value="每日登录">每日登录</Select.Option>
                  <Select.Option value="完成学习">完成学习</Select.Option>
                  <Select.Option value="连续学习">连续学习</Select.Option>
                  <Select.Option value="违规扣分">违规扣分</Select.Option>
                  <Select.Option value="未学习扣分">未学习扣分</Select.Option>
                </Select>
              </div>
            </div>

            <Space>
              <Button
                type="primary"
                icon={<IconBell />}
                onClick={handleTestNotification}
                style={{
                  borderRadius: '20px',
                  fontFamily: 'var(--font-handwritten)'
                }}
              >
                发送通知
              </Button>
              
              <Button
                type="secondary"
                icon={<IconStar />}
                onClick={handleLevelUpTest}
                style={{
                  borderRadius: '20px',
                  fontFamily: 'var(--font-handwritten)'
                }}
              >
                测试等级升级
              </Button>
              
              <Button
                type="tertiary"
                icon={<IconRefresh />}
                onClick={resetTestData}
                style={{
                  borderRadius: '20px',
                  fontFamily: 'var(--font-handwritten)'
                }}
              >
                重置数据
              </Button>
            </Space>
          </Space>
        </Card>

        {/* 使用说明 */}
        <Card 
          title="使用说明"
          style={{
            background: 'var(--paper-cream)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            transform: 'rotate(0.1deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)'
          }}
        >
          <div style={{ 
            fontFamily: 'var(--font-handwritten)',
            lineHeight: '1.6',
            color: 'var(--ink-dark)'
          }}>
            <p><strong>📋 测试功能说明：</strong></p>
            <ul>
              <li><strong>发送通知：</strong> 根据设置的参数发送信誉变更通知</li>
              <li><strong>测试等级升级：</strong> 直接触发等级升级庆祝动画</li>
              <li><strong>重置数据：</strong> 将测试数据重置为初始状态</li>
            </ul>
            
            <p><strong>🎯 等级阈值：</strong></p>
            <ul>
              <li>新手: 0-99分</li>
              <li>青铜: 100-299分</li>
              <li>白银: 300-599分</li>
              <li>黄金: 600-999分</li>
              <li>铂金: 1000-1999分</li>
              <li>钻石: 2000-4999分</li>
              <li>大师: 5000-9999分</li>
              <li>宗师: 10000+分</li>
            </ul>
          </div>
        </Card>
      </div>

      {/* 信誉通知组件 */}
      <ReputationNotification
        notification={notification}
        onClose={clearNotification}
      />
    </div>
  );
};

export default ReputationTest;
