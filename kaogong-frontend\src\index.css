/* 考公刷题系统 - 全局样式重置 */

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background-color: #fefdf8;
  color: #2d3748;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  width: 100%;
}

/* 链接样式 */
a {
  color: #4299e1;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #3182ce;
  text-decoration: underline;
}

/* 按钮基础样式重置 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
}

/* 输入框基础样式重置 */
input, textarea, select {
  font-family: inherit;
  outline: none;
}

/* 标题样式重置 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: normal;
}

/* 列表样式重置 */
ul, ol {
  list-style: none;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 确保Semi Design组件正常显示 */
.semi-button {
  font-family: inherit !important;
}

.semi-input {
  font-family: inherit !important;
}
