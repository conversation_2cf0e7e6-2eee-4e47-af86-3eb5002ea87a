# 考试公告模块 API 接口文档

## 模块概述
考试公告模块负责考试信息的发布、管理和搜索，支持全文搜索、分类筛选和时间提醒功能。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 公告查询 | 获取公告列表 | `/api/v1/announcements` | GET | `page=1&size=20&region=string&examType=string&priority=normal&status=published&sortBy=publishedAt&sortOrder=desc` | `{"code": 200, "data": {"records": [...], "total": 100, "current": 1, "size": 20}}` | 无 | 公开访问，支持多条件筛选 |
| 公告查询 | 获取公告详情 | `/api/v1/announcements/{announcementId}` | GET | 路径参数: `announcementId` | `{"code": 200, "data": {"id": "uuid", "title": "2024年国考公告", "content": "详细内容", "region": "全国", "examType": "国考", "importantDates": [...], "priority": "high", "status": "published", "createdBy": "uuid", "publishedAt": "datetime", "createdAt": "datetime", "updatedAt": "datetime"}}` | 无 | 公告详细信息 |
| 公告查询 | 搜索公告 | `/api/v1/announcements/search` | GET | `keyword=公务员&region=string&examType=string&startDate=date&endDate=date&page=1&size=20` | `{"code": 200, "data": {"records": [...], "total": 50, "keyword": "公务员", "searchTime": "0.15s"}}` | 无 | 支持全文搜索和高亮 |
| 公告查询 | 获取热门公告 | `/api/v1/announcements/hot` | GET | `limit=10&period=week` | `{"code": 200, "data": [{"id": "uuid", "title": "热门公告", "viewCount": 1500, "region": "北京", "examType": "省考"}]}` | 无 | 按浏览量排序 |
| 公告查询 | 获取最新公告 | `/api/v1/announcements/latest` | GET | `limit=5&region=string&examType=string` | `{"code": 200, "data": [{"id": "uuid", "title": "最新公告", "publishedAt": "datetime", "region": "上海", "examType": "事业单位"}]}` | 无 | 最新发布的公告 |
| 公告查询 | 获取公告分类 | `/api/v1/announcements/categories` | GET | 无 | `{"code": 200, "data": {"regions": ["全国", "北京", "上海"], "examTypes": ["国考", "省考", "事业单位"], "priorities": ["normal", "high", "urgent"]}}` | 无 | 公告分类信息 |
| 公告管理 | 创建公告 | `/api/v1/announcements` | POST | `{"title": "2024年国考公告", "content": "详细内容", "region": "全国", "examType": "国考", "importantDates": [{"name": "报名开始", "date": "2024-02-01", "type": "registration_start"}], "priority": "high", "publishedAt": "datetime?"}` | `{"code": 200, "message": "公告创建成功", "data": {"id": "uuid"}}` | admin | 管理员创建公告 |
| 公告管理 | 更新公告 | `/api/v1/announcements/{announcementId}` | PUT | `{"title": "string?", "content": "string?", "region": "string?", "examType": "string?", "importantDates": [...], "priority": "normal|high|urgent", "status": "draft|published|archived"}` | `{"code": 200, "message": "公告更新成功"}` | admin | 更新公告信息 |
| 公告管理 | 删除公告 | `/api/v1/announcements/{announcementId}` | DELETE | 路径参数: `announcementId` | `{"code": 200, "message": "公告删除成功"}` | admin | 软删除公告 |
| 公告管理 | 发布公告 | `/api/v1/announcements/{announcementId}/publish` | POST | `{"publishedAt": "datetime?"}` | `{"code": 200, "message": "公告发布成功"}` | admin | 发布草稿公告 |
| 公告管理 | 归档公告 | `/api/v1/announcements/{announcementId}/archive` | POST | `{"reason": "string?"}` | `{"code": 200, "message": "公告已归档"}` | admin | 归档过期公告 |
| 公告管理 | 批量操作 | `/api/v1/announcements/batch` | POST | `{"action": "publish|archive|delete", "announcementIds": ["uuid1", "uuid2"], "reason": "string?"}` | `{"code": 200, "message": "批量操作完成", "data": {"success": 2, "failed": 0}}` | admin | 批量处理公告 |
| 公告管理 | 获取管理列表 | `/api/v1/announcements/manage` | GET | `page=1&size=20&status=all&createdBy=uuid&startDate=date&endDate=date` | `{"code": 200, "data": {"records": [...], "total": 50}}` | admin | 管理员查看所有公告 |
| 时间提醒 | 添加提醒 | `/api/v1/announcements/{announcementId}/reminders` | POST | `{"dateType": "registration_start|exam_date", "advanceDays": 3, "reminderType": "system|email", "enabled": true}` | `{"code": 200, "message": "提醒设置成功", "data": {"id": "uuid"}}` | user | 用户设置考试提醒 |
| 时间提醒 | 获取我的提醒 | `/api/v1/my-reminders` | GET | `page=1&size=20&status=active&sortBy=reminderDate` | `{"code": 200, "data": {"records": [...], "total": 10}}` | user | 查看个人提醒列表 |
| 时间提醒 | 更新提醒 | `/api/v1/reminders/{reminderId}` | PUT | `{"advanceDays": 5, "reminderType": "system|email", "enabled": false}` | `{"code": 200, "message": "提醒更新成功"}` | user | 修改提醒设置 |
| 时间提醒 | 删除提醒 | `/api/v1/reminders/{reminderId}` | DELETE | 路径参数: `reminderId` | `{"code": 200, "message": "提醒删除成功"}` | user | 取消提醒 |
| 时间提醒 | 获取即将到期 | `/api/v1/announcements/upcoming` | GET | `days=7&userId=uuid` | `{"code": 200, "data": [{"announcementId": "uuid", "title": "公告标题", "dateType": "registration_end", "date": "2024-02-15", "daysLeft": 3}]}` | user | 即将到期的重要日期 |
| 统计分析 | 获取公告统计 | `/api/v1/announcements/stats` | GET | `period=month&groupBy=region` | `{"code": 200, "data": {"totalCount": 100, "publishedCount": 85, "draftCount": 15, "byRegion": [{"region": "全国", "count": 20}, {"region": "北京", "count": 15}], "byExamType": [{"examType": "国考", "count": 30}]}}` | admin | 公告发布统计 |
| 统计分析 | 获取浏览统计 | `/api/v1/announcements/view-stats` | GET | `announcementId=uuid&period=week` | `{"code": 200, "data": {"totalViews": 1500, "uniqueViews": 1200, "dailyViews": [{"date": "2024-01-15", "views": 150}], "topRegions": [{"region": "北京", "views": 300}]}}` | admin | 公告浏览数据统计 |
| 统计分析 | 获取搜索统计 | `/api/v1/announcements/search-stats` | GET | `period=month&limit=20` | `{"code": 200, "data": {"totalSearches": 5000, "topKeywords": [{"keyword": "公务员", "count": 500}, {"keyword": "事业单位", "count": 300}], "noResultKeywords": [{"keyword": "某关键词", "count": 10}]}}` | admin | 搜索行为统计 |

## 数据模型

### 公告模型 (Announcement)
```json
{
  "id": "uuid",
  "title": "2024年国家公务员考试公告",
  "content": "详细的公告内容...",
  "region": "全国",
  "examType": "国考",
  "importantDates": [
    {
      "name": "报名开始",
      "date": "2024-02-01",
      "type": "registration_start",
      "description": "网上报名开始时间"
    },
    {
      "name": "报名结束", 
      "date": "2024-02-15",
      "type": "registration_end",
      "description": "网上报名截止时间"
    },
    {
      "name": "笔试时间",
      "date": "2024-03-15",
      "type": "exam_date",
      "description": "笔试考试时间"
    }
  ],
  "priority": "high",
  "status": "published",
  "createdBy": "uuid",
  "publishedAt": "2024-01-15T10:00:00Z",
  "createdAt": "2024-01-15T09:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z",
  "viewCount": 1500,
  "tags": ["国考", "公务员", "2024"]
}
```

### 提醒设置模型 (Reminder)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "announcementId": "uuid",
  "announcementTitle": "2024年国考公告",
  "dateType": "registration_start",
  "targetDate": "2024-02-01",
  "advanceDays": 3,
  "reminderDate": "2024-01-29",
  "reminderType": "system",
  "enabled": true,
  "status": "pending",
  "createdAt": "datetime"
}
```

### 公告统计模型 (AnnouncementStats)
```json
{
  "period": "month",
  "totalCount": 100,
  "publishedCount": 85,
  "draftCount": 15,
  "archivedCount": 5,
  "byRegion": [
    {"region": "全国", "count": 20},
    {"region": "北京", "count": 15}
  ],
  "byExamType": [
    {"examType": "国考", "count": 30},
    {"examType": "省考", "count": 25}
  ],
  "byPriority": [
    {"priority": "high", "count": 10},
    {"priority": "normal", "count": 75}
  ]
}
```

## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|---------|------|
| 4001 | 公告不存在 | 请求的公告ID无效 |
| 4002 | 公告已发布 | 不能修改已发布的公告 |
| 4003 | 公告已归档 | 不能操作已归档的公告 |
| 4004 | 重要日期格式错误 | importantDates字段格式不正确 |
| 4005 | 提醒已存在 | 该公告的提醒已设置 |
| 4006 | 提醒不存在 | 请求的提醒ID无效 |
| 4007 | 搜索关键词为空 | 搜索关键词不能为空 |
| 4008 | 批量操作失败 | 部分公告操作失败 |

## 业务规则

### 公告发布规则
1. **草稿状态**: 创建后默认为草稿，可以修改
2. **发布状态**: 发布后不能修改内容，只能归档
3. **归档状态**: 过期或无效的公告可以归档
4. **权限控制**: 只有管理员可以创建、修改、删除公告

### 搜索规则
1. **全文搜索**: 支持标题和内容的全文搜索
2. **分词搜索**: 使用MySQL ngram分词器
3. **高亮显示**: 搜索结果中关键词高亮
4. **搜索历史**: 记录用户搜索行为用于统计

### 提醒规则
1. **提前提醒**: 可设置提前1-30天提醒
2. **提醒类型**: 支持系统通知和邮件提醒
3. **自动清理**: 过期提醒自动清理
4. **重复提醒**: 同一用户同一公告只能设置一个提醒

### 时间处理
1. **时区统一**: 所有时间使用UTC存储
2. **日期格式**: 使用ISO 8601格式
3. **重要日期**: 支持多个重要时间节点
4. **过期处理**: 自动标记过期公告
