-- =====================================================
-- 错题图片功能数据库迁移脚本
-- 版本: 001
-- 创建时间: 2024-07-19
-- 描述: 为错题管理系统添加图片上传功能支持
-- =====================================================

-- 1. 创建错题图片表
CREATE TABLE `wrong_question_images` (
  `id` VARCHAR(36) NOT NULL COMMENT '图片ID',
  `wrong_question_id` VARCHAR(36) NOT NULL COMMENT '错题ID',
  `image_type` VARCHAR(20) NOT NULL COMMENT '图片类型：question|answer|explanation',
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件存储路径',
  `file_size` BIGINT NOT NULL COMMENT '文件大小(字节)',
  `mime_type` VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  `width` INT NULL COMMENT '图片宽度(像素)',
  `height` INT NULL COMMENT '图片高度(像素)',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_compressed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已压缩',
  `thumbnail_path` VARCHAR(500) NULL COMMENT '缩略图路径',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  
  -- 索引设计
  INDEX `idx_wrong_question_id` (`wrong_question_id`),
  INDEX `idx_image_type` (`image_type`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_wrong_question_type` (`wrong_question_id`, `image_type`),
  INDEX `idx_wrong_question_sort` (`wrong_question_id`, `image_type`, `sort_order`),
  
  -- 外键约束
  CONSTRAINT `fk_wrong_question_images_wrong_question_id` 
    FOREIGN KEY (`wrong_question_id`) 
    REFERENCES `wrong_questions` (`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE,
    
  -- 检查约束
  CONSTRAINT `chk_image_type` 
    CHECK (`image_type` IN ('question', 'answer', 'explanation')),
  CONSTRAINT `chk_file_size` 
    CHECK (`file_size` > 0 AND `file_size` <= 10485760), -- 最大10MB
  CONSTRAINT `chk_sort_order` 
    CHECK (`sort_order` >= 0)
    
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错题图片表';

-- 2. 创建文件上传日志表（用于审计和清理）
CREATE TABLE `file_upload_logs` (
  `id` VARCHAR(36) NOT NULL COMMENT '日志ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '上传用户ID',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
  `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
  `file_size` BIGINT NOT NULL COMMENT '文件大小',
  `mime_type` VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  `upload_status` VARCHAR(20) NOT NULL DEFAULT 'success' COMMENT '上传状态：success|failed|deleted',
  `related_table` VARCHAR(50) NULL COMMENT '关联表名',
  `related_id` VARCHAR(36) NULL COMMENT '关联记录ID',
  `ip_address` VARCHAR(45) NULL COMMENT '上传IP地址',
  `user_agent` TEXT NULL COMMENT '用户代理',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_file_path` (`file_path`),
  INDEX `idx_upload_status` (`upload_status`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_related` (`related_table`, `related_id`),
  
  CONSTRAINT `fk_file_upload_logs_user_id` 
    FOREIGN KEY (`user_id`) 
    REFERENCES `users` (`id`) 
    ON DELETE CASCADE ON UPDATE CASCADE
    
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传日志表';

-- 3. 文件上传配置说明
-- 注意：文件上传相关配置在应用配置文件(application.yml)中管理
-- 主要配置项：
-- - app.upload.max-size: 文件上传最大大小(默认10MB)
-- - app.upload.allowed-types: 允许上传的文件类型(jpg,jpeg,png,gif,webp)
-- - app.upload.path: 文件上传路径(默认/uploads)
-- - app.image.max-width: 图片最大宽度(默认1920px)
-- - app.image.max-height: 图片最大高度(默认1080px)
-- - app.image.quality: 图片压缩质量(默认85)
-- - app.thumbnail.width: 缩略图宽度(默认200px)
-- - app.thumbnail.height: 缩略图高度(默认200px)
