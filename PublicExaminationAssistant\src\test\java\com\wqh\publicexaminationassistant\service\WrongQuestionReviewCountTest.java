package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.dto.request.WrongQuestionRequest;
import com.wqh.publicexaminationassistant.dto.response.WrongQuestionResponse;
import com.wqh.publicexaminationassistant.entity.WrongQuestion;
import com.wqh.publicexaminationassistant.mapper.WrongQuestionMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 错题复习次数功能测试
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class WrongQuestionReviewCountTest {

    @Autowired
    private WrongQuestionService wrongQuestionService;

    @Autowired
    private WrongQuestionMapper wrongQuestionMapper;

    @Test
    @DisplayName("测试增加复习次数的基本功能")
    void testIncrementReviewCountBasic() {
        // 1. 先创建一个错题
        WrongQuestionRequest request = new WrongQuestionRequest();
        request.setModuleType("math");
        request.setQuestionType("single_choice");
        request.setDifficultyLevel("medium");
        request.setQuestionContent("测试题目内容");
        request.setCorrectAnswer("A");
        
        String userId = "test-user-id";
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userId);
        String wrongQuestionId = response.getId();
        
        // 验证初始复习次数为0
        assertEquals(0, response.getReviewCount(), "初始复习次数应该为0");
        
        // 2. 增加复习次数
        wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
        
        // 3. 验证复习次数已增加
        WrongQuestion updatedQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        assertNotNull(updatedQuestion, "错题应该存在");
        assertEquals(1, updatedQuestion.getReviewCount(), "复习次数应该增加到1");
        assertNotNull(updatedQuestion.getReviewedAt(), "最后复习时间应该被更新");
    }

    @Test
    @DisplayName("测试多次增加复习次数")
    void testIncrementReviewCountMultipleTimes() {
        // 1. 创建错题
        WrongQuestionRequest request = new WrongQuestionRequest();
        request.setModuleType("math");
        request.setQuestionType("single_choice");
        request.setDifficultyLevel("medium");
        request.setQuestionContent("测试题目内容");
        request.setCorrectAnswer("A");
        
        String userId = "test-user-id";
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userId);
        String wrongQuestionId = response.getId();
        
        // 2. 多次增加复习次数
        int reviewTimes = 5;
        for (int i = 1; i <= reviewTimes; i++) {
            wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
            
            // 验证每次增加后的复习次数
            WrongQuestion updatedQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
            assertEquals(i, updatedQuestion.getReviewCount(), 
                    "第" + i + "次复习后，复习次数应该为" + i);
        }
    }

    @Test
    @DisplayName("测试增加不存在错题的复习次数")
    void testIncrementReviewCountForNonExistentQuestion() {
        String nonExistentId = "non-existent-id";
        String userId = "test-user-id";
        
        // 应该抛出BusinessException
        assertThrows(BusinessException.class, () -> {
            wrongQuestionService.incrementReviewCount(nonExistentId, userId);
        }, "对不存在的错题增加复习次数应该抛出异常");
    }

    @Test
    @DisplayName("测试增加其他用户错题的复习次数")
    void testIncrementReviewCountForOtherUserQuestion() {
        // 1. 用用户A创建错题
        WrongQuestionRequest request = new WrongQuestionRequest();
        request.setModuleType("math");
        request.setQuestionType("single_choice");
        request.setDifficultyLevel("medium");
        request.setQuestionContent("测试题目内容");
        request.setCorrectAnswer("A");
        
        String userA = "user-a";
        String userB = "user-b";
        
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userA);
        String wrongQuestionId = response.getId();
        
        // 2. 用用户B尝试增加复习次数
        assertThrows(BusinessException.class, () -> {
            wrongQuestionService.incrementReviewCount(wrongQuestionId, userB);
        }, "用户不能增加其他用户错题的复习次数");
    }

    @Test
    @DisplayName("测试获取错题详情时复习次数的正确显示")
    void testGetWrongQuestionDetailWithReviewCount() {
        // 1. 创建错题
        WrongQuestionRequest request = new WrongQuestionRequest();
        request.setModuleType("math");
        request.setQuestionType("single_choice");
        request.setDifficultyLevel("medium");
        request.setQuestionContent("测试题目内容");
        request.setCorrectAnswer("A");
        
        String userId = "test-user-id";
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userId);
        String wrongQuestionId = response.getId();
        
        // 2. 增加几次复习次数
        wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
        wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
        wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
        
        // 3. 获取错题详情
        WrongQuestionResponse detailResponse = wrongQuestionService.getWrongQuestionById(wrongQuestionId, userId);
        
        // 4. 验证复习次数正确显示
        assertEquals(3, detailResponse.getReviewCount(), "错题详情中的复习次数应该正确显示");
        assertNotNull(detailResponse.getReviewedAt(), "最后复习时间应该存在");
    }

    @Test
    @DisplayName("测试并发增加复习次数的原子性")
    void testConcurrentIncrementReviewCount() throws InterruptedException {
        // 1. 创建错题
        WrongQuestionRequest request = new WrongQuestionRequest();
        request.setModuleType("math");
        request.setQuestionType("single_choice");
        request.setDifficultyLevel("medium");
        request.setQuestionContent("测试题目内容");
        request.setCorrectAnswer("A");
        
        String userId = "test-user-id";
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userId);
        String wrongQuestionId = response.getId();
        
        // 2. 模拟并发增加复习次数
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> {
                try {
                    wrongQuestionService.incrementReviewCount(wrongQuestionId, userId);
                } catch (Exception e) {
                    // 忽略异常，专注于测试原子性
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 3. 验证最终复习次数
        WrongQuestion finalQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        assertEquals(threadCount, finalQuestion.getReviewCount(), 
                "并发增加复习次数后，最终次数应该等于线程数量");
    }
}
