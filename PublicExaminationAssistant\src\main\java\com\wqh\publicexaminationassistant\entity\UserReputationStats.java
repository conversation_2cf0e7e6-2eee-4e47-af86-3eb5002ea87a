package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信誉统计实体类
 * 存储用户信誉统计信息和状态
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_reputation_stats")
public class UserReputationStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.INPUT)
    private String userId;

    /**
     * 当前信誉分数
     */
    @TableField("current_score")
    private Integer currentScore;

    /**
     * 当前信誉等级
     */
    @TableField("current_level")
    private String currentLevel;

    /**
     * 累计获得分数
     */
    @TableField("total_earned")
    private Integer totalEarned;

    /**
     * 累计扣除分数
     */
    @TableField("total_deducted")
    private Integer totalDeducted;

    /**
     * 连续登录天数
     */
    @TableField("consecutive_login_days")
    private Integer consecutiveLoginDays;

    /**
     * 连续学习天数
     */
    @TableField("consecutive_study_days")
    private Integer consecutiveStudyDays;

    /**
     * 连续未学习天数
     */
    @TableField("consecutive_no_study_days")
    private Integer consecutiveNoStudyDays;

    /**
     * 最后登录日期
     */
    @TableField("last_login_date")
    private LocalDate lastLoginDate;

    /**
     * 最后学习日期
     */
    @TableField("last_study_date")
    private LocalDate lastStudyDate;

    /**
     * 最后分数更新时间
     */
    @TableField("last_score_update")
    private LocalDateTime lastScoreUpdate;

    /**
     * 保护期结束时间
     */
    @TableField("protection_end_time")
    private LocalDateTime protectionEndTime;

    /**
     * 本周已扣分数
     */
    @TableField("weekly_deduct_points")
    private Integer weeklyDeductPoints;

    /**
     * 本月已扣分数
     */
    @TableField("monthly_deduct_points")
    private Integer monthlyDeductPoints;

    /**
     * 上次周重置日期
     */
    @TableField("last_weekly_reset")
    private LocalDate lastWeeklyReset;

    /**
     * 上次月重置日期
     */
    @TableField("last_monthly_reset")
    private LocalDate lastMonthlyReset;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 常量定义
    public static class Level {
        public static final String NEWBIE = "newbie";
        public static final String BRONZE = "bronze";
        public static final String SILVER = "silver";
        public static final String GOLD = "gold";
        public static final String PLATINUM = "platinum";
        public static final String DIAMOND = "diamond";
        public static final String MASTER = "master";
        public static final String GRANDMASTER = "grandmaster";
    }

    /**
     * 检查是否在保护期内
     */
    public boolean isInProtectionPeriod() {
        return protectionEndTime != null && LocalDateTime.now().isBefore(protectionEndTime);
    }

    /**
     * 获取保护期剩余小时数
     */
    public long getProtectionHoursRemaining() {
        if (!isInProtectionPeriod()) {
            return 0;
        }
        return java.time.Duration.between(LocalDateTime.now(), protectionEndTime).toHours();
    }
}
