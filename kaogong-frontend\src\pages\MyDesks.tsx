import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Avatar,
  Empty,
  Spin,
  Tabs,
  Toast,
  Row,
  Col,
  Divider
} from '@douyinfe/semi-ui';
import {
  IconUser,
  IconUserGroup,
  IconCalendar,
  IconStar,
  IconPlus,
  IconSetting,
  IconRankingCardStroked
} from '@douyinfe/semi-icons';
import { useNavigate } from 'react-router-dom';
import { deskService, DeskResponse, DeskApplicationResponse } from '../services/deskService';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 我的小桌页面
 * 显示用户创建的和加入的小桌，以及申请历史
 */
const MyDesks: React.FC = () => {
  const navigate = useNavigate();
  
  // 状态管理
  const [myDesks, setMyDesks] = useState<DeskResponse[]>([]);
  const [myApplications, setMyApplications] = useState<DeskApplicationResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('desks');

  // 加载我的小桌
  const loadMyDesks = async () => {
    try {
      setLoading(true);
      const response = await deskService.getMyDesks();
      setMyDesks(response);
    } catch (error: any) {
      console.error('加载我的小桌失败:', error);
      Toast.error(error.message || '加载我的小桌失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载我的申请
  const loadMyApplications = async () => {
    try {
      setLoading(true);
      const response = await deskService.getMyApplications();
      setMyApplications(response);
    } catch (error: any) {
      console.error('加载申请历史失败:', error);
      Toast.error(error.message || '加载申请历史失败');
    } finally {
      setLoading(false);
    }
  };

  // 进入小桌
  const handleEnterDesk = (desk: DeskResponse) => {
    navigate(`/desks/${desk.id}/dashboard`);
  };

  // 查看小桌详情
  const handleViewDetail = (desk: DeskResponse) => {
    navigate(`/desks/${desk.id}`);
  };

  // 创建新小桌
  const handleCreateDesk = () => {
    navigate('/desks');
  };

  // 取消申请
  const handleCancelApplication = async (applicationId: string) => {
    try {
      await deskService.cancelApplication(applicationId);
      Toast.success('申请已取消');
      loadMyApplications(); // 重新加载申请列表
    } catch (error: any) {
      Toast.error(error.message || '取消申请失败');
    }
  };

  // 获取申请状态标签
  const getApplicationStatusTag = (application: DeskApplicationResponse) => {
    if (application.isPending) {
      return <Tag color="orange">待审核</Tag>;
    } else if (application.isApproved) {
      return <Tag color="green">已通过</Tag>;
    } else if (application.isRejected) {
      return <Tag color="red">已拒绝</Tag>;
    }
    return <Tag>未知</Tag>;
  };

  // 标签页切换
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
    if (tabKey === 'desks') {
      loadMyDesks();
    } else if (tabKey === 'applications') {
      loadMyApplications();
    }
  };

  // 初始化加载
  useEffect(() => {
    loadMyDesks();
  }, []);

  // 渲染小桌卡片
  const renderDeskCard = (desk: DeskResponse) => (
    <Col span={8} key={desk.id}>
      <Card
        style={{ height: '100%' }}
        bodyStyle={{ padding: '20px' }}
        hoverable
      >
        {/* 小桌标题 */}
        <div style={{ marginBottom: '12px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Title heading={4} style={{ margin: 0, marginBottom: '8px' }}>
              {desk.name}
            </Title>
            {desk.isOwner && <Tag color="blue" size="small">桌长</Tag>}
          </div>
          <Text type="secondary" size="small">
            {desk.description || '暂无描述'}
          </Text>
        </div>

        {/* 桌长信息 */}
        {!desk.isOwner && (
          <div style={{ marginBottom: '12px' }}>
            <Space>
              <Avatar size="small">
                <IconUser />
              </Avatar>
              <Text size="small">
                桌长：{desk.ownerNickname || desk.ownerUsername}
              </Text>
            </Space>
          </div>
        )}

        {/* 成员信息 */}
        <div style={{ marginBottom: '12px' }}>
          <Space>
            <IconUserGroup />
            <Text size="small">
              {desk.currentMembers}/{desk.maxMembers} 人
            </Text>
            <Tag 
              color={desk.availableSlots > 0 ? 'green' : 'red'}
              size="small"
            >
              {desk.availableSlots > 0 ? `还有${desk.availableSlots}个位置` : '已满员'}
            </Tag>
          </Space>
        </div>

        {/* 创建时间 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <IconCalendar />
            <Text size="small" type="secondary">
              {new Date(desk.createdAt).toLocaleDateString()}
            </Text>
          </Space>
        </div>

        <Divider margin="12px" />

        {/* 操作按钮 */}
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button 
            size="small" 
            type="tertiary"
            onClick={() => handleViewDetail(desk)}
          >
            查看详情
          </Button>
          
          <Space>
            <Button 
              size="small" 
              type="primary"
              icon={<IconSetting />}
              onClick={() => handleEnterDesk(desk)}
            >
              进入小桌
            </Button>
            
            {desk.isOwner && (
              <Button
                size="small"
                icon={<IconRankingCardStroked />}
                onClick={() => navigate(`/desks/${desk.id}/ranking`)}
              >
                排行榜
              </Button>
            )}
          </Space>
        </Space>
      </Card>
    </Col>
  );

  // 渲染申请卡片
  const renderApplicationCard = (application: DeskApplicationResponse) => (
    <Card key={application.id} style={{ marginBottom: '16px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          <div style={{ marginBottom: '8px' }}>
            <Space>
              <Title heading={5} style={{ margin: 0 }}>
                {application.deskName}
              </Title>
              {getApplicationStatusTag(application)}
            </Space>
          </div>
          
          <Text type="secondary" size="small" style={{ display: 'block', marginBottom: '8px' }}>
            {application.deskDescription || '暂无描述'}
          </Text>
          
          <div style={{ marginBottom: '8px' }}>
            <Text strong size="small">申请理由：</Text>
            <Text size="small">{application.reason}</Text>
          </div>
          
          {application.studyPlan && (
            <div style={{ marginBottom: '8px' }}>
              <Text strong size="small">学习计划：</Text>
              <Text size="small">{application.studyPlan}</Text>
            </div>
          )}
          
          <div>
            <Space>
              <Text size="small" type="secondary">
                申请时间：{new Date(application.appliedAt).toLocaleString()}
              </Text>
              {application.processedAt && (
                <Text size="small" type="secondary">
                  处理时间：{new Date(application.processedAt).toLocaleString()}
                </Text>
              )}
            </Space>
          </div>
        </div>
        
        <div>
          <Space>
            <Button 
              size="small" 
              type="tertiary"
              onClick={() => navigate(`/desks/${application.deskId}`)}
            >
              查看小桌
            </Button>
            
            {application.isPending && (
              <Button 
                size="small" 
                type="danger"
                onClick={() => handleCancelApplication(application.id)}
              >
                取消申请
              </Button>
            )}
          </Space>
        </div>
      </div>
    </Card>
  );

  return (
    <>
      <Navigation />
      <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto', paddingTop: '104px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title heading={2} style={{ margin: 0, marginBottom: '8px' }}>
          我的小桌
        </Title>
        <Text type="secondary">
          管理您创建和加入的学习小桌，查看申请状态
        </Text>
      </div>

      {/* 标签页 */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="我的小桌" itemKey="desks">
          <Spin spinning={loading}>
            {myDesks.length > 0 ? (
              <Row gutter={[16, 16]}>
                {myDesks.map(renderDeskCard)}
              </Row>
            ) : (
              <Empty
                image={<IconStar size="extra-large" />}
                title="暂无小桌"
                description="您还没有创建或加入任何小桌"
                style={{ padding: '60px 0' }}
              >
                <Button 
                  theme="solid" 
                  type="primary" 
                  icon={<IconPlus />}
                  onClick={handleCreateDesk}
                >
                  创建小桌
                </Button>
              </Empty>
            )}
          </Spin>
        </TabPane>
        
        <TabPane tab="申请历史" itemKey="applications">
          <Spin spinning={loading}>
            {myApplications.length > 0 ? (
              <div>
                {myApplications.map(renderApplicationCard)}
              </div>
            ) : (
              <Empty
                image={<IconStar size="extra-large" />}
                title="暂无申请记录"
                description="您还没有申请加入任何小桌"
                style={{ padding: '60px 0' }}
              >
                <Button 
                  theme="solid" 
                  type="primary"
                  onClick={() => navigate('/desks')}
                >
                  去申请加入小桌
                </Button>
              </Empty>
            )}
          </Spin>
        </TabPane>
      </Tabs>
      </div>
    </>
  );
};

export default MyDesks;
