# 考公刷题记录系统 - 数据库设计文档

## 设计概述

基于功能需求文档，设计支持以下核心功能的数据库结构：

- 用户管理系统（邀请注册、认证、个人资料）
- 刷题记录管理（模块化记录、学习统计、错题本、学习计划）
- 小桌排行系统（小桌管理、排行榜、成员互动）🌟
- 信誉体系（信誉评分、质量保障、权限管理）🌟
- 考试公告系统（公告发布、搜索、时间提醒）
- 数据分析面板（个人报告、学习洞察）

## 数据库表结构设计

### 1. 用户管理模块

#### 1.1 用户表 (users)

**功能**: 存储用户基本信息和认证数据

| 字段名           | 类型      | 长度 | 约束                      | 说明               |
| ---------------- | --------- | ---- | ------------------------- | ------------------ |
| id               | VARCHAR   | 36   | PK                        | 用户唯一标识(UUID) |
| username         | VARCHAR   | 50   | UNIQUE, NOT NULL          | 用户名             |
| email            | VARCHAR   | 100  | UNIQUE, NOT NULL          | 邮箱地址           |
| password_hash    | VARCHAR   | 255  | NOT NULL                  | 密码哈希值         |
| avatar_url       | VARCHAR   | 500  | NULL                      | 头像 URL           |
| nickname         | VARCHAR   | 50   | NULL                      | 昵称               |
| target_position  | VARCHAR   | 100  | NULL                      | 目标岗位           |
| reputation_score | INT       | -    | DEFAULT 100               | 信誉分数           |
| reputation_level | VARCHAR   | 20   | DEFAULT 'newbie'          | 信誉等级           |
| system_role      | VARCHAR   | 20   | DEFAULT 'user'            | 系统角色           |
| phone            | VARCHAR   | 20   | NULL                      | 手机号             |
| is_active        | BOOLEAN   | -    | DEFAULT TRUE              | 账户状态           |
| last_login_at    | TIMESTAMP | -    | NULL                      | 最后登录时间       |
| created_at       | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间           |
| updated_at       | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 更新时间           |

**索引设计**:

- PRIMARY KEY (id)
- UNIQUE KEY uk_username (username)
- UNIQUE KEY uk_email (email)
- INDEX idx_reputation_score (reputation_score)
- INDEX idx_reputation_level (reputation_level)
- INDEX idx_system_role (system_role)

#### 1.2 邀请码表 (invite_codes)

**功能**: 管理邀请码的生成、使用和追踪

| 字段名     | 类型      | 长度 | 约束                      | 说明      |
| ---------- | --------- | ---- | ------------------------- | --------- |
| id         | VARCHAR   | 36   | PK                        | 邀请码 ID |
| code       | VARCHAR   | 20   | UNIQUE, NOT NULL          | 邀请码    |
| created_by | VARCHAR   | 36   | FK(users.id)              | 创建者 ID |
| used_by    | VARCHAR   | 36   | FK(users.id), NULL        | 使用者 ID |
| expires_at | TIMESTAMP | -    | NOT NULL                  | 过期时间  |
| used_at    | TIMESTAMP | -    | NULL                      | 使用时间  |
| is_active  | BOOLEAN   | -    | DEFAULT TRUE              | 是否有效  |
| created_at | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间  |

**索引设计**:

- PRIMARY KEY (id)
- UNIQUE KEY uk_code (code)
- INDEX idx_created_by (created_by)
- INDEX idx_used_by (used_by)
- INDEX idx_expires_at (expires_at)

### 2. 刷题记录模块

#### 2.1 刷题记录表 (study_records)

**功能**: 记录每次刷题的详细数据

| 字段名         | 类型      | 长度 | 约束                      | 说明           |
| -------------- | --------- | ---- | ------------------------- | -------------- |
| id             | VARCHAR   | 36   | PK                        | 记录 ID        |
| user_id        | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID        |
| module_type    | VARCHAR   | 50   | NOT NULL                  | 模块类型       |
| question_count | INT       | -    | NOT NULL                  | 题目数量       |
| correct_count  | INT       | -    | NOT NULL                  | 正确数量       |
| accuracy_rate  | DECIMAL   | 5,2  | NOT NULL                  | 正确率(%)      |
| study_duration | INT       | -    | NOT NULL                  | 学习时长(分钟) |
| weak_points    | JSON      | -    | NULL                      | 薄弱知识点     |
| study_date     | DATE      | -    | NOT NULL                  | 学习日期       |
| notes          | TEXT      | -    | NULL                      | 学习笔记       |
| created_at     | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间       |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_user_id (user_id)
- INDEX idx_study_date (study_date)
- INDEX idx_module_type (module_type)
- INDEX idx_user_date (user_id, study_date)

#### 2.2 错题本表 (wrong_questions)

**功能**: 存储用户的错题信息和复习状态

| 字段名           | 类型      | 长度 | 约束                      | 说明           |
| ---------------- | --------- | ---- | ------------------------- | -------------- |
| id               | VARCHAR   | 36   | PK                        | 错题 ID        |
| user_id          | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID        |
| study_record_id  | VARCHAR   | 36   | FK(study_records.id)      | 关联的学习记录 |
| question_type    | VARCHAR   | 50   | NOT NULL                  | 题目类型       |
| difficulty_level | VARCHAR   | 20   | NOT NULL                  | 难度等级       |
| question_content | TEXT      | -    | NOT NULL                  | 题目内容       |
| user_answer      | TEXT      | -    | NULL                      | 用户答案       |
| correct_answer   | TEXT      | -    | NOT NULL                  | 正确答案       |
| explanation      | TEXT      | -    | NULL                      | 解析           |
| mastery_status   | VARCHAR   | 20   | DEFAULT 'not_mastered'    | 掌握状态       |
| review_count     | INT       | -    | DEFAULT 0                 | 复习次数       |
| created_at       | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间       |
| reviewed_at      | TIMESTAMP | -    | NULL                      | 最后复习时间   |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_user_id (user_id)
- INDEX idx_mastery_status (mastery_status)
- INDEX idx_question_type (question_type)

#### 2.3 错题图片表 (wrong_question_images)

**功能**: 存储错题相关的图片信息，支持题目截图、答案图片、解析图片等

| 字段名            | 类型      | 长度 | 约束                         | 说明                                    |
| ----------------- | --------- | ---- | ---------------------------- | --------------------------------------- |
| id                | VARCHAR   | 36   | PK                           | 图片 ID                                 |
| wrong_question_id | VARCHAR   | 36   | FK(wrong_questions.id), NOT NULL | 关联的错题 ID                           |
| image_type        | VARCHAR   | 20   | NOT NULL                     | 图片类型：question\|answer\|explanation |
| file_name         | VARCHAR   | 255  | NOT NULL                     | 原始文件名                              |
| file_path         | VARCHAR   | 500  | NOT NULL                     | 文件存储路径                            |
| file_size         | BIGINT    | -    | NOT NULL                     | 文件大小(字节)                          |
| mime_type         | VARCHAR   | 100  | NOT NULL                     | MIME 类型                               |
| width             | INT       | -    | NULL                         | 图片宽度(像素)                          |
| height            | INT       | -    | NULL                         | 图片高度(像素)                          |
| sort_order        | INT       | -    | DEFAULT 0                    | 排序顺序                                |
| is_compressed     | BOOLEAN   | -    | DEFAULT FALSE                | 是否已压缩                              |
| thumbnail_path    | VARCHAR   | 500  | NULL                         | 缩略图路径                              |
| created_at        | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP    | 创建时间                                |
| updated_at        | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP    | 更新时间                                |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_wrong_question_id (wrong_question_id)
- INDEX idx_image_type (image_type)
- INDEX idx_created_at (created_at)
- INDEX idx_wrong_question_type (wrong_question_id, image_type)
- INDEX idx_wrong_question_sort (wrong_question_id, image_type, sort_order)

**约束设计**:

- FOREIGN KEY (wrong_question_id) REFERENCES wrong_questions(id) ON DELETE CASCADE
- CHECK (image_type IN ('question', 'answer', 'explanation'))
- CHECK (file_size > 0 AND file_size <= 10485760)
- CHECK (sort_order >= 0)

#### 2.4 学习计划表 (study_plans)

**功能**: 管理用户的学习计划和目标

| 字段名                 | 类型      | 长度 | 约束                      | 说明               |
| ---------------------- | --------- | ---- | ------------------------- | ------------------ |
| id                     | VARCHAR   | 36   | PK                        | 计划 ID            |
| user_id                | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID            |
| title                  | VARCHAR   | 100  | NOT NULL                  | 计划标题           |
| description            | TEXT      | -    | NULL                      | 计划描述           |
| target_exam_date       | DATE      | -    | NULL                      | 目标考试日期       |
| daily_target_questions | INT       | -    | NULL                      | 每日目标题量       |
| daily_target_time      | INT       | -    | NULL                      | 每日目标时长(分钟) |
| modules                | JSON      | -    | NOT NULL                  | 学习模块配置       |
| status                 | VARCHAR   | 20   | DEFAULT 'active'          | 计划状态           |
| created_at             | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间           |
| updated_at             | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 更新时间           |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_user_id (user_id)
- INDEX idx_status (status)
- INDEX idx_target_exam_date (target_exam_date)

### 3. 小桌系统模块 🌟

#### 3.1 小桌表 (desks)

**功能**: 存储小桌的基本信息和配置

| 字段名             | 类型      | 长度 | 约束                      | 说明         |
| ------------------ | --------- | ---- | ------------------------- | ------------ |
| id                 | VARCHAR   | 36   | PK                        | 小桌 ID      |
| name               | VARCHAR   | 100  | NOT NULL                  | 小桌名称     |
| description        | TEXT      | -    | NULL                      | 小桌描述     |
| owner_id           | VARCHAR   | 36   | FK(users.id), NOT NULL    | 桌长 ID      |
| max_members        | INT       | -    | DEFAULT 8                 | 最大成员数   |
| current_members    | INT       | -    | DEFAULT 1                 | 当前成员数   |
| auto_approve_rules | JSON      | -    | NULL                      | 自动审核规则 |
| status             | VARCHAR   | 20   | DEFAULT 'active'          | 小桌状态     |
| created_at         | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间     |
| updated_at         | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 更新时间     |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_owner_id (owner_id)
- INDEX idx_status (status)
- INDEX idx_created_at (created_at)

#### 3.2 小桌成员表 (desk_members)

**功能**: 管理小桌成员关系和状态

| 字段名         | 类型      | 长度 | 约束                      | 说明               |
| -------------- | --------- | ---- | ------------------------- | ------------------ |
| id             | VARCHAR   | 36   | PK                        | 成员关系 ID        |
| desk_id        | VARCHAR   | 36   | FK(desks.id), NOT NULL    | 小桌 ID            |
| user_id        | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID            |
| role           | VARCHAR   | 20   | DEFAULT 'member'          | 角色(owner/member) |
| status         | VARCHAR   | 20   | DEFAULT 'active'          | 成员状态           |
| join_reason    | TEXT      | -    | NULL                      | 加入理由           |
| joined_at      | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 加入时间           |
| last_active_at | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 最后活跃时间       |

**索引设计**:

- PRIMARY KEY (id)
- UNIQUE KEY uk_desk_user (desk_id, user_id)
- INDEX idx_desk_id (desk_id)
- INDEX idx_user_id (user_id)
- INDEX idx_status (status)

#### 3.3 小桌申请表 (desk_applications)

**功能**: 管理加入小桌的申请流程

| 字段名       | 类型      | 长度 | 约束                      | 说明      |
| ------------ | --------- | ---- | ------------------------- | --------- |
| id           | VARCHAR   | 36   | PK                        | 申请 ID   |
| desk_id      | VARCHAR   | 36   | FK(desks.id), NOT NULL    | 小桌 ID   |
| user_id      | VARCHAR   | 36   | FK(users.id), NOT NULL    | 申请者 ID |
| reason       | TEXT      | -    | NOT NULL                  | 申请理由  |
| study_plan   | TEXT      | -    | NULL                      | 学习计划  |
| status       | VARCHAR   | 20   | DEFAULT 'pending'         | 申请状态  |
| applied_at   | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 申请时间  |
| processed_at | TIMESTAMP | -    | NULL                      | 处理时间  |
| processed_by | VARCHAR   | 36   | FK(users.id), NULL        | 处理人 ID |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_desk_id (desk_id)
- INDEX idx_user_id (user_id)
- INDEX idx_status (status)
- INDEX idx_applied_at (applied_at)

### 4. 信誉系统模块 🌟

#### 4.1 信誉记录表 (reputation_logs)

**功能**: 记录信誉分数的变化历史和原因

| 字段名       | 类型      | 长度 | 约束                      | 说明     |
| ------------ | --------- | ---- | ------------------------- | -------- |
| id           | VARCHAR   | 36   | PK                        | 记录 ID  |
| user_id      | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID  |
| action_type  | VARCHAR   | 50   | NOT NULL                  | 行为类型 |
| score_change | INT       | -    | NOT NULL                  | 分数变化 |
| reason       | TEXT      | -    | NOT NULL                  | 变化原因 |
| metadata     | JSON      | -    | NULL                      | 额外信息 |
| created_at   | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_user_id (user_id)
- INDEX idx_action_type (action_type)
- INDEX idx_created_at (created_at)

### 5. 排行榜模块

#### 5.1 排行榜表 (rankings)

**功能**: 存储各种维度的排行榜数据

| 字段名        | 类型      | 长度 | 约束                      | 说明                 |
| ------------- | --------- | ---- | ------------------------- | -------------------- |
| id            | VARCHAR   | 36   | PK                        | 排行记录 ID          |
| desk_id       | VARCHAR   | 36   | FK(desks.id), NULL        | 小桌 ID(NULL 为全站) |
| user_id       | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID              |
| ranking_type  | VARCHAR   | 50   | NOT NULL                  | 排行类型             |
| score         | INT       | -    | NOT NULL                  | 排行分数             |
| rank_position | INT       | -    | NOT NULL                  | 排名位置             |
| calculated_at | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 计算时间             |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_desk_id (desk_id)
- INDEX idx_user_id (user_id)
- INDEX idx_ranking_type (ranking_type)
- INDEX idx_rank_position (rank_position)
- UNIQUE KEY uk_desk_user_type_date (desk_id, user_id, ranking_type, calculated_at)

### 6. 考试公告模块

#### 6.1 考试公告表 (announcements)

**功能**: 存储考试公告信息，支持全文搜索

| 字段名          | 类型      | 长度 | 约束                      | 说明         |
| --------------- | --------- | ---- | ------------------------- | ------------ |
| id              | VARCHAR   | 36   | PK                        | 公告 ID      |
| title           | VARCHAR   | 200  | NOT NULL                  | 公告标题     |
| content         | TEXT      | -    | NOT NULL                  | 公告内容     |
| region          | VARCHAR   | 50   | NULL                      | 地区         |
| exam_type       | VARCHAR   | 50   | NULL                      | 考试类型     |
| important_dates | JSON      | -    | NULL                      | 重要时间节点 |
| priority        | VARCHAR   | 20   | DEFAULT 'normal'          | 优先级       |
| status          | VARCHAR   | 20   | DEFAULT 'published'       | 状态         |
| created_by      | VARCHAR   | 36   | FK(users.id), NOT NULL    | 创建者 ID    |
| published_at    | TIMESTAMP | -    | NULL                      | 发布时间     |
| created_at      | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间     |
| updated_at      | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 更新时间     |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_region (region)
- INDEX idx_exam_type (exam_type)
- INDEX idx_published_at (published_at)
- INDEX idx_status (status)
- FULLTEXT KEY ft_title_content (title, content) WITH PARSER ngram

### 7. 通知系统模块

#### 7.1 通知表 (notifications)

**功能**: 存储系统通知和用户消息

| 字段名     | 类型      | 长度 | 约束                      | 说明     |
| ---------- | --------- | ---- | ------------------------- | -------- |
| id         | VARCHAR   | 36   | PK                        | 通知 ID  |
| user_id    | VARCHAR   | 36   | FK(users.id), NOT NULL    | 用户 ID  |
| type       | VARCHAR   | 50   | NOT NULL                  | 通知类型 |
| title      | VARCHAR   | 200  | NOT NULL                  | 通知标题 |
| content    | TEXT      | -    | NOT NULL                  | 通知内容 |
| metadata   | JSON      | -    | NULL                      | 额外数据 |
| is_read    | BOOLEAN   | -    | DEFAULT FALSE             | 是否已读 |
| created_at | TIMESTAMP | -    | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**索引设计**:

- PRIMARY KEY (id)
- INDEX idx_user_id (user_id)
- INDEX idx_type (type)
- INDEX idx_is_read (is_read)
- INDEX idx_created_at (created_at)

## 表关系 ER 图

### 1. 用户管理模块 ER 图

```mermaid
erDiagram
    users {
        string id PK
        string username UK
        string email UK
        string password_hash
        string avatar_url
        string nickname
        string target_position
        int reputation_score
        string reputation_level
        string system_role
        string phone
        bool is_active
        datetime last_login_at
        datetime created_at
        datetime updated_at
    }

    invite_codes {
        string id PK
        string code UK
        string created_by FK
        string used_by FK
        datetime expires_at
        datetime used_at
        bool is_active
        datetime created_at
    }

    users ||--o{ invite_codes : "created_by"
    users ||--o| invite_codes : "used_by"
```

### 2. 刷题记录模块 ER 图

```mermaid
erDiagram
    users {
        string id PK
        string username
        int reputation_score
        string system_role
    }

    study_records {
        string id PK
        string user_id FK
        string module_type
        int question_count
        int correct_count
        float accuracy_rate
        int study_duration
        string weak_points
        date study_date
        string notes
        datetime created_at
    }

    wrong_questions {
        string id PK
        string user_id FK
        string study_record_id FK
        string question_type
        string difficulty_level
        string question_content
        string user_answer
        string correct_answer
        string explanation
        string mastery_status
        int review_count
        datetime created_at
        datetime reviewed_at
    }

    study_plans {
        string id PK
        string user_id FK
        string title
        string description
        date target_exam_date
        int daily_target_questions
        int daily_target_time
        string modules
        string status
        datetime created_at
        datetime updated_at
    }

    users ||--o{ study_records : "user_id"
    users ||--o{ wrong_questions : "user_id"
    users ||--o{ study_plans : "user_id"
    study_records ||--o{ wrong_questions : "study_record_id"
```

### 3. 小桌系统模块 ER 图 🌟

```mermaid
erDiagram
    users {
        string id PK
        string username
        int reputation_score
        string system_role
    }

    desks {
        string id PK
        string name
        string description
        string owner_id FK
        int max_members
        int current_members
        string auto_approve_rules
        string status
        datetime created_at
        datetime updated_at
    }

    desk_members {
        string id PK
        string desk_id FK
        string user_id FK
        string role
        string status
        string join_reason
        datetime joined_at
        datetime last_active_at
    }

    desk_applications {
        string id PK
        string desk_id FK
        string user_id FK
        string reason
        string study_plan
        string status
        datetime applied_at
        datetime processed_at
        string processed_by FK
    }

    users ||--o{ desks : "owner_id"
    desks ||--o{ desk_members : "desk_id"
    users ||--o{ desk_members : "user_id"
    desks ||--o{ desk_applications : "desk_id"
    users ||--o{ desk_applications : "user_id"
    users ||--o{ desk_applications : "processed_by"
```

### 4. 信誉系统模块 ER 图 🌟

```mermaid
erDiagram
    users {
        string id PK
        string username
        int reputation_score
        string reputation_level
        string system_role
    }

    reputation_logs {
        string id PK
        string user_id FK
        string action_type
        int score_change
        string reason
        string metadata
        datetime created_at
    }

    users ||--o{ reputation_logs : "user_id"
```

### 5. 排行榜模块 ER 图

```mermaid
erDiagram
    users {
        string id PK
        string username
        string system_role
    }

    desks {
        string id PK
        string name
    }

    rankings {
        string id PK
        string desk_id FK
        string user_id FK
        string ranking_type
        int score
        int rank_position
        datetime calculated_at
    }

    desks ||--o{ rankings : "desk_id"
    users ||--o{ rankings : "user_id"
```

### 6. 考试公告模块 ER 图

```mermaid
erDiagram
    users {
        string id PK
        string username
        string system_role
    }

    announcements {
        string id PK
        string title
        string content
        string region
        string exam_type
        string important_dates
        string priority
        string status
        string created_by FK
        datetime published_at
        datetime created_at
        datetime updated_at
    }

    users ||--o{ announcements : "created_by"
```

### 7. 通知系统模块 ER 图

```mermaid
erDiagram
    users {
        string id PK
        string username
        string system_role
    }

    notifications {
        string id PK
        string user_id FK
        string type
        string title
        string content
        string metadata
        bool is_read
        datetime created_at
    }

    users ||--o{ notifications : "user_id"
```

## 整体系统 ER 图

```mermaid
erDiagram
    %% 用户管理核心
    users {
        string id PK
        string username UK
        string email UK
        string password_hash
        string avatar_url
        string nickname
        string target_position
        int reputation_score
        string reputation_level
        string system_role
        string phone
        bool is_active
        datetime last_login_at
        datetime created_at
        datetime updated_at
    }

    invite_codes {
        string id PK
        string code UK
        string created_by FK
        string used_by FK
        datetime expires_at
        datetime used_at
        bool is_active
        datetime created_at
    }

    %% 刷题记录模块
    study_records {
        string id PK
        string user_id FK
        string module_type
        int question_count
        int correct_count
        float accuracy_rate
        int study_duration
        string weak_points
        date study_date
        string notes
        datetime created_at
    }

    wrong_questions {
        string id PK
        string user_id FK
        string study_record_id FK
        string question_type
        string difficulty_level
        string question_content
        string user_answer
        string correct_answer
        string explanation
        string mastery_status
        int review_count
        datetime created_at
        datetime reviewed_at
    }

    study_plans {
        string id PK
        string user_id FK
        string title
        string description
        date target_exam_date
        int daily_target_questions
        int daily_target_time
        string modules
        string status
        datetime created_at
        datetime updated_at
    }

    %% 小桌系统模块 🌟
    desks {
        string id PK
        string name
        string description
        string owner_id FK
        int max_members
        int current_members
        string auto_approve_rules
        string status
        datetime created_at
        datetime updated_at
    }

    desk_members {
        string id PK
        string desk_id FK
        string user_id FK
        string role
        string status
        string join_reason
        datetime joined_at
        datetime last_active_at
    }

    desk_applications {
        string id PK
        string desk_id FK
        string user_id FK
        string reason
        string study_plan
        string status
        datetime applied_at
        datetime processed_at
        string processed_by FK
    }

    %% 信誉系统模块 🌟
    reputation_logs {
        string id PK
        string user_id FK
        string action_type
        int score_change
        string reason
        string metadata
        datetime created_at
    }

    %% 排行榜模块
    rankings {
        string id PK
        string desk_id FK
        string user_id FK
        string ranking_type
        int score
        int rank_position
        datetime calculated_at
    }

    %% 考试公告模块
    announcements {
        string id PK
        string title
        string content
        string region
        string exam_type
        string important_dates
        string priority
        string status
        string created_by FK
        datetime published_at
        datetime created_at
        datetime updated_at
    }

    %% 通知系统模块
    notifications {
        string id PK
        string user_id FK
        string type
        string title
        string content
        string metadata
        bool is_read
        datetime created_at
    }

    %% 关系定义
    users ||--o{ invite_codes : "created_by"
    users ||--o| invite_codes : "used_by"
    users ||--o{ study_records : "user_id"
    users ||--o{ wrong_questions : "user_id"
    users ||--o{ study_plans : "user_id"
    study_records ||--o{ wrong_questions : "study_record_id"
    users ||--o{ desks : "owner_id"
    desks ||--o{ desk_members : "desk_id"
    users ||--o{ desk_members : "user_id"
    desks ||--o{ desk_applications : "desk_id"
    users ||--o{ desk_applications : "user_id"
    users ||--o{ desk_applications : "processed_by"
    users ||--o{ reputation_logs : "user_id"
    desks ||--o{ rankings : "desk_id"
    users ||--o{ rankings : "user_id"
    users ||--o{ announcements : "created_by"
    users ||--o{ notifications : "user_id"
```

## 设计要点说明

### 1. 核心创新功能支持 🌟

- **小桌系统**: 通过 desks、desk_members、desk_applications 三表支持完整的小桌管理流程
- **信誉体系**: 通过 reputation_logs 记录所有信誉变化，支持复杂的信誉计算规则
- **角色权限管理**: 采用简单角色扩展方案，通过 users.system_role 字段实现三级权限管理

### 2. 性能优化考虑

- **索引策略**: 为高频查询字段建立合适索引
- **分区设计**: study_records 可按日期分区，提升查询性能
- **JSON 字段**: 使用 JSON 存储灵活配置，减少表结构变更

### 3. 扩展性设计

- **UUID 主键**: 支持分布式部署和数据迁移
- **软删除**: 重要数据支持软删除，保留历史记录
- **元数据字段**: 预留 metadata 字段支持功能扩展

### 4. 角色权限管理设计

**系统角色定义 (users.system_role)**:

- `user` - 普通用户（默认）：基础功能使用、小桌参与
- `admin` - 管理员：公告管理、内容审核、数据统计
- `super_admin` - 超级管理员：用户管理、系统配置、邀请码管理

**信誉等级 (users.reputation_level)**:

- `newbie` - 新手（0-99 分）
- `regular` - 常规（100-299 分）
- `expert` - 专家（300-599 分）
- `master` - 大师（600+分）

**小桌角色 (desk_members.role)**:

- `owner` - 桌长：小桌创建者和管理者
- `member` - 成员：普通参与者

**权限集成优势**:

- **Spring Security 集成**: 直接映射到 Spring Security 的 Role
- **缓存友好**: 用户信息+角色一次性缓存
- **查询高效**: 单表查询，无需复杂 JOIN
- **扩展性**: 可平滑升级到 RBAC 系统

### 5. 数据一致性保障

- **外键约束**: 确保数据引用完整性
- **唯一约束**: 防止重复数据
- **默认值**: 合理的默认值设置

这个数据库设计完整支持了考公刷题记录系统的所有核心功能，特别是小桌系统和信誉体系这两个创新功能，同时通过简单而有效的角色权限管理确保了系统的安全性，为后续的 API 设计和业务逻辑实现提供了坚实的数据基础。
