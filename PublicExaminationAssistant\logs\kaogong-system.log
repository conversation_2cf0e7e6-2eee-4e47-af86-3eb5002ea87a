2025-07-22 00:00:00.119 [scheduling-1] INFO  c.w.p.service.DailyStudyCheckTask - 开始执行每日学习检查任务，检查日期: 2025-07-21
2025-07-22 00:00:00.232 [scheduling-1] ERROR c.w.p.service.DailyStudyCheckTask - 每日学习检查任务执行失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
### The error may exist in com/wqh/publicexaminationassistant/mapper/UserReputationStatsMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT urs.* FROM user_reputation_stats urs INNER JOIN users u ON urs.user_id = u.id WHERE u.status = 'active' AND (urs.protection_end_time IS NULL OR urs.protection_end_time <= NOW())
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy97.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy111.selectActiveUsersExcludeProtected(Unknown Source)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask.checkDailyStudy(DailyStudyCheckTask.java:77)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask$$FastClassBySpringCGLIB$$23fa2239.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask$$EnhancerBySpringCGLIB$$5bc7ddbf.checkDailyStudy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy140.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy138.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy137.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor120.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 34 common frames omitted
2025-07-22 00:00:00.312 [scheduling-1] ERROR o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
### The error may exist in com/wqh/publicexaminationassistant/mapper/UserReputationStatsMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT urs.* FROM user_reputation_stats urs INNER JOIN users u ON urs.user_id = u.id WHERE u.status = 'active' AND (urs.protection_end_time IS NULL OR urs.protection_end_time <= NOW())
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy97.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:166)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy111.selectActiveUsersExcludeProtected(Unknown Source)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask.checkDailyStudy(DailyStudyCheckTask.java:77)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask$$FastClassBySpringCGLIB$$23fa2239.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.service.DailyStudyCheckTask$$EnhancerBySpringCGLIB$$5bc7ddbf.checkDailyStudy(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'u.status' in 'where clause'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at sun.reflect.GeneratedMethodAccessor115.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy140.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy138.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy137.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor120.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 34 common frames omitted
2025-07-22 00:08:57.075 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:08:57.077 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:08:57.077 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:08:57.077 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:08:57.078 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:08:57.078 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:08:57.082 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:08:57.082 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:08:57.083 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:08:57.083 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:08:57.088 [http-nio-8080-exec-9] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:08:57.088 [http-nio-8080-exec-5] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:08:57.089 [http-nio-8080-exec-9] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:08:57.089 [http-nio-8080-exec-5] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:08:57.089 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:08:57.089 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:08:57.089 [http-nio-8080-exec-5] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:08:57.089 [http-nio-8080-exec-9] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:08:57.091 [http-nio-8080-exec-5] INFO  c.w.p.service.RankingService - 获取全站排行榜: type=study_questions, period=weekly, page=1, size=50, userId=d618476e7f317cee6d51eb6192b5542f
2025-07-22 00:08:57.091 [http-nio-8080-exec-9] INFO  c.w.p.service.RankingService - 获取用户排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:08:57.192 [http-nio-8080-exec-5] INFO  c.w.p.service.RankingService - 获取全站排行榜成功: type=study_questions, period=weekly, total=1
2025-07-22 00:08:57.198 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:08:57.213 [http-nio-8080-exec-9] INFO  c.w.p.service.RankingService - 获取用户排名成功: userId=d618476e7f317cee6d51eb6192b5542f, rank=1
2025-07-22 00:08:57.215 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:10:54.123 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:10:54.123 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:10:54.125 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:10:54.125 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:10:54.125 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 00:10:54.125 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:10:54.125 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png] with attributes [permitAll]
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG c.w.p.controller.FileAccessController - 访问文件: requestPath=/api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png, filePath=avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:10:54.125 [http-nio-8080-exec-1] DEBUG c.w.p.controller.FileAccessController - 返回文件: path=uploads\avatars\2025\07\21\db73003c8e954b4ebfde12268557da1d.png, contentType=image/png, size=2430851
2025-07-22 00:10:54.125 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:10:54.125 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:10:54.125 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:10:54.125 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:10:54.135 [http-nio-8080-exec-4] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:10:54.135 [http-nio-8080-exec-2] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:10:54.135 [http-nio-8080-exec-2] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:10:54.135 [http-nio-8080-exec-4] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:10:54.135 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:10:54.135 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:10:54.135 [http-nio-8080-exec-4] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:10:54.135 [http-nio-8080-exec-2] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:10:54.135 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:10:54.135 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:10:55.491 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.818 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.819 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.819 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.819 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.820 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.821 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.821 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.821 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.821 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.821 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.821 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.822 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.825 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.826 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.826 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.826 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.826 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.826 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.826 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.826 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:38.833 [http-nio-8080-exec-4] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:38.833 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:38.833 [http-nio-8080-exec-8] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:38.833 [http-nio-8080-exec-2] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:38.833 [http-nio-8080-exec-8] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:16:38.833 [http-nio-8080-exec-4] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:16:38.833 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:16:38.834 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.833 [http-nio-8080-exec-2] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:16:38.834 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.834 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:38.834 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:38.834 [http-nio-8080-exec-8] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:16:38.834 [http-nio-8080-exec-2] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:16:38.835 [http-nio-8080-exec-4] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:16:38.835 [http-nio-8080-exec-1] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:16:38.880 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.880 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.880 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:38.880 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png] with attributes [permitAll]
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG c.w.p.controller.FileAccessController - 访问文件: requestPath=/api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png, filePath=avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:16:39.064 [http-nio-8080-exec-5] DEBUG c.w.p.controller.FileAccessController - 返回文件: path=uploads\avatars\2025\07\21\db73003c8e954b4ebfde12268557da1d.png, contentType=image/png, size=2430851
2025-07-22 00:16:40.465 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:52.417 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_accuracy&period=weekly&page=1&size=50
2025-07-22 00:16:52.417 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_accuracy&period=weekly
2025-07-22 00:16:52.417 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:52.417 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:52.417 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:52.417 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:52.422 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_accuracy&period=weekly&page=1&size=50
2025-07-22 00:16:52.422 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_accuracy&period=weekly
2025-07-22 00:16:52.422 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:52.422 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:52.426 [http-nio-8080-exec-7] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:52.426 [http-nio-8080-exec-3] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:52.426 [http-nio-8080-exec-7] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_accuracy&period=weekly] with attributes [permitAll]
2025-07-22 00:16:52.426 [http-nio-8080-exec-3] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_accuracy&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:16:52.426 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_accuracy&period=weekly&page=1&size=50
2025-07-22 00:16:52.426 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_accuracy&period=weekly
2025-07-22 00:16:52.427 [http-nio-8080-exec-3] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_accuracy, period=weekly, page=1, size=50
2025-07-22 00:16:52.427 [http-nio-8080-exec-7] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_accuracy, period=weekly
2025-07-22 00:16:52.427 [http-nio-8080-exec-7] INFO  c.w.p.service.RankingService - 获取用户排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_accuracy, period=weekly
2025-07-22 00:16:52.427 [http-nio-8080-exec-3] INFO  c.w.p.service.RankingService - 获取全站排行榜: type=study_accuracy, period=weekly, page=1, size=50, userId=d618476e7f317cee6d51eb6192b5542f
2025-07-22 00:16:52.535 [http-nio-8080-exec-3] INFO  c.w.p.service.RankingService - 获取全站排行榜成功: type=study_accuracy, period=weekly, total=1
2025-07-22 00:16:52.544 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:52.561 [http-nio-8080-exec-7] INFO  c.w.p.service.RankingService - 获取用户排名成功: userId=d618476e7f317cee6d51eb6192b5542f, rank=1
2025-07-22 00:16:52.561 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:54.057 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:54.057 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:54.057 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:54.057 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:54.057 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:54.057 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:16:54.057 [http-nio-8080-exec-1] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:16:54.057 [http-nio-8080-exec-8] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:16:54.069 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:16:54.069 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:17:30.046 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:17:30.046 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:17:30.046 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:17:30.046 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:17:30.046 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:17:30.047 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:17:30.052 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:17:30.052 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:17:30.052 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:17:30.052 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:17:30.055 [http-nio-8080-exec-6] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:17:30.055 [http-nio-8080-exec-9] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:17:30.056 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:17:30.056 [http-nio-8080-exec-9] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:17:30.056 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:17:30.056 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:17:30.056 [http-nio-8080-exec-6] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:17:30.056 [http-nio-8080-exec-9] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:17:30.063 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:17:30.063 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.615 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.615 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.615 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.615 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.615 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.616 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.617 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.617 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.617 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.618 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.619 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.620 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.626 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.626 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.626 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.626 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.628 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.628 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.629 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.629 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:39.637 [http-nio-8080-exec-3] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:39.638 [http-nio-8080-exec-9] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:39.637 [http-nio-8080-exec-6] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:39.638 [http-nio-8080-exec-3] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:29:39.638 [http-nio-8080-exec-9] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:29:39.638 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.638 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:39.638 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:29:39.638 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.638 [http-nio-8080-exec-7] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:39.640 [http-nio-8080-exec-7] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:29:39.640 [http-nio-8080-exec-3] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:29:39.640 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:39.640 [http-nio-8080-exec-9] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:29:39.640 [http-nio-8080-exec-6] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:29:39.641 [http-nio-8080-exec-7] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:29:39.654 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.654 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.654 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:39.656 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:40.047 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:29:40.047 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:40.048 [http-nio-8080-exec-10] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:29:40.048 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 00:29:40.048 [http-nio-8080-exec-10] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png] with attributes [permitAll]
2025-07-22 00:29:40.048 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:29:40.049 [http-nio-8080-exec-10] DEBUG c.w.p.controller.FileAccessController - 访问文件: requestPath=/api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png, filePath=avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 00:29:40.049 [http-nio-8080-exec-10] DEBUG c.w.p.controller.FileAccessController - 返回文件: path=uploads\avatars\2025\07\21\db73003c8e954b4ebfde12268557da1d.png, contentType=image/png, size=2430851
2025-07-22 00:29:41.829 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:51.446 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:51.447 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:51.447 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:51.449 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:51.449 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:51.450 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:51.456 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:51.458 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:51.460 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:51.460 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 00:29:51.466 [http-nio-8080-exec-2] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:51.466 [http-nio-8080-exec-2] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 00:29:51.466 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 00:29:51.473 [http-nio-8080-exec-2] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 00:29:51.479 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 00:29:51.479 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 00:29:51.479 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 00:29:51.481 [http-nio-8080-exec-1] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 00:29:51.483 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 00:29:51.485 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:16:39.102 [main] INFO  c.w.p.PublicExaminationAssistantApplication - Starting PublicExaminationAssistantApplication using Java 1.8.0_271 on 王麒鸿 with PID 7084 (E:\KGDEMO\PublicExaminationAssistant\PublicExaminationAssistant\target\classes started by Mr.麒鸿 in E:\KGDEMO\PublicExaminationAssistant\PublicExaminationAssistant)
2025-07-22 21:16:39.105 [main] DEBUG c.w.p.PublicExaminationAssistantApplication - Running with Spring Boot v2.6.13, Spring v5.3.23
2025-07-22 21:16:39.106 [main] INFO  c.w.p.PublicExaminationAssistantApplication - The following 1 profile is active: "file-upload"
2025-07-22 21:16:39.148 [main] WARN  o.s.boot.context.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 184:13]
2025-07-22 21:16:39.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-22 21:16:39.899 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-22 21:16:39.933 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-07-22 21:16:41.359 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-22 21:16:41.370 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-22 21:16:41.371 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-22 21:16:41.618 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-22 21:16:41.618 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2468 ms
2025-07-22 21:16:41.724 [main] DEBUG c.w.p.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-22 21:16:44.739 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.ReputationController.adjustUserReputation(com.wqh.publicexaminationassistant.security.JwtUserDetails,java.lang.String,int,java.lang.String)
2025-07-22 21:16:44.750 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.wqh.publicexaminationassistant.controller.ReputationController; public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.ReputationController.adjustUserReputation(com.wqh.publicexaminationassistant.security.JwtUserDetails,java.lang.String,int,java.lang.String)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-22 21:16:44.763 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.ReputationController.extendUserProtection(com.wqh.publicexaminationassistant.security.JwtUserDetails,java.lang.String,int)
2025-07-22 21:16:44.763 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.wqh.publicexaminationassistant.controller.ReputationController; public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.ReputationController.extendUserProtection(com.wqh.publicexaminationassistant.security.JwtUserDetails,java.lang.String,int)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-22 21:16:44.823 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.TaskTestController.cleanupTaskLogs(int)
2025-07-22 21:16:44.824 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.wqh.publicexaminationassistant.controller.TaskTestController; public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.TaskTestController.cleanupTaskLogs(int)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-22 21:16:44.825 [main] DEBUG o.s.s.a.p.PrePostAnnotationSecurityMetadataSource - @org.springframework.security.access.prepost.PreAuthorize(value=hasRole('ADMIN')) found on specific method: public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.TaskTestController.manualDailyStudyCheck(java.time.LocalDate)
2025-07-22 21:16:44.825 [main] DEBUG o.s.s.a.m.DelegatingMethodSecurityMetadataSource - Caching method [CacheKey[com.wqh.publicexaminationassistant.controller.TaskTestController; public com.wqh.publicexaminationassistant.common.result.ApiResponse com.wqh.publicexaminationassistant.controller.TaskTestController.manualDailyStudyCheck(java.time.LocalDate)]] with attributes [[authorize: 'hasRole('ADMIN')', filter: 'null', filterTarget: 'null']]
2025-07-22 21:16:45.255 [main] DEBUG o.s.s.c.a.a.c.AuthenticationConfiguration$DefaultPasswordEncoderAuthenticationManagerBuilder - No authenticationProviders and no parentAuthenticationManager defined. Returning null.
2025-07-22 21:16:45.355 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/v3/api-docs/**']
2025-07-22 21:16:45.356 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api-docs/**']
2025-07-22 21:16:45.356 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui/**']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-ui.html']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/swagger-resources/**']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/webjars/**']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/v1/test/**']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/v1/auth/**']
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/v1/announcements/**', GET]
2025-07-22 21:16:45.357 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/files/**', GET]
2025-07-22 21:16:45.358 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for Ant [pattern='/api/files/**', HEAD]
2025-07-22 21:16:45.358 [main] DEBUG o.s.s.w.a.e.ExpressionBasedFilterInvocationSecurityMetadataSource - Adding web access control expression [permitAll] for any request
2025-07-22 21:16:45.363 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@562919fe, org.springframework.security.web.context.SecurityContextPersistenceFilter@392781e, org.springframework.security.web.header.HeaderWriterFilter@733fb462, org.springframework.web.filter.CorsFilter@794f11cd, org.springframework.security.web.authentication.logout.LogoutFilter@a85644c, com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter@4068102e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@28cd2c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18a096b5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@715f45c6, org.springframework.security.web.session.SessionManagementFilter@67fb5025, org.springframework.security.web.access.ExceptionTranslationFilter@6c977dcf, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36c7cbe1]
2025-07-22 21:16:46.476 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-22 21:16:46.490 [main] INFO  c.w.p.PublicExaminationAssistantApplication - Started PublicExaminationAssistantApplication in 7.796 seconds (JVM running for 8.818)
2025-07-22 21:16:46.492 [main] INFO  c.w.p.util.ConfigurationValidator - 开始验证文件上传配置和环境...
2025-07-22 21:16:46.492 [main] INFO  c.w.p.util.ConfigurationValidator - ✅ 配置加载验证通过
2025-07-22 21:16:46.492 [main] INFO  c.w.p.util.ConfigurationValidator - ✅ 目录结构验证通过
2025-07-22 21:16:46.496 [main] INFO  c.w.p.util.ConfigurationValidator - ✅ 权限验证通过
2025-07-22 21:16:46.497 [main] INFO  c.w.p.util.ConfigurationValidator - ✅ 配置值验证通过
2025-07-22 21:16:46.497 [main] INFO  c.w.p.util.ConfigurationValidator - ✅ 文件上传配置验证通过，系统已准备就绪！
2025-07-22 21:16:46.497 [main] INFO  c.w.p.util.ConfigurationValidator - ==================== 文件上传配置摘要 ====================
2025-07-22 21:16:46.497 [main] INFO  c.w.p.util.ConfigurationValidator - 最大文件大小: 10 MB
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 允许的文件类型: jpg, jpeg, png, gif, webp
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 上传路径: uploads
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 图片最大尺寸: 1920x1080
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 图片质量: 85
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 缩略图尺寸: 200x200
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 存储类型: local
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - 基础URL: /api/files
2025-07-22 21:16:46.498 [main] INFO  c.w.p.util.ConfigurationValidator - ========================================================
2025-07-22 21:16:46.502 [main] INFO  c.w.p.service.RankingCalculationTask - 应用启动完成，开始执行初始排行榜计算
2025-07-22 21:16:49.515 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 开始计算所有排行榜，触发方式: 应用启动初始化
2025-07-22 21:16:49.515 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_questions, period=daily
2025-07-22 21:16:49.520 [ForkJoinPool.commonPool-worker-9] INFO  com.zaxxer.hikari.HikariDataSource - KaogongHikariCP - Starting...
2025-07-22 21:16:49.954 [ForkJoinPool.commonPool-worker-9] INFO  com.zaxxer.hikari.HikariDataSource - KaogongHikariCP - Start completed.
2025-07-22 21:16:49.984 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_questions, period=daily
2025-07-22 21:16:49.986 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算刷题数量排名: 2025-07-22T00:00 - 2025-07-22T23:59:59.999999999
2025-07-22 21:16:50.066 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算刷题数量排名完成: 共0个用户
2025-07-22 21:16:50.066 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_questions, period=daily
2025-07-22 21:16:50.115 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_questions, period=daily
2025-07-22 21:16:50.228 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_questions, period=weekly
2025-07-22 21:16:50.254 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_questions, period=weekly
2025-07-22 21:16:50.255 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算刷题数量排名: 2025-07-21T00:00 - 2025-07-27T23:59:59.999999999
2025-07-22 21:16:50.281 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算刷题数量排名完成: 共0个用户
2025-07-22 21:16:50.282 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_questions, period=weekly
2025-07-22 21:16:50.328 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_questions, period=weekly
2025-07-22 21:16:50.429 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_questions, period=monthly
2025-07-22 21:16:50.454 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_questions, period=monthly
2025-07-22 21:16:50.455 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算刷题数量排名: 2025-07-01T00:00 - 2025-07-31T23:59:59.999999999
2025-07-22 21:16:50.481 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算刷题数量排名完成: 共1个用户
2025-07-22 21:16:50.482 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_questions
2025-07-22 21:16:50.760 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_questions, period=monthly
2025-07-22 21:16:50.760 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_questions, period=monthly, records=1
2025-07-22 21:16:50.808 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_questions, period=monthly, records=1
2025-07-22 21:16:50.923 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_questions, period=all_time
2025-07-22 21:16:50.946 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_questions, period=all_time
2025-07-22 21:16:50.946 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算刷题数量排名: 2024-01-01T00:00 - 2025-07-22T21:16:50.946
2025-07-22 21:16:50.972 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算刷题数量排名完成: 共1个用户
2025-07-22 21:16:50.972 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_questions
2025-07-22 21:16:51.073 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_questions, period=all_time
2025-07-22 21:16:51.074 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_questions, period=all_time, records=1
2025-07-22 21:16:51.123 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_questions, period=all_time, records=1
2025-07-22 21:16:51.231 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_accuracy, period=daily
2025-07-22 21:16:51.256 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_accuracy, period=daily
2025-07-22 21:16:51.256 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算正确率排名: 2025-07-22T00:00 - 2025-07-22T23:59:59.999999999
2025-07-22 21:16:51.281 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算正确率排名完成: 共0个用户
2025-07-22 21:16:51.281 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_accuracy, period=daily
2025-07-22 21:16:51.328 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_accuracy, period=daily
2025-07-22 21:16:51.432 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_accuracy, period=weekly
2025-07-22 21:16:51.455 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_accuracy, period=weekly
2025-07-22 21:16:51.455 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算正确率排名: 2025-07-21T00:00 - 2025-07-27T23:59:59.999999999
2025-07-22 21:16:51.480 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算正确率排名完成: 共0个用户
2025-07-22 21:16:51.480 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_accuracy, period=weekly
2025-07-22 21:16:51.527 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_accuracy, period=weekly
2025-07-22 21:16:51.632 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_accuracy, period=monthly
2025-07-22 21:16:51.655 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_accuracy, period=monthly
2025-07-22 21:16:51.655 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算正确率排名: 2025-07-01T00:00 - 2025-07-31T23:59:59.999999999
2025-07-22 21:16:51.680 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算正确率排名完成: 共1个用户
2025-07-22 21:16:51.680 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_accuracy
2025-07-22 21:16:51.774 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_accuracy, period=monthly
2025-07-22 21:16:51.774 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_accuracy, period=monthly, records=1
2025-07-22 21:16:51.823 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_accuracy, period=monthly, records=1
2025-07-22 21:16:51.924 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_accuracy, period=all_time
2025-07-22 21:16:51.947 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_accuracy, period=all_time
2025-07-22 21:16:51.948 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算正确率排名: 2024-01-01T00:00 - 2025-07-22T21:16:51.947
2025-07-22 21:16:51.973 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算正确率排名完成: 共1个用户
2025-07-22 21:16:51.973 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_accuracy
2025-07-22 21:16:52.072 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_accuracy, period=all_time
2025-07-22 21:16:52.072 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_accuracy, period=all_time, records=1
2025-07-22 21:16:52.121 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_accuracy, period=all_time, records=1
2025-07-22 21:16:52.222 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_time, period=daily
2025-07-22 21:16:52.246 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_time, period=daily
2025-07-22 21:16:52.246 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算学习时长排名: 2025-07-22T00:00 - 2025-07-22T23:59:59.999999999
2025-07-22 21:16:52.272 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算学习时长排名完成: 共0个用户
2025-07-22 21:16:52.273 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_time, period=daily
2025-07-22 21:16:52.319 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_time, period=daily
2025-07-22 21:16:52.423 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_time, period=weekly
2025-07-22 21:16:52.447 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_time, period=weekly
2025-07-22 21:16:52.447 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算学习时长排名: 2025-07-21T00:00 - 2025-07-27T23:59:59.999999999
2025-07-22 21:16:52.472 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算学习时长排名完成: 共0个用户
2025-07-22 21:16:52.472 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=study_time, period=weekly
2025-07-22 21:16:52.517 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=study_time, period=weekly
2025-07-22 21:16:52.626 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_time, period=monthly
2025-07-22 21:16:52.650 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_time, period=monthly
2025-07-22 21:16:52.650 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算学习时长排名: 2025-07-01T00:00 - 2025-07-31T23:59:59.999999999
2025-07-22 21:16:52.677 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算学习时长排名完成: 共1个用户
2025-07-22 21:16:52.677 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_time
2025-07-22 21:16:52.775 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_time, period=monthly
2025-07-22 21:16:52.775 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_time, period=monthly, records=1
2025-07-22 21:16:52.823 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_time, period=monthly, records=1
2025-07-22 21:16:52.931 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=study_time, period=all_time
2025-07-22 21:16:52.956 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=study_time, period=all_time
2025-07-22 21:16:52.956 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算学习时长排名: 2024-01-01T00:00 - 2025-07-22T21:16:52.956
2025-07-22 21:16:52.983 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算学习时长排名完成: 共1个用户
2025-07-22 21:16:52.983 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=study_time
2025-07-22 21:16:53.083 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=study_time, period=all_time
2025-07-22 21:16:53.083 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=study_time, period=all_time, records=1
2025-07-22 21:16:53.131 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=study_time, period=all_time, records=1
2025-07-22 21:16:53.237 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=comprehensive, period=daily
2025-07-22 21:16:53.261 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=comprehensive, period=daily
2025-07-22 21:16:53.261 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算综合排名: 2025-07-22T00:00 - 2025-07-22T23:59:59.999999999
2025-07-22 21:16:53.289 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算综合排名完成: 共0个用户
2025-07-22 21:16:53.290 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=comprehensive, period=daily
2025-07-22 21:16:53.340 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=comprehensive, period=daily
2025-07-22 21:16:53.455 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=comprehensive, period=weekly
2025-07-22 21:16:53.478 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=comprehensive, period=weekly
2025-07-22 21:16:53.478 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算综合排名: 2025-07-21T00:00 - 2025-07-27T23:59:59.999999999
2025-07-22 21:16:53.504 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算综合排名完成: 共0个用户
2025-07-22 21:16:53.504 [ForkJoinPool.commonPool-worker-9] WARN  c.w.p.service.RankingService - 没有数据需要计算排行榜: type=comprehensive, period=weekly
2025-07-22 21:16:53.552 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 排行榜无数据: type=comprehensive, period=weekly
2025-07-22 21:16:53.655 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=comprehensive, period=monthly
2025-07-22 21:16:53.678 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=comprehensive, period=monthly
2025-07-22 21:16:53.678 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算综合排名: 2025-07-01T00:00 - 2025-07-31T23:59:59.999999999
2025-07-22 21:16:53.703 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算综合排名完成: 共1个用户
2025-07-22 21:16:53.703 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=comprehensive
2025-07-22 21:16:53.800 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=comprehensive, period=monthly
2025-07-22 21:16:53.800 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=comprehensive, period=monthly, records=1
2025-07-22 21:16:53.847 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=comprehensive, period=monthly, records=1
2025-07-22 21:16:53.951 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingCalculationTask - 计算排行榜: type=comprehensive, period=all_time
2025-07-22 21:16:53.974 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 开始计算排行榜: type=comprehensive, period=all_time
2025-07-22 21:16:53.975 [ForkJoinPool.commonPool-worker-9] DEBUG c.w.p.service.RankingService - 计算综合排名: 2024-01-01T00:00 - 2025-07-22T21:16:53.975
2025-07-22 21:16:54.001 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 计算综合排名完成: 共1个用户
2025-07-22 21:16:54.001 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 删除旧排行榜记录: type=comprehensive
2025-07-22 21:16:54.099 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除排行榜缓存: type=comprehensive, period=all_time
2025-07-22 21:16:54.099 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 排行榜计算完成: type=comprehensive, period=all_time, records=1
2025-07-22 21:16:54.148 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算成功: type=comprehensive, period=all_time, records=1
2025-07-22 21:16:54.262 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingCalculationTask - 排行榜计算完成统计: 成功=16, 失败=0, 总更新记录数=8
2025-07-22 21:16:54.264 [ForkJoinPool.commonPool-worker-9] INFO  c.w.p.service.RankingService - 清除所有排行榜缓存
2025-07-22 21:16:57.026 [ForkJoinPool.commonPool-worker-9] ERROR c.w.p.service.RankingCalculationTask - 清除排行榜缓存失败
org.springframework.data.redis.RedisConnectionFailureException: Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.translateException(LettuceConnectionFactory.java:1689)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1597)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1383)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1366)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedConnection(LettuceConnectionFactory.java:1093)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:421)
	at org.springframework.data.redis.cache.DefaultRedisCacheWriter.execute(DefaultRedisCacheWriter.java:304)
	at org.springframework.data.redis.cache.DefaultRedisCacheWriter.clean(DefaultRedisCacheWriter.java:209)
	at org.springframework.data.redis.cache.RedisCache.clear(RedisCache.java:211)
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doClear(AbstractCacheInvoker.java:122)
	at org.springframework.cache.interceptor.CacheAspectSupport.performCacheEvict(CacheAspectSupport.java:505)
	at org.springframework.cache.interceptor.CacheAspectSupport.processCacheEvicts(CacheAspectSupport.java:493)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:434)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:345)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.service.RankingService$$EnhancerBySpringCGLIB$$911b4fb6.clearAllRankingCache(<generated>)
	at com.wqh.publicexaminationassistant.service.RankingCalculationTask.calculateAllRankings(RankingCalculationTask.java:144)
	at com.wqh.publicexaminationassistant.service.RankingCalculationTask.lambda$onApplicationReady$0(RankingCalculationTask.java:54)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.exec(CompletableFuture.java:1632)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1067)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1703)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:172)
Caused by: io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:78)
	at io.lettuce.core.RedisConnectionException.create(RedisConnectionException.java:56)
	at io.lettuce.core.AbstractRedisClient.getConnection(AbstractRedisClient.java:330)
	at io.lettuce.core.RedisClient.connect(RedisClient.java:216)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.lambda$getConnection$1(StandaloneConnectionProvider.java:115)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.springframework.data.redis.connection.lettuce.StandaloneConnectionProvider.getConnection(StandaloneConnectionProvider.java:115)
	at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1595)
	... 25 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: localhost/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:30.108 [http-nio-8080-exec-3] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-22 21:35:30.108 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-22 21:35:30.110 [http-nio-8080-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-22 21:35:30.120 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.120 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.120 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.120 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.125 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.125 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.125 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.125 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.139 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.139 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.139 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.139 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.144 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.144 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.144 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.145 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.736 [http-nio-8080-exec-6] ERROR c.w.p.security.JwtTokenProvider - Invalid JWT token: JWT expired at 2025-07-21T16:48:26Z. Current time: 2025-07-22T13:35:30Z, a difference of 74824731 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-22 21:35:30.736 [http-nio-8080-exec-5] ERROR c.w.p.security.JwtTokenProvider - Invalid JWT token: JWT expired at 2025-07-21T16:48:26Z. Current time: 2025-07-22T13:35:30Z, a difference of 74824731 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-22 21:35:30.739 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:30.739 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:30.745 [http-nio-8080-exec-5] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/overview?period=week] with attributes [permitAll]
2025-07-22 21:35:30.745 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/trends?period=week&granularity=day] with attributes [permitAll]
2025-07-22 21:35:30.745 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.745 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.904 [http-nio-8080-exec-5] ERROR c.w.p.common.exception.GlobalExceptionHandler - 系统异常
java.lang.NullPointerException: null
	at com.wqh.publicexaminationassistant.controller.StudyRecordController.getStatisticsOverview(StudyRecordController.java:158)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$FastClassBySpringCGLIB$$aebf612f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$EnhancerBySpringCGLIB$$f53bdd65.getStatisticsOverview(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:60)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:30.904 [http-nio-8080-exec-6] ERROR c.w.p.common.exception.GlobalExceptionHandler - 系统异常
java.lang.NullPointerException: null
	at com.wqh.publicexaminationassistant.controller.StudyRecordController.getStudyTrends(StudyRecordController.java:203)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$FastClassBySpringCGLIB$$aebf612f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$EnhancerBySpringCGLIB$$f53bdd65.getStudyTrends(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:60)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:30.987 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.987 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:30.987 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.987 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.987 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.987 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:30.995 [http-nio-8080-exec-7] ERROR c.w.p.security.JwtTokenProvider - Invalid JWT token: JWT expired at 2025-07-21T16:48:26Z. Current time: 2025-07-22T13:35:30Z, a difference of 74824995 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-22 21:35:30.995 [http-nio-8080-exec-9] ERROR c.w.p.security.JwtTokenProvider - Invalid JWT token: JWT expired at 2025-07-21T16:48:26Z. Current time: 2025-07-22T13:35:30Z, a difference of 74824995 milliseconds.  Allowed clock skew: 0 milliseconds.
2025-07-22 21:35:30.995 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:30.995 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:30.995 [http-nio-8080-exec-7] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/trends?period=week&granularity=day] with attributes [permitAll]
2025-07-22 21:35:30.995 [http-nio-8080-exec-9] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/overview?period=week] with attributes [permitAll]
2025-07-22 21:35:30.995 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:30.995 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:30.995 [http-nio-8080-exec-7] ERROR c.w.p.common.exception.GlobalExceptionHandler - 系统异常
java.lang.NullPointerException: null
	at com.wqh.publicexaminationassistant.controller.StudyRecordController.getStudyTrends(StudyRecordController.java:203)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$FastClassBySpringCGLIB$$aebf612f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$EnhancerBySpringCGLIB$$f53bdd65.getStudyTrends(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:60)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:30.995 [http-nio-8080-exec-9] ERROR c.w.p.common.exception.GlobalExceptionHandler - 系统异常
java.lang.NullPointerException: null
	at com.wqh.publicexaminationassistant.controller.StudyRecordController.getStatisticsOverview(StudyRecordController.java:158)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$FastClassBySpringCGLIB$$aebf612f.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.controller.StudyRecordController$$EnhancerBySpringCGLIB$$f53bdd65.getStatisticsOverview(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:670)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:60)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:31.004 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:31.004 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:39.483 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /v1/auth/logout
2025-07-22 21:35:39.483 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:39.484 [http-nio-8080-exec-4] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/v1/auth/logout
2025-07-22 21:35:39.485 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:39.485 [http-nio-8080-exec-4] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [POST /v1/auth/logout] with attributes [permitAll]
2025-07-22 21:35:39.485 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /v1/auth/logout
2025-07-22 21:35:39.491 [http-nio-8080-exec-4] ERROR c.w.p.common.exception.GlobalExceptionHandler - 系统异常
java.lang.NullPointerException: null
	at com.wqh.publicexaminationassistant.controller.UserController.logout(UserController.java:57)
	at com.wqh.publicexaminationassistant.controller.UserController$$FastClassBySpringCGLIB$$fa04096e.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.wqh.publicexaminationassistant.controller.UserController$$EnhancerBySpringCGLIB$$e6797582.logout(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at com.wqh.publicexaminationassistant.security.JwtAuthenticationFilter.doFilterInternal(JwtAuthenticationFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-07-22 21:35:39.491 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:45.940 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/auth/login
2025-07-22 21:35:45.940 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:45.940 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:45.948 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /v1/auth/login
2025-07-22 21:35:45.948 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:45.950 [http-nio-8080-exec-8] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/v1/auth/login
2025-07-22 21:35:45.950 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:35:45.950 [http-nio-8080-exec-8] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [POST /v1/auth/login] with attributes [permitAll]
2025-07-22 21:35:45.950 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured POST /v1/auth/login
2025-07-22 21:35:45.973 [http-nio-8080-exec-8] INFO  c.w.p.controller.UserController - 用户登录请求: xph
2025-07-22 21:35:45.973 [http-nio-8080-exec-8] INFO  c.w.publicexaminationassistant.service.UserService - 用户登录请求: username=xph
2025-07-22 21:35:46.806 [http-nio-8080-exec-8] DEBUG c.w.p.service.ReputationService - 更新用户 d618476e7f317cee6d51eb6192b5542f 登录统计成功: 登录日期=2025-07-22, 连续天数=2
2025-07-22 21:35:46.854 [http-nio-8080-exec-8] DEBUG c.w.publicexaminationassistant.service.UserService - 更新用户 d618476e7f317cee6d51eb6192b5542f 登录统计: 登录日期=2025-07-22, 连续天数=2
2025-07-22 21:35:46.854 [http-nio-8080-exec-8] INFO  c.w.publicexaminationassistant.service.UserService - 用户 d618476e7f317cee6d51eb6192b5542f 登录奖励处理完成，连续登录天数: 2
2025-07-22 21:35:46.854 [http-nio-8080-exec-8] INFO  c.w.publicexaminationassistant.service.UserService - 用户登录成功: userId=d618476e7f317cee6d51eb6192b5542f, username=xph
2025-07-22 21:35:46.877 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:46.926 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:46.926 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:46.926 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:46.926 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:46.933 [http-nio-8080-exec-2] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:35:46.933 [http-nio-8080-exec-3] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:35:46.933 [http-nio-8080-exec-3] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/overview?period=week] with attributes [permitAll]
2025-07-22 21:35:46.933 [http-nio-8080-exec-2] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/trends?period=week&granularity=day] with attributes [permitAll]
2025-07-22 21:35:46.933 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:46.933 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:46.933 [http-nio-8080-exec-2] INFO  c.w.p.controller.StudyRecordController - 用户查询学习趋势: userId=d618476e7f317cee6d51eb6192b5542f, period=week, granularity=day
2025-07-22 21:35:46.933 [http-nio-8080-exec-3] INFO  c.w.p.controller.StudyRecordController - 用户查询学习统计概览: userId=d618476e7f317cee6d51eb6192b5542f, period=week
2025-07-22 21:35:46.939 [http-nio-8080-exec-2] INFO  c.w.p.service.StudyRecordService - 获取学习趋势: userId=d618476e7f317cee6d51eb6192b5542f, period=week, granularity=day
2025-07-22 21:35:46.939 [http-nio-8080-exec-3] INFO  c.w.p.service.StudyRecordService - 获取学习统计概览: userId=d618476e7f317cee6d51eb6192b5542f, period=week
2025-07-22 21:35:47.007 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:47.010 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:47.010 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:47.014 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:35:47.014 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/trends?period=week&granularity=day] with attributes [permitAll]
2025-07-22 21:35:47.014 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/trends?period=week&granularity=day
2025-07-22 21:35:47.014 [http-nio-8080-exec-1] INFO  c.w.p.controller.StudyRecordController - 用户查询学习趋势: userId=d618476e7f317cee6d51eb6192b5542f, period=week, granularity=day
2025-07-22 21:35:47.014 [http-nio-8080-exec-1] INFO  c.w.p.service.StudyRecordService - 获取学习趋势: userId=d618476e7f317cee6d51eb6192b5542f, period=week, granularity=day
2025-07-22 21:35:47.039 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:47.149 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:35:47.151 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:47.151 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:35:47.155 [http-nio-8080-exec-5] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:35:47.155 [http-nio-8080-exec-5] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/study-records/stats/overview?period=week] with attributes [permitAll]
2025-07-22 21:35:47.155 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/study-records/stats/overview?period=week
2025-07-22 21:35:47.157 [http-nio-8080-exec-5] INFO  c.w.p.controller.StudyRecordController - 用户查询学习统计概览: userId=d618476e7f317cee6d51eb6192b5542f, period=week
2025-07-22 21:35:47.157 [http-nio-8080-exec-5] INFO  c.w.p.service.StudyRecordService - 获取学习统计概览: userId=d618476e7f317cee6d51eb6192b5542f, period=week
2025-07-22 21:35:47.311 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.514 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.514 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.514 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.514 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.515 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.515 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.515 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.514 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.516 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.515 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.518 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.518 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.522 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.523 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.523 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.523 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.531 [http-nio-8080-exec-3] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:37:24.532 [http-nio-8080-exec-3] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 21:37:24.532 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.532 [http-nio-8080-exec-1] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:37:24.532 [http-nio-8080-exec-1] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 21:37:24.532 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.536 [http-nio-8080-exec-3] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 21:37:24.539 [http-nio-8080-exec-1] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 21:37:24.574 [http-nio-8080-exec-1] INFO  c.w.p.service.RankingService - 获取全站排行榜: type=study_questions, period=weekly, page=1, size=50, userId=d618476e7f317cee6d51eb6192b5542f
2025-07-22 21:37:24.574 [http-nio-8080-exec-3] INFO  c.w.p.service.RankingService - 获取用户排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 21:37:24.705 [http-nio-8080-exec-1] INFO  c.w.p.service.RankingService - 获取全站排行榜成功: type=study_questions, period=weekly, total=1
2025-07-22 21:37:24.731 [http-nio-8080-exec-3] INFO  c.w.p.service.RankingService - 获取用户排名成功: userId=d618476e7f317cee6d51eb6192b5542f, rank=1
2025-07-22 21:37:24.754 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.754 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.754 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.754 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.760 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.760 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.761 [http-nio-8080-exec-5] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:37:24.761 [http-nio-8080-exec-5] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/my-rank?type=study_questions&period=weekly] with attributes [permitAll]
2025-07-22 21:37:24.761 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/my-rank?type=study_questions&period=weekly
2025-07-22 21:37:24.761 [http-nio-8080-exec-6] DEBUG c.w.p.security.JwtAuthenticationFilter - 设置用户认证信息到SecurityContext: xph
2025-07-22 21:37:24.761 [http-nio-8080-exec-5] INFO  c.w.p.controller.RankingController - 用户查看个人排名: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly
2025-07-22 21:37:24.761 [http-nio-8080-exec-6] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50] with attributes [permitAll]
2025-07-22 21:37:24.761 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /v1/rankings/global?type=study_questions&period=weekly&page=1&size=50
2025-07-22 21:37:24.769 [http-nio-8080-exec-6] INFO  c.w.p.controller.RankingController - 用户查看全站排行榜: userId=d618476e7f317cee6d51eb6192b5542f, type=study_questions, period=weekly, page=1, size=50
2025-07-22 21:37:24.816 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.820 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG c.w.p.security.JwtAuthenticationFilter - 跳过公开端点的JWT验证: /api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG o.s.s.w.access.intercept.FilterSecurityInterceptor - Authorized filter invocation [GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png] with attributes [permitAll]
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 21:37:24.919 [http-nio-8080-exec-9] DEBUG c.w.p.controller.FileAccessController - 访问文件: requestPath=/api/files/avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png, filePath=avatars/2025/07/21/db73003c8e954b4ebfde12268557da1d.png
2025-07-22 21:37:24.939 [http-nio-8080-exec-9] DEBUG c.w.p.controller.FileAccessController - 返回文件: path=uploads\avatars\2025\07\21\db73003c8e954b4ebfde12268557da1d.png, contentType=image/png, size=2430851
2025-07-22 21:37:24.983 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
