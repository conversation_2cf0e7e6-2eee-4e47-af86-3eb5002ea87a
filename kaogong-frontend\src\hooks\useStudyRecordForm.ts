import { useState } from 'react';
import { Toast } from '@douyinfe/semi-ui';
import { studyService, StudyRecordResponse, CreateStudyRecordRequest, UpdateStudyRecordRequest } from '../services/studyService';

// 辅助函数：将Date对象转换为本地日期字符串 (YYYY-MM-DD)
const formatDateToLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 表单数据接口
export interface StudyRecordFormData {
  moduleType: string;
  questionCount: number;
  correctCount: number;
  studyDuration: number;
  studyDate: Date | string;
  notes: string;
  weakPoints: string; // 逗号分隔的字符串
}

// Hook配置接口
export interface UseStudyRecordFormOptions {
  mode: 'create' | 'edit';
  initialData?: StudyRecordResponse;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

// Hook返回值接口
export interface UseStudyRecordFormReturn {
  formData: StudyRecordFormData;
  setFormData: React.Dispatch<React.SetStateAction<StudyRecordFormData>>;
  loading: boolean;
  isDirty: boolean;
  errors: Record<string, string>;
  handleSubmit: (values: any) => Promise<void>;
  handleReset: () => void;
  validateForm: (values: any) => Record<string, string>;
}

export const useStudyRecordForm = (options: UseStudyRecordFormOptions): UseStudyRecordFormReturn => {
  const { mode, initialData, onSuccess, onError } = options;

  // 初始化表单数据
  const getInitialFormData = (): StudyRecordFormData => {
    if (mode === 'edit' && initialData) {
      return {
        moduleType: initialData.moduleType,
        questionCount: initialData.questionCount,
        correctCount: initialData.correctCount,
        studyDuration: initialData.studyDuration,
        studyDate: new Date(initialData.studyDate),
        notes: initialData.notes || '',
        weakPoints: Array.isArray(initialData.weakPoints) 
          ? initialData.weakPoints.join(', ') 
          : (initialData.weakPoints || '')
      };
    }
    
    return {
      moduleType: '',
      questionCount: 0,
      correctCount: 0,
      studyDuration: 0,
      studyDate: new Date(),
      notes: '',
      weakPoints: ''
    };
  };

  const [formData, setFormData] = useState<StudyRecordFormData>(getInitialFormData);
  const [loading, setLoading] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 表单验证
  const validateForm = (values: any): Record<string, string> => {
    const newErrors: Record<string, string> = {};

    if (mode === 'create') {
      // 创建模式：验证所有必填字段
      if (!values.moduleType) {
        newErrors.moduleType = '请选择学习模块';
      }

      if (!values.questionCount || values.questionCount <= 0) {
        newErrors.questionCount = '题目数量必须大于0';
      }

      if (values.correctCount < 0) {
        newErrors.correctCount = '正确数量不能小于0';
      }

      if (values.correctCount > values.questionCount) {
        newErrors.correctCount = '正确数量不能大于题目总数';
      }

      if (!values.studyDuration || values.studyDuration <= 0) {
        newErrors.studyDuration = '学习时长必须大于0';
      }

      if (!values.studyDate) {
        newErrors.studyDate = '请选择学习日期';
      }
    } else if (mode === 'edit') {
      // 编辑模式：只验证可编辑字段（notes和weakPoints）
      // notes 和 weakPoints 都是可选字段，不需要必填验证

      // 可以添加一些格式验证
      if (values.notes && values.notes.length > 1000) {
        newErrors.notes = '学习笔记不能超过1000个字符';
      }

      if (values.weakPoints && values.weakPoints.length > 500) {
        newErrors.weakPoints = '薄弱知识点不能超过500个字符';
      }
    }

    return newErrors;
  };

  // 处理薄弱知识点数据 - 创建模式（返回数组）
  const processWeakPointsForCreate = (weakPoints: string): string[] | null => {
    if (!weakPoints || weakPoints.trim() === '') {
      return null;
    }
    // 按逗号分割并清理空白
    const points = weakPoints.split(',').map(point => point.trim()).filter(point => point.length > 0);
    return points.length > 0 ? points : null;
  };

  // 处理薄弱知识点数据 - 编辑模式（返回字符串）
  const processWeakPointsForEdit = (weakPoints: string): string | null => {
    if (!weakPoints || weakPoints.trim() === '') {
      return null;
    }
    // 清理并格式化为逗号分隔的字符串
    const points = weakPoints.split(',').map(point => point.trim()).filter(point => point.length > 0);
    return points.length > 0 ? points.join(', ') : null;
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    setLoading(true);
    setErrors({});

    try {
      // 验证表单
      const validationErrors = validateForm(values);

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        Toast.error('请检查表单输入');
        return;
      }

      // 转换数据格式
      let studyDateStr: string;
      if (values.studyDate instanceof Date) {
        studyDateStr = formatDateToLocal(values.studyDate);
      } else if (typeof values.studyDate === 'string') {
        studyDateStr = values.studyDate.split('T')[0];
      } else {
        studyDateStr = formatDateToLocal(new Date());
      }

      if (mode === 'create') {
        // 创建记录
        const requestData: CreateStudyRecordRequest = {
          moduleType: values.moduleType,
          questionCount: Number(values.questionCount),
          correctCount: Number(values.correctCount),
          studyDuration: Number(values.studyDuration),
          studyDate: studyDateStr,
          notes: values.notes?.trim() || null,
          weakPoints: processWeakPointsForCreate(values.weakPoints)
        };

        const response = await studyService.createRecord(requestData);
        Toast.success('刷题记录创建成功！');
        onSuccess?.(response);
      } else {
        // 更新记录（只能更新notes和weakPoints）
        const requestData: UpdateStudyRecordRequest = {
          notes: values.notes?.trim() || null,
          weakPoints: processWeakPointsForEdit(values.weakPoints)
        };

        await studyService.updateRecord(initialData!.id, requestData);
        Toast.success('记录更新成功！');
        onSuccess?.(requestData);
      }

      setIsDirty(false);
    } catch (error: any) {
      console.error(`${mode === 'create' ? '创建' : '更新'}记录失败:`, error);
      const errorMessage = error.response?.data?.message || error.message || `${mode === 'create' ? '创建' : '更新'}记录失败`;
      Toast.error(errorMessage);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    setFormData(getInitialFormData());
    setErrors({});
    setIsDirty(false);
  };

  return {
    formData,
    setFormData,
    loading,
    isDirty,
    errors,
    handleSubmit,
    handleReset,
    validateForm
  };
};
