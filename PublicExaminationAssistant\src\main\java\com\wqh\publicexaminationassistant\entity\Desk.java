package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 小桌表实体类
 * 存储小桌的基本信息和配置
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("desks")
public class Desk implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 小桌ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 小桌名称
     */
    @TableField("name")
    private String name;

    /**
     * 小桌描述
     */
    @TableField("description")
    private String description;

    /**
     * 桌长ID
     */
    @TableField("owner_id")
    private String ownerId;

    /**
     * 最大成员数
     */
    @TableField("max_members")
    private Integer maxMembers;

    /**
     * 当前成员数
     */
    @TableField("current_members")
    private Integer currentMembers;

    /**
     * 自动审核规则(JSON格式)
     */
    @TableField("auto_approve_rules")
    private String autoApproveRules;

    /**
     * 小桌状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 默认构造函数
     */
    public Desk() {
        this.maxMembers = 8;
        this.currentMembers = 1;
        this.status = "active";
    }

    /**
     * 构造函数
     */
    public Desk(String name, String description, String ownerId) {
        this();
        this.name = name;
        this.description = description;
        this.ownerId = ownerId;
    }

    /**
     * 检查小桌是否已满员
     */
    public boolean isFull() {
        return this.currentMembers != null && this.maxMembers != null
               && this.currentMembers >= this.maxMembers;
    }

    /**
     * 检查小桌是否活跃
     */
    public boolean isActive() {
        return "active".equals(this.status);
    }

    /**
     * 检查小桌是否已解散
     */
    public boolean isDissolved() {
        return "dissolved".equals(this.status);
    }

    /**
     * 检查用户是否为桌长
     */
    public boolean isOwner(String userId) {
        return this.ownerId != null && this.ownerId.equals(userId);
    }

    /**
     * 获取剩余可加入人数
     */
    public int getAvailableSlots() {
        if (this.maxMembers == null || this.currentMembers == null) {
            return 0;
        }
        return Math.max(0, this.maxMembers - this.currentMembers);
    }

    /**
     * 检查是否可以加入新成员
     */
    public boolean canAcceptNewMember() {
        return isActive() && !isFull();
    }
}
