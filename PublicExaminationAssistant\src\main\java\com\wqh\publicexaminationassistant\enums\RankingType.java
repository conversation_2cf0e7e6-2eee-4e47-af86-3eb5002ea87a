package com.wqh.publicexaminationassistant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 排行榜类型枚举
 * 定义系统支持的各种排行榜类型
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Getter
@AllArgsConstructor
public enum RankingType {

    /**
     * 刷题数量排名
     */
    STUDY_QUESTIONS("study_questions", "刷题数量", "根据用户完成的题目总数进行排名"),

    /**
     * 正确率排名
     */
    STUDY_ACCURACY("study_accuracy", "正确率", "根据用户答题的平均正确率进行排名"),

    /**
     * 学习时长排名
     */
    STUDY_TIME("study_time", "学习时长", "根据用户的累计学习时长进行排名"),

    /**
     * 信誉分数排名
     */
    REPUTATION_SCORE("reputation_score", "信誉分数", "根据用户的当前信誉分数进行排名"),

    /**
     * 综合排名
     */
    COMPREHENSIVE("comprehensive", "综合排名", "根据多维度加权计算的综合分数进行排名");

    /**
     * 排行榜类型代码
     */
    private final String code;

    /**
     * 排行榜类型名称
     */
    private final String name;

    /**
     * 排行榜类型描述
     */
    private final String description;

    /**
     * 根据代码获取排行榜类型
     *
     * @param code 类型代码
     * @return 排行榜类型枚举，如果不存在则返回null
     */
    public static RankingType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (RankingType type : values()) {
            if (type.getCode().equals(code.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证排行榜类型代码是否有效
     *
     * @param code 类型代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有排行榜类型代码
     *
     * @return 类型代码数组
     */
    public static String[] getAllCodes() {
        RankingType[] types = values();
        String[] codes = new String[types.length];
        for (int i = 0; i < types.length; i++) {
            codes[i] = types[i].getCode();
        }
        return codes;
    }
}
