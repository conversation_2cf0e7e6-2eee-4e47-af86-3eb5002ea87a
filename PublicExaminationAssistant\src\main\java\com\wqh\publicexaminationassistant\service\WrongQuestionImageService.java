package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import com.wqh.publicexaminationassistant.dto.request.ImageUploadRequest;
import com.wqh.publicexaminationassistant.dto.response.ImageUploadResponse;
import com.wqh.publicexaminationassistant.entity.WrongQuestion;
import com.wqh.publicexaminationassistant.entity.WrongQuestionImage;
import com.wqh.publicexaminationassistant.mapper.WrongQuestionImageMapper;
import com.wqh.publicexaminationassistant.mapper.WrongQuestionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 错题图片业务服务
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WrongQuestionImageService {

    private final WrongQuestionImageMapper wrongQuestionImageMapper;
    private final WrongQuestionMapper wrongQuestionMapper;
    private final FileUploadService fileUploadService;
    private final ImageProcessingService imageProcessingService;
    private final FileUploadProperties fileUploadProperties;

    /**
     * 上传错题图片
     * 
     * @param wrongQuestionId 错题ID
     * @param file 上传的文件
     * @param request 上传请求参数
     * @param userId 用户ID
     * @return 图片上传响应
     */
    @Transactional
    public ImageUploadResponse uploadImage(String wrongQuestionId, MultipartFile file, 
                                         ImageUploadRequest request, String userId) {
        log.info("开始上传错题图片: wrongQuestionId={}, imageType={}, userId={}", 
                wrongQuestionId, request.getImageType(), userId);

        // 1. 验证错题所有权
        WrongQuestion wrongQuestion = validateWrongQuestionOwnership(wrongQuestionId, userId);

        // 2. 验证图片数量限制
        validateImageCount(wrongQuestionId, request.getImageType());

        try {
            // 3. 上传文件
            String subDir = "wrong-questions/" + request.getImageType();
            String filePath = fileUploadService.uploadImage(file, subDir);

            // 4. 处理图片（压缩和生成缩略图）
            ImageProcessingService.ImageDimension dimension = imageProcessingService.getImageDimension(filePath);
            String thumbnailPath = imageProcessingService.processUploadedImage(filePath);

            // 5. 保存图片记录
            WrongQuestionImage imageEntity = new WrongQuestionImage();
            imageEntity.setWrongQuestionId(wrongQuestionId);
            imageEntity.setImageType(request.getImageType());
            imageEntity.setFileName(file.getOriginalFilename());
            imageEntity.setFilePath(filePath);
            imageEntity.setFileSize(file.getSize());
            imageEntity.setMimeType(file.getContentType());
            imageEntity.setSortOrder(request.getSortOrder());
            imageEntity.setIsCompressed(dimension != null && shouldCompress(dimension));
            imageEntity.setThumbnailPath(thumbnailPath);
            imageEntity.setCreatedAt(LocalDateTime.now());

            if (dimension != null) {
                imageEntity.setWidth(dimension.getWidth());
                imageEntity.setHeight(dimension.getHeight());
            }

            int result = wrongQuestionImageMapper.insert(imageEntity);
            if (result <= 0) {
                throw new BusinessException(ResultCode.FILE_STORAGE_ERROR, "图片记录保存失败");
            }

            log.info("错题图片上传成功: imageId={}, filePath={}", imageEntity.getId(), filePath);

            // 6. 构建响应
            return buildImageUploadResponse(imageEntity);

        } catch (Exception e) {
            log.error("错题图片上传失败: wrongQuestionId={}, imageType={}", wrongQuestionId, request.getImageType(), e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_FAILED, "图片上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取错题图片列表
     * 
     * @param wrongQuestionId 错题ID
     * @param userId 用户ID
     * @return 图片列表
     */
    public List<ImageUploadResponse> getImagesByWrongQuestionId(String wrongQuestionId, String userId) {
        log.info("获取错题图片列表: wrongQuestionId={}, userId={}", wrongQuestionId, userId);

        // 验证错题所有权
        validateWrongQuestionOwnership(wrongQuestionId, userId);

        // 查询图片列表
        List<WrongQuestionImage> images = wrongQuestionImageMapper.findByWrongQuestionId(wrongQuestionId);
        
        // 转换为响应对象
        List<ImageUploadResponse> responses = new ArrayList<ImageUploadResponse>();
        for (WrongQuestionImage image : images) {
            responses.add(buildImageUploadResponse(image));
        }

        return responses;
    }

    /**
     * 删除图片
     * 
     * @param imageId 图片ID
     * @param userId 用户ID
     */
    @Transactional
    public void deleteImage(String imageId, String userId) {
        log.info("删除错题图片: imageId={}, userId={}", imageId, userId);

        // 验证图片所有权
        WrongQuestionImage image = wrongQuestionImageMapper.findByIdAndUserId(imageId, userId);
        if (image == null) {
            throw new BusinessException(ResultCode.FILE_NOT_FOUND, "图片不存在或无权限访问");
        }

        try {
            // 删除物理文件
            if (image.getFilePath() != null) {
                fileUploadService.deleteFile(image.getFilePath());
            }
            if (image.getThumbnailPath() != null) {
                fileUploadService.deleteFile(image.getThumbnailPath());
            }

            // 删除数据库记录
            int result = wrongQuestionImageMapper.deleteById(imageId);
            if (result <= 0) {
                throw new BusinessException(ResultCode.FILE_DELETE_FAILED, "图片记录删除失败");
            }

            log.info("错题图片删除成功: imageId={}", imageId);

        } catch (Exception e) {
            log.error("错题图片删除失败: imageId={}", imageId, e);
            throw new BusinessException(ResultCode.FILE_DELETE_FAILED, "图片删除失败: " + e.getMessage());
        }
    }

    /**
     * 验证错题所有权
     * 
     * @param wrongQuestionId 错题ID
     * @param userId 用户ID
     * @return 错题实体
     */
    private WrongQuestion validateWrongQuestionOwnership(String wrongQuestionId, String userId) {
        WrongQuestion wrongQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        if (wrongQuestion == null) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND, "错题不存在");
        }
        if (!wrongQuestion.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.ACCESS_DENIED, "无权限访问该错题");
        }
        return wrongQuestion;
    }

    /**
     * 验证图片数量限制
     * 
     * @param wrongQuestionId 错题ID
     * @param imageType 图片类型
     */
    private void validateImageCount(String wrongQuestionId, String imageType) {
        int currentCount = wrongQuestionImageMapper.countByWrongQuestionIdAndImageType(wrongQuestionId, imageType);
        int maxCount = getMaxImageCount(imageType);
        
        if (currentCount >= maxCount) {
            throw new BusinessException(ResultCode.FILE_SIZE_EXCEEDED,
                    String.format("该类型图片数量已达上限（%d张）", maxCount));
        }
    }

    /**
     * 获取图片类型的最大数量限制
     * 
     * @param imageType 图片类型
     * @return 最大数量
     */
    private int getMaxImageCount(String imageType) {
        // 可以根据不同类型设置不同的限制
        switch (imageType) {
            case "question":
                return 5; // 题目图片最多5张
            case "answer":
                return 3; // 答案图片最多3张
            case "explanation":
                return 5; // 解析图片最多5张
            default:
                return 3; // 默认最多3张
        }
    }

    /**
     * 判断是否需要压缩
     * 
     * @param dimension 图片尺寸
     * @return 是否需要压缩
     */
    private boolean shouldCompress(ImageProcessingService.ImageDimension dimension) {
        FileUploadProperties.ImageConfig imageConfig = fileUploadProperties.getImage();
        return dimension.getWidth() > imageConfig.getMaxWidth() || 
               dimension.getHeight() > imageConfig.getMaxHeight();
    }

    /**
     * 构建图片上传响应
     * 
     * @param image 图片实体
     * @return 响应对象
     */
    private ImageUploadResponse buildImageUploadResponse(WrongQuestionImage image) {
        ImageUploadResponse response = new ImageUploadResponse();
        BeanUtils.copyProperties(image, response);
        
        // 构建文件访问URL
        String baseUrl = fileUploadProperties.getStorage().getBaseUrl();
        response.setFileUrl(baseUrl + "/" + image.getFilePath());
        
        if (image.getThumbnailPath() != null) {
            response.setThumbnailUrl(baseUrl + "/" + image.getThumbnailPath());
        }
        
        return response;
    }
}
