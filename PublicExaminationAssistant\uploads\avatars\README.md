# 头像上传目录

这个目录用于存储用户上传的头像文件。

## 目录结构

```
avatars/
├── README.md           # 本说明文件
├── 2025/              # 按年份组织
│   └── 07/            # 按月份组织
│       └── 21/        # 按日期组织
│           ├── uuid1_avatar.jpg
│           ├── uuid2_avatar.png
│           └── ...
```

## 文件命名规则

- 文件按日期分目录存储：`YYYY/MM/DD/`
- 文件名格式：`{UUID}_{originalName}`
- 支持的格式：JPG, JPEG, PNG, GIF, WebP
- 最大文件大小：10MB

## 访问方式

头像文件可以通过以下URL访问：
```
http://localhost:8080/api/files/avatars/2025/07/21/filename.jpg
```

## 安全说明

- 所有上传的文件都会进行格式验证
- 文件大小限制为10MB
- 只允许图片格式文件
- 文件头验证防止恶意文件上传
