<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wqh.publicexaminationassistant.mapper.RankingMapper">

    <!-- 获取全站排行榜数据（分页） -->
    <select id="getGlobalRanking" resultType="java.util.Map">
        SELECT
            r.rank_position,
            r.user_id,
            COALESCE(u.username, CONCAT('用户', SUBSTRING(r.user_id, -4))) as username,
            COALESCE(u.nickname, CONCAT('用户', SUBSTRING(r.user_id, -4))) as nickname,
            u.avatar_url,
            r.score,
            COALESCE(u.reputation_level, 'newbie') as reputation_level,
            r.calculated_at,
            CASE WHEN r.user_id = #{currentUserId} THEN 1 ELSE 0 END as is_current_user,
            -- 根据排行榜类型获取不同的统计数据
            CASE 
                WHEN #{rankingType} = 'study_questions' THEN (
                    SELECT COALESCE(SUM(sr.question_count), 0) 
                    FROM study_records sr 
                    WHERE sr.user_id = r.user_id 
                    AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})
                )
                ELSE 0 
            END as total_questions,
            CASE 
                WHEN #{rankingType} = 'study_questions' THEN (
                    SELECT COALESCE(SUM(sr.correct_count), 0) 
                    FROM study_records sr 
                    WHERE sr.user_id = r.user_id 
                    AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})
                )
                ELSE 0 
            END as correct_questions,
            CASE 
                WHEN #{rankingType} = 'study_accuracy' THEN (
                    SELECT COALESCE(AVG(sr.accuracy_rate), 0) 
                    FROM study_records sr 
                    WHERE sr.user_id = r.user_id 
                    AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})
                )
                ELSE 0 
            END as accuracy_rate,
            CASE 
                WHEN #{rankingType} = 'study_time' THEN (
                    SELECT COALESCE(SUM(sr.study_duration), 0) 
                    FROM study_records sr 
                    WHERE sr.user_id = r.user_id 
                    AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})
                )
                ELSE 0 
            END as study_time,
            (
                SELECT COUNT(DISTINCT sr.study_date) 
                FROM study_records sr 
                WHERE sr.user_id = r.user_id 
                AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})
            ) as study_days,
            COALESCE(urs.consecutive_study_days, 0) as consecutive_study_days,
            COALESCE(urs.current_score, 0) as reputation_score
        FROM rankings r
        LEFT JOIN users u ON r.user_id = u.id
        LEFT JOIN user_reputation_stats urs ON r.user_id = urs.user_id
        WHERE r.desk_id IS NULL
        AND r.ranking_type = #{rankingType}
        AND r.id IN (
            -- 只获取每个用户在指定排行榜类型中的最新记录
            SELECT MAX(r2.id)
            FROM rankings r2
            WHERE r2.desk_id IS NULL
            AND r2.ranking_type = #{rankingType}
            AND r2.calculated_at BETWEEN #{startTime} AND #{endTime}
            GROUP BY r2.user_id
        )
        ORDER BY r.rank_position ASC
    </select>

    <!-- 获取用户在特定排行榜中的排名信息 -->
    <select id="getUserRank" resultType="java.util.Map">
        SELECT 
            r.rank_position as current_rank,
            (SELECT COUNT(*) FROM rankings r2 
             WHERE r2.desk_id IS NULL 
             AND r2.ranking_type = #{rankingType}
             AND r2.calculated_at BETWEEN #{startTime} AND #{endTime}) as total_participants,
            r.score,
            ROUND((1 - (r.rank_position - 1) / (
                SELECT COUNT(*) FROM rankings r3 
                WHERE r3.desk_id IS NULL 
                AND r3.ranking_type = #{rankingType}
                AND r3.calculated_at BETWEEN #{startTime} AND #{endTime}
            )) * 100, 2) as percentile,
            r.calculated_at,
            -- 距离上一名的分数差距
            (SELECT (r4.score - r.score) FROM rankings r4 
             WHERE r4.desk_id IS NULL 
             AND r4.ranking_type = #{rankingType}
             AND r4.calculated_at BETWEEN #{startTime} AND #{endTime}
             AND r4.rank_position = r.rank_position - 1) as score_gap_to_next,
            -- 距离下一名的分数差距
            (SELECT (r.score - r5.score) FROM rankings r5 
             WHERE r5.desk_id IS NULL 
             AND r5.ranking_type = #{rankingType}
             AND r5.calculated_at BETWEEN #{startTime} AND #{endTime}
             AND r5.rank_position = r.rank_position + 1) as score_gap_to_previous,
            -- 用户详细统计数据
            (SELECT COALESCE(SUM(sr.question_count), 0) 
             FROM study_records sr 
             WHERE sr.user_id = #{userId} 
             AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})) as total_questions,
            (SELECT COALESCE(SUM(sr.correct_count), 0) 
             FROM study_records sr 
             WHERE sr.user_id = #{userId} 
             AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})) as correct_questions,
            (SELECT COALESCE(AVG(sr.accuracy_rate), 0) 
             FROM study_records sr 
             WHERE sr.user_id = #{userId} 
             AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})) as accuracy_rate,
            (SELECT COALESCE(SUM(sr.study_duration), 0) 
             FROM study_records sr 
             WHERE sr.user_id = #{userId} 
             AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})) as study_time,
            (SELECT COUNT(DISTINCT sr.study_date) 
             FROM study_records sr 
             WHERE sr.user_id = #{userId} 
             AND sr.study_date BETWEEN DATE(#{startTime}) AND DATE(#{endTime})) as study_days,
            (SELECT COALESCE(urs.consecutive_study_days, 0) 
             FROM user_reputation_stats urs 
             WHERE urs.user_id = #{userId}) as consecutive_study_days,
            (SELECT COALESCE(urs.current_score, 0) 
             FROM user_reputation_stats urs 
             WHERE urs.user_id = #{userId}) as reputation_score
        FROM rankings r
        WHERE r.user_id = #{userId}
        AND r.desk_id IS NULL 
        AND r.ranking_type = #{rankingType}
        AND r.calculated_at BETWEEN #{startTime} AND #{endTime}
        ORDER BY r.calculated_at DESC
        LIMIT 1
    </select>

    <!-- 获取排行榜统计信息 -->
    <select id="getRankingStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_participants,
            ROUND(AVG(r.score), 2) as average_score,
            (SELECT r2.score FROM rankings r2 
             WHERE r2.desk_id IS NULL 
             AND r2.ranking_type = #{rankingType}
             AND r2.calculated_at BETWEEN #{startTime} AND #{endTime}
             ORDER BY r2.rank_position 
             LIMIT 1 OFFSET (COUNT(*) DIV 2)) as median_score,
            MAX(r.score) as max_score,
            MIN(r.score) as min_score,
            (SELECT r3.score FROM rankings r3 
             WHERE r3.desk_id IS NULL 
             AND r3.ranking_type = #{rankingType}
             AND r3.calculated_at BETWEEN #{startTime} AND #{endTime}
             ORDER BY r3.rank_position 
             LIMIT 1 OFFSET (COUNT(*) DIV 10)) as top_percentile_score,
            MAX(r.calculated_at) as updated_at
        FROM rankings r
        WHERE r.desk_id IS NULL 
        AND r.ranking_type = #{rankingType}
        AND r.calculated_at BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 获取分数分布数据 -->
    <select id="getScoreDistribution" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN score BETWEEN 0 AND 100 THEN '0-100'
                WHEN score BETWEEN 101 AND 500 THEN '101-500'
                WHEN score BETWEEN 501 AND 1000 THEN '501-1000'
                WHEN score BETWEEN 1001 AND 2000 THEN '1001-2000'
                WHEN score BETWEEN 2001 AND 5000 THEN '2001-5000'
                ELSE '5000+'
            END as range,
            CASE 
                WHEN score BETWEEN 0 AND 100 THEN 0
                WHEN score BETWEEN 101 AND 500 THEN 101
                WHEN score BETWEEN 501 AND 1000 THEN 501
                WHEN score BETWEEN 1001 AND 2000 THEN 1001
                WHEN score BETWEEN 2001 AND 5000 THEN 2001
                ELSE 5000
            END as min_score,
            CASE 
                WHEN score BETWEEN 0 AND 100 THEN 100
                WHEN score BETWEEN 101 AND 500 THEN 500
                WHEN score BETWEEN 501 AND 1000 THEN 1000
                WHEN score BETWEEN 1001 AND 2000 THEN 2000
                WHEN score BETWEEN 2001 AND 5000 THEN 5000
                ELSE 999999
            END as max_score,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (
                SELECT COUNT(*) FROM rankings r2 
                WHERE r2.desk_id IS NULL 
                AND r2.ranking_type = #{rankingType}
                AND r2.calculated_at BETWEEN #{startTime} AND #{endTime}
            ), 2) as percentage
        FROM rankings r
        WHERE r.desk_id IS NULL 
        AND r.ranking_type = #{rankingType}
        AND r.calculated_at BETWEEN #{startTime} AND #{endTime}
        GROUP BY 
            CASE 
                WHEN score BETWEEN 0 AND 100 THEN '0-100'
                WHEN score BETWEEN 101 AND 500 THEN '101-500'
                WHEN score BETWEEN 501 AND 1000 THEN '501-1000'
                WHEN score BETWEEN 1001 AND 2000 THEN '1001-2000'
                WHEN score BETWEEN 2001 AND 5000 THEN '2001-5000'
                ELSE '5000+'
            END
        ORDER BY min_score
    </select>

    <!-- 批量插入或更新排行榜记录 -->
    <insert id="batchInsertOrUpdate">
        INSERT INTO rankings (id, desk_id, user_id, ranking_type, score, rank_position, calculated_at)
        VALUES
        <foreach collection="rankings" item="ranking" separator=",">
            (#{ranking.id}, #{ranking.deskId}, #{ranking.userId}, #{ranking.rankingType}, 
             #{ranking.score}, #{ranking.rankPosition}, #{ranking.calculatedAt})
        </foreach>
        ON DUPLICATE KEY UPDATE
        score = VALUES(score),
        rank_position = VALUES(rank_position),
        calculated_at = VALUES(calculated_at)
    </insert>

    <!-- 删除指定时间之前的排行榜记录 -->
    <delete id="deleteOldRankings">
        DELETE FROM rankings
        WHERE calculated_at &lt; #{beforeTime}
        <if test="rankingType != null and rankingType != ''">
            AND ranking_type = #{rankingType}
        </if>
    </delete>

    <!-- 删除指定类型和时间范围的排行榜记录 -->
    <delete id="deleteByTypeAndPeriod">
        DELETE FROM rankings
        WHERE desk_id IS NULL
        AND ranking_type = #{rankingType}
        AND calculated_at BETWEEN #{startTime} AND #{endTime}
    </delete>

    <!-- 获取用户排名变化 -->
    <select id="getUserRankChange" resultType="java.util.Map">
        SELECT 
            current_rank.rank_position as current_rank,
            previous_rank.rank_position as previous_rank,
            (previous_rank.rank_position - current_rank.rank_position) as rank_change
        FROM 
            (SELECT rank_position FROM rankings 
             WHERE user_id = #{userId} 
             AND desk_id IS NULL 
             AND ranking_type = #{rankingType}
             AND calculated_at >= #{currentStartTime}
             ORDER BY calculated_at DESC LIMIT 1) current_rank,
            (SELECT rank_position FROM rankings 
             WHERE user_id = #{userId} 
             AND desk_id IS NULL 
             AND ranking_type = #{rankingType}
             AND calculated_at BETWEEN #{previousStartTime} AND #{previousEndTime}
             ORDER BY calculated_at DESC LIMIT 1) previous_rank
    </select>

</mapper>
