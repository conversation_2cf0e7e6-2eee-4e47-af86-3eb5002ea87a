package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * 文件上传服务
 * 提供文件上传、验证、存储等核心功能
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadService {

    private final FileUploadProperties fileUploadProperties;

    // JDK 1.8兼容：使用HashSet构造而不是Set.of()
    private static final Set<String> ALLOWED_MIME_TYPES = new HashSet<String>(Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    ));

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    /**
     * 上传图片文件
     * 
     * @param file 上传的文件
     * @param subDir 子目录（如：wrong-questions/question）
     * @return 文件相对路径
     * @throws IOException 文件操作异常
     */
    public String uploadImage(MultipartFile file, String subDir) throws IOException {
        log.info("开始上传图片文件: fileName={}, size={}, subDir={}", 
                file.getOriginalFilename(), file.getSize(), subDir);

        // 1. 验证文件
        validateImageFile(file);

        // 2. 生成文件名和路径
        String fileName = generateFileName(file.getOriginalFilename());
        String datePath = LocalDateTime.now().format(DATE_FORMATTER);
        String relativePath = subDir + "/" + datePath + "/" + fileName;

        // 3. 创建目录
        Path uploadDir = createUploadDirectory(subDir, datePath);

        // 4. 保存文件
        Path filePath = uploadDir.resolve(fileName);
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

        log.info("图片文件上传成功: filePath={}, size={}", filePath, file.getSize());
        return relativePath;
    }

    /**
     * 删除文件
     * 
     * @param relativePath 文件相对路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String relativePath) {
        if (!StringUtils.hasText(relativePath)) {
            return false;
        }

        try {
            Path filePath = getAbsolutePath(relativePath);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", relativePath);
                return true;
            } else {
                log.warn("文件不存在，无法删除: {}", relativePath);
                return false;
            }
        } catch (IOException e) {
            log.error("文件删除失败: relativePath={}", relativePath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     * 
     * @param relativePath 文件相对路径
     * @return 文件是否存在
     */
    public boolean fileExists(String relativePath) {
        if (!StringUtils.hasText(relativePath)) {
            return false;
        }

        try {
            Path filePath = getAbsolutePath(relativePath);
            return Files.exists(filePath);
        } catch (Exception e) {
            log.error("检查文件存在性失败: relativePath={}", relativePath, e);
            return false;
        }
    }

    /**
     * 获取文件绝对路径
     * 
     * @param relativePath 相对路径
     * @return 绝对路径
     */
    public Path getAbsolutePath(String relativePath) {
        String projectRoot = System.getProperty("user.dir");
        return Paths.get(projectRoot, fileUploadProperties.getPath(), relativePath);
    }

    /**
     * 验证图片文件
     * 
     * @param file 上传的文件
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.INVALID_PARAM, "文件不能为空");
        }

        // 验证文件大小
        if (!fileUploadProperties.isAllowedSize(file.getSize())) {
            long maxSizeMB = fileUploadProperties.getMaxSize() / 1024 / 1024;
            throw new BusinessException(ResultCode.FILE_SIZE_EXCEEDED,
                    "文件大小超过限制，最大允许" + maxSizeMB + "MB");
        }

        // 验证MIME类型
        String contentType = file.getContentType();
        if (!ALLOWED_MIME_TYPES.contains(contentType)) {
            throw new BusinessException(ResultCode.INVALID_FILE_TYPE,
                    "不支持的文件类型: " + contentType + "，只支持图片格式");
        }

        // 验证文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (!fileUploadProperties.isAllowedType(originalFilename)) {
            throw new BusinessException(ResultCode.INVALID_FILE_TYPE,
                    "不支持的文件扩展名，只支持: " + String.join(", ", fileUploadProperties.getAllowedTypes()));
        }

        // 验证文件头（防止文件伪造）
        validateFileHeader(file);
    }

    /**
     * 验证文件头（简单的文件类型验证）
     * 
     * @param file 上传的文件
     */
    private void validateFileHeader(MultipartFile file) {
        try {
            byte[] header = new byte[8];
            int bytesRead = file.getInputStream().read(header);
            
            if (bytesRead < 2) {
                throw new BusinessException(ResultCode.INVALID_FILE_FORMAT, "文件内容无效");
            }

            // 检查常见图片格式的文件头
            if (isJpegFile(header) || isPngFile(header) || isGifFile(header) || isWebpFile(header)) {
                return; // 验证通过
            }

            throw new BusinessException(ResultCode.INVALID_FILE_FORMAT, "文件格式验证失败，请确保上传的是有效的图片文件");

        } catch (IOException e) {
            log.error("文件头验证失败", e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_FAILED, "文件读取失败");
        }
    }

    /**
     * 检查是否为JPEG文件
     */
    private boolean isJpegFile(byte[] header) {
        return header.length >= 2 && 
               (header[0] & 0xFF) == 0xFF && 
               (header[1] & 0xFF) == 0xD8;
    }

    /**
     * 检查是否为PNG文件
     */
    private boolean isPngFile(byte[] header) {
        return header.length >= 8 && 
               (header[0] & 0xFF) == 0x89 && 
               (header[1] & 0xFF) == 0x50 && 
               (header[2] & 0xFF) == 0x4E && 
               (header[3] & 0xFF) == 0x47;
    }

    /**
     * 检查是否为GIF文件
     */
    private boolean isGifFile(byte[] header) {
        return header.length >= 6 && 
               header[0] == 'G' && header[1] == 'I' && header[2] == 'F' &&
               header[3] == '8' && (header[4] == '7' || header[4] == '9') && header[5] == 'a';
    }

    /**
     * 检查是否为WebP文件
     */
    private boolean isWebpFile(byte[] header) {
        return header.length >= 8 && 
               header[0] == 'R' && header[1] == 'I' && header[2] == 'F' && header[3] == 'F' &&
               header[8] == 'W' && header[9] == 'E' && header[10] == 'B' && header[11] == 'P';
    }

    /**
     * 生成唯一文件名
     * 
     * @param originalFilename 原始文件名
     * @return 新的文件名
     */
    private String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid + "." + extension;
    }

    /**
     * 获取文件扩展名
     * 
     * @param filename 文件名
     * @return 扩展名（小写）
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 创建上传目录
     * 
     * @param subDir 子目录
     * @param datePath 日期路径
     * @return 创建的目录路径
     * @throws IOException 目录创建失败
     */
    private Path createUploadDirectory(String subDir, String datePath) throws IOException {
        String projectRoot = System.getProperty("user.dir");
        Path uploadDir = Paths.get(projectRoot, fileUploadProperties.getPath(), subDir, datePath);
        
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
            log.debug("创建上传目录: {}", uploadDir);
        }
        
        return uploadDir;
    }
}
