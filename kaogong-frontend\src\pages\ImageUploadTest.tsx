import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Divider,
  Toast,
  Spin
} from '@douyinfe/semi-ui';
import ImageUpload, { ImageInfo as UploadImageInfo } from '../components/ImageUpload';
import ImagePreview from '../components/ImagePreview';
import { imageService, ImageInfo } from '../services/studyService';
import '../styles/image-upload.css';

const { Title, Text } = Typography;

const ImageUploadTest: React.FC = () => {
  const [questionImages, setQuestionImages] = useState<UploadImageInfo[]>([]);
  const [answerImages, setAnswerImages] = useState<UploadImageInfo[]>([]);
  const [explanationImages, setExplanationImages] = useState<UploadImageInfo[]>([]);
  const [previewImages, setPreviewImages] = useState<ImageInfo[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟错题ID（实际使用时应该是真实的错题ID）
  const mockWrongQuestionId = 'test-wrong-question-id';

  // 模拟获取图片列表
  const fetchImages = async () => {
    try {
      setLoading(true);
      // 这里应该调用真实的API
      // const images = await imageService.getWrongQuestionImages(mockWrongQuestionId);
      
      // 模拟数据
      const mockImages: ImageInfo[] = [
        {
          id: '1',
          imageType: 'question',
          fileName: 'question1.jpg',
          fileUrl: 'https://via.placeholder.com/400x300/4CAF50/white?text=Question+1',
          fileSize: 102400,
          width: 400,
          height: 300,
          sortOrder: 0,
          thumbnailUrl: 'https://via.placeholder.com/200x200/4CAF50/white?text=Q1',
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          imageType: 'answer',
          fileName: 'answer1.jpg',
          fileUrl: 'https://via.placeholder.com/400x300/2196F3/white?text=Answer+1',
          fileSize: 98304,
          width: 400,
          height: 300,
          sortOrder: 0,
          thumbnailUrl: 'https://via.placeholder.com/200x200/2196F3/white?text=A1',
          createdAt: new Date().toISOString()
        },
        {
          id: '3',
          imageType: 'explanation',
          fileName: 'explanation1.jpg',
          fileUrl: 'https://via.placeholder.com/400x300/FF9800/white?text=Explanation+1',
          fileSize: 112640,
          width: 400,
          height: 300,
          sortOrder: 0,
          thumbnailUrl: 'https://via.placeholder.com/200x200/FF9800/white?text=E1',
          createdAt: new Date().toISOString()
        }
      ];
      
      setPreviewImages(mockImages);
      Toast.success('图片列表加载成功');
    } catch (error: any) {
      Toast.error(`加载失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 清空所有图片
  const clearAllImages = () => {
    setQuestionImages([]);
    setAnswerImages([]);
    setExplanationImages([]);
    setPreviewImages([]);
    Toast.success('已清空所有图片');
  };

  // 模拟删除图片
  const handleDeleteImage = async (imageId: string) => {
    try {
      // 这里应该调用真实的删除API
      // await imageService.deleteImage(imageId);
      
      setPreviewImages(prev => prev.filter(img => img.id !== imageId));
      Toast.success('图片删除成功');
    } catch (error: any) {
      Toast.error(`删除失败: ${error.message}`);
    }
  };

  // 统计信息
  const totalUploadImages = questionImages.length + answerImages.length + explanationImages.length;
  const totalPreviewImages = previewImages.length;

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>图片上传功能测试</Title>
        <Text type="secondary">
          测试错题图片上传、预览、删除等功能
        </Text>
        
        <Divider margin="24px" />

        {/* 统计信息 */}
        <Card 
          style={{ marginBottom: '24px', backgroundColor: 'var(--semi-color-fill-0)' }}
          bodyStyle={{ padding: '16px' }}
        >
          <Space>
            <Text>待上传图片: <Text strong>{totalUploadImages}</Text> 张</Text>
            <Text>已上传图片: <Text strong>{totalPreviewImages}</Text> 张</Text>
            <Button size="small" onClick={fetchImages} loading={loading}>
              刷新列表
            </Button>
            <Button size="small" type="danger" onClick={clearAllImages}>
              清空所有
            </Button>
          </Space>
        </Card>

        {/* 图片上传区域 */}
        <div style={{ marginBottom: '32px' }}>
          <Title heading={4}>图片上传测试</Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            测试不同类型图片的上传功能（不会真实上传到服务器）
          </Text>
          
          <Space direction="vertical" spacing="large" style={{ width: '100%' }}>
            {/* 题目图片上传 */}
            <div>
              <Text strong style={{ marginBottom: '8px', display: 'block' }}>
                题目图片 ({questionImages.length}/5)
              </Text>
              <ImageUpload
                imageType="question"
                maxCount={5}
                value={questionImages}
                onChange={setQuestionImages}
                // wrongQuestionId={mockWrongQuestionId} // 注释掉以避免真实上传
              />
            </div>

            {/* 答案图片上传 */}
            <div>
              <Text strong style={{ marginBottom: '8px', display: 'block' }}>
                答案图片 ({answerImages.length}/3)
              </Text>
              <ImageUpload
                imageType="answer"
                maxCount={3}
                value={answerImages}
                onChange={setAnswerImages}
                // wrongQuestionId={mockWrongQuestionId} // 注释掉以避免真实上传
              />
            </div>

            {/* 解析图片上传 */}
            <div>
              <Text strong style={{ marginBottom: '8px', display: 'block' }}>
                解析图片 ({explanationImages.length}/5)
              </Text>
              <ImageUpload
                imageType="explanation"
                maxCount={5}
                value={explanationImages}
                onChange={setExplanationImages}
                // wrongQuestionId={mockWrongQuestionId} // 注释掉以避免真实上传
              />
            </div>
          </Space>
        </div>

        <Divider margin="32px" />

        {/* 图片预览区域 */}
        <div>
          <Title heading={4}>图片预览测试</Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: '16px' }}>
            测试图片预览、放大查看、删除等功能
          </Text>
          
          {loading ? (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px' }}>
                <Text type="secondary">加载中...</Text>
              </div>
            </div>
          ) : (
            <ImagePreview 
              images={previewImages}
              editable={true}
              onDelete={handleDeleteImage}
            />
          )}
        </div>

        <Divider margin="32px" />

        {/* 功能说明 */}
        <Card 
          title="功能说明"
          style={{ backgroundColor: 'var(--semi-color-fill-0)' }}
        >
          <Space direction="vertical" spacing="small">
            <Text>• <Text strong>拖拽上传:</Text> 支持将图片文件拖拽到上传区域</Text>
            <Text>• <Text strong>点击上传:</Text> 点击上传区域选择文件</Text>
            <Text>• <Text strong>文件验证:</Text> 自动验证文件类型和大小</Text>
            <Text>• <Text strong>图片预览:</Text> 实时预览上传的图片</Text>
            <Text>• <Text strong>进度显示:</Text> 显示上传进度（模拟）</Text>
            <Text>• <Text strong>错误处理:</Text> 处理上传失败的情况</Text>
            <Text>• <Text strong>图片管理:</Text> 支持查看大图、下载、删除</Text>
            <Text>• <Text strong>响应式设计:</Text> 适配不同屏幕尺寸</Text>
          </Space>
        </Card>

        {/* 技术规格 */}
        <Card 
          title="技术规格"
          style={{ marginTop: '16px', backgroundColor: 'var(--semi-color-fill-0)' }}
        >
          <Space direction="vertical" spacing="small">
            <Text>• <Text strong>支持格式:</Text> JPG, PNG, GIF, WebP</Text>
            <Text>• <Text strong>文件大小:</Text> 最大 10MB</Text>
            <Text>• <Text strong>数量限制:</Text> 题目图片5张，答案图片3张，解析图片5张</Text>
            <Text>• <Text strong>图片处理:</Text> 自动压缩和生成缩略图</Text>
            <Text>• <Text strong>存储方式:</Text> 按日期分类存储</Text>
          </Space>
        </Card>
      </Card>
    </div>
  );
};

export default ImageUploadTest;
