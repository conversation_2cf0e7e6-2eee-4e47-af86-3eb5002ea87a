# 信誉体系模块 API 接口文档 🌟

## 模块概述
信誉体系是本系统的核心创新功能，通过用户行为记录和信誉分数计算，建立质量保障机制，提升用户参与质量和系统可信度。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 信誉查询 | 获取个人信誉信息 | `/api/v1/reputation/profile` | GET | 无 | `{"code": 200, "data": {"userId": "uuid", "currentScore": 350, "level": "expert", "nextLevelScore": 600, "progress": 58.3, "rank": 156, "totalUsers": 1000}}` | user | 个人信誉概览 |
| 信誉查询 | 获取信誉变化记录 | `/api/v1/reputation/logs` | GET | `page=1&size=20&actionType=string&startDate=date&endDate=date&sortBy=createdAt` | `{"code": 200, "data": {"records": [...], "total": 50, "scoreChange": "+25"}}` | user | 个人信誉变化历史 |
| 信誉查询 | 获取信誉等级说明 | `/api/v1/reputation/levels` | GET | 无 | `{"code": 200, "data": [{"level": "newbie", "minScore": 0, "maxScore": 99, "name": "新手", "privileges": ["基础功能"], "color": "#gray"}, {"level": "regular", "minScore": 100, "maxScore": 299, "name": "常规", "privileges": ["创建小桌"], "color": "#blue"}]}` | 无 | 信誉等级体系说明 |
| 信誉查询 | 获取用户信誉详情 | `/api/v1/reputation/users/{userId}` | GET | 路径参数: `userId` | `{"code": 200, "data": {"userId": "uuid", "username": "string", "currentScore": 350, "level": "expert", "recentActions": [...], "achievements": [...]}}` | user | 查看其他用户信誉 |
| 信誉计算 | 记录学习行为 | `/api/v1/reputation/actions/study` | POST | `{"actionType": "daily_study", "studyDuration": 120, "questionCount": 50, "accuracyRate": 85.0, "metadata": {"moduleType": "math"}}` | `{"code": 200, "message": "行为记录成功", "data": {"scoreChange": "+5", "newScore": 355}}` | user | 系统自动调用 |
| 信誉计算 | 记录社交行为 | `/api/v1/reputation/actions/social` | POST | `{"actionType": "help_member", "targetUserId": "uuid", "deskId": "uuid", "metadata": {"helpType": "answer_question"}}` | `{"code": 200, "message": "行为记录成功", "data": {"scoreChange": "+3", "newScore": 358}}` | user | 小桌互助行为 |
| 信誉计算 | 记录违规行为 | `/api/v1/reputation/actions/violation` | POST | `{"actionType": "spam_content", "targetUserId": "uuid", "severity": "minor", "reason": "发布垃圾内容", "metadata": {"contentId": "uuid"}}` | `{"code": 200, "message": "违规记录成功", "data": {"scoreChange": "-10", "newScore": 340}}` | admin | 管理员操作 |
| 信誉计算 | 手动调整信誉 | `/api/v1/reputation/adjust` | POST | `{"userId": "uuid", "scoreChange": -20, "reason": "恶意刷分", "actionType": "manual_adjust", "metadata": {"adminId": "uuid"}}` | `{"code": 200, "message": "信誉调整成功"}` | admin | 管理员手动调整 |
| 信誉计算 | 批量重新计算 | `/api/v1/reputation/recalculate` | POST | `{"userIds": ["uuid1", "uuid2"], "period": "last_month", "force": true}` | `{"code": 200, "message": "重新计算完成", "data": {"processed": 2, "updated": 1}}` | admin | 批量重算信誉分数 |
| 排行榜 | 获取信誉排行榜 | `/api/v1/reputation/leaderboard` | GET | `type=global|desk&deskId=uuid&period=all|month|week&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "score": 850, "level": "master", "change": "+5"}], "total": 1000, "myRank": 156}}` | user | 多维度排行榜 |
| 排行榜 | 获取等级分布 | `/api/v1/reputation/distribution` | GET | `deskId=uuid&period=month` | `{"code": 200, "data": [{"level": "newbie", "count": 100, "percentage": 10.0}, {"level": "regular", "count": 500, "percentage": 50.0}]}` | user | 信誉等级分布统计 |
| 排行榜 | 获取信誉趋势 | `/api/v1/reputation/trends` | GET | `userId=uuid&period=3months&granularity=week` | `{"code": 200, "data": [{"period": "2024-W01", "score": 320, "change": "+15"}, {"period": "2024-W02", "score": 335, "change": "+15"}]}` | user | 信誉分数变化趋势 |
| 行为分析 | 获取行为统计 | `/api/v1/reputation/actions/stats` | GET | `userId=uuid&period=month&groupBy=actionType` | `{"code": 200, "data": [{"actionType": "daily_study", "count": 25, "totalScore": "+125"}, {"actionType": "help_member", "count": 8, "totalScore": "+24"}]}` | user | 个人行为统计 |
| 行为分析 | 获取行为详情 | `/api/v1/reputation/actions/{actionId}` | GET | 路径参数: `actionId` | `{"code": 200, "data": {"id": "uuid", "userId": "uuid", "actionType": "daily_study", "scoreChange": "+5", "reason": "完成每日学习目标", "metadata": {...}, "createdAt": "datetime"}}` | user | 单个行为记录详情 |
| 行为分析 | 获取系统行为统计 | `/api/v1/reputation/actions/system-stats` | GET | `period=month&actionType=string&groupBy=day` | `{"code": 200, "data": {"totalActions": 10000, "positiveActions": 8500, "negativeActions": 1500, "averageScore": 2.5, "trends": [...]}}` | admin | 系统整体行为统计 |
| 奖励机制 | 获取成就列表 | `/api/v1/reputation/achievements` | GET | `userId=uuid&category=study|social|milestone` | `{"code": 200, "data": [{"id": "uuid", "name": "学习达人", "description": "连续学习30天", "icon": "icon_url", "unlockedAt": "datetime", "scoreReward": 50}]}` | user | 用户成就列表 |
| 奖励机制 | 解锁成就 | `/api/v1/reputation/achievements/unlock` | POST | `{"achievementId": "uuid", "userId": "uuid", "metadata": {"studyDays": 30}}` | `{"code": 200, "message": "成就解锁成功", "data": {"scoreReward": 50, "newScore": 400}}` | system | 系统自动调用 |
| 奖励机制 | 获取可用特权 | `/api/v1/reputation/privileges` | GET | `userId=uuid` | `{"code": 200, "data": [{"name": "创建小桌", "description": "可以创建和管理小桌", "requiredLevel": "regular", "available": true}, {"name": "高级搜索", "description": "使用高级搜索功能", "requiredLevel": "expert", "available": false}]}` | user | 当前可用特权 |
| 质量保障 | 举报用户行为 | `/api/v1/reputation/reports` | POST | `{"targetUserId": "uuid", "reportType": "spam|harassment|cheating", "reason": "举报理由", "evidence": ["screenshot_url"], "relatedContent": "uuid"}` | `{"code": 200, "message": "举报已提交", "data": {"reportId": "uuid"}}` | user | 用户举报机制 |
| 质量保障 | 获取举报列表 | `/api/v1/reputation/reports` | GET | `page=1&size=20&status=pending&reportType=string&sortBy=createdAt` | `{"code": 200, "data": {"records": [...], "total": 20}}` | admin | 管理员查看举报 |
| 质量保障 | 处理举报 | `/api/v1/reputation/reports/{reportId}/handle` | POST | `{"action": "approve|reject", "punishment": "warning|score_deduction|temporary_ban", "reason": "处理说明", "scoreDeduction": 20}` | `{"code": 200, "message": "举报处理完成"}` | admin | 举报处理流程 |
| 质量保障 | 获取用户信用记录 | `/api/v1/reputation/credit-record/{userId}` | GET | 路径参数: `userId` | `{"code": 200, "data": {"userId": "uuid", "creditScore": 85, "violations": [...], "warnings": 2, "bans": 0, "lastViolation": "datetime"}}` | admin | 用户信用档案 |

## 数据模型

### 信誉记录模型 (ReputationLog)
```json
{
  "id": "uuid",
  "userId": "uuid",
  "actionType": "daily_study|help_member|create_desk|spam_content|manual_adjust",
  "scoreChange": 5,
  "reason": "完成每日学习目标",
  "metadata": {
    "studyDuration": 120,
    "questionCount": 50,
    "accuracyRate": 85.0,
    "moduleType": "math"
  },
  "createdAt": "datetime"
}
```

### 信誉等级模型 (ReputationLevel)
```json
{
  "level": "expert",
  "name": "专家",
  "minScore": 300,
  "maxScore": 599,
  "color": "#gold",
  "privileges": [
    "创建小桌",
    "高级搜索",
    "优先审核"
  ],
  "description": "在某个领域有深入理解的用户"
}
```

### 成就模型 (Achievement)
```json
{
  "id": "uuid",
  "name": "学习达人",
  "description": "连续学习30天",
  "category": "study",
  "icon": "achievement_icon_url",
  "scoreReward": 50,
  "rarity": "rare",
  "unlockCondition": {
    "type": "consecutive_study_days",
    "target": 30
  }
}
```
