import React, { useEffect, useState } from 'react';
import { Toast, Modal } from '@douyinfe/semi-ui';
import { IconStar, IconPrizeStroked, IconArrowUp, IconArrowDown } from '@douyinfe/semi-icons';

interface ReputationChangeNotification {
  id: string;
  changeType: 'earn' | 'deduct';
  points: number;
  reason: string;
  category: string;
  newScore: number;
  newLevel?: string;
  levelChanged?: boolean;
}

interface ReputationNotificationProps {
  notification: ReputationChangeNotification | null;
  onClose: () => void;
}

// 等级升级庆祝组件
const LevelUpCelebration: React.FC<{
  newLevel: string;
  newScore: number;
  visible: boolean;
  onClose: () => void;
}> = ({ newLevel, newScore, visible, onClose }) => {
  const levelNames: { [key: string]: string } = {
    'bronze': '青铜',
    'silver': '白银', 
    'gold': '黄金',
    'platinum': '铂金',
    'diamond': '钻石',
    'master': '大师',
    'grandmaster': '宗师'
  };

  const levelEmojis: { [key: string]: string } = {
    'bronze': '🥉',
    'silver': '🥈',
    'gold': '🥇',
    'platinum': '💎',
    'diamond': '💠',
    'master': '👑',
    'grandmaster': '🏆'
  };

  return (
    <Modal
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      maskClosable={true}
      style={{
        background: 'var(--paper-bg)',
        border: '3px solid var(--accent-gold)',
        borderRadius: '20px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.3)'
      }}
    >
      <div style={{
        textAlign: 'center',
        padding: '30px 20px',
        background: 'linear-gradient(135deg, #fff59d 0%, #fff176 100%)',
        borderRadius: '15px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 装饰性背景 */}
        <div style={{
          position: 'absolute',
          top: '-50px',
          left: '-50px',
          width: '100px',
          height: '100px',
          background: 'rgba(255, 215, 0, 0.2)',
          borderRadius: '50%',
          animation: 'float 3s ease-in-out infinite'
        }} />
        <div style={{
          position: 'absolute',
          bottom: '-30px',
          right: '-30px',
          width: '60px',
          height: '60px',
          background: 'rgba(255, 215, 0, 0.3)',
          borderRadius: '50%',
          animation: 'float 3s ease-in-out infinite 1s'
        }} />

        {/* 庆祝内容 */}
        <div style={{ position: 'relative', zIndex: 1 }}>
          <div style={{ fontSize: '60px', marginBottom: '15px' }}>
            {levelEmojis[newLevel] || '⭐'}
          </div>
          
          <h2 style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '28px',
            color: 'var(--ink-dark)',
            margin: '0 0 10px 0',
            textShadow: '2px 2px 0px rgba(255,255,255,0.8)'
          }}>
            🎉 等级提升！🎉
          </h2>
          
          <p style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '20px',
            color: 'var(--ink-medium)',
            margin: '0 0 15px 0'
          }}>
            恭喜升级到 <strong style={{ color: 'var(--accent-gold)' }}>
              {levelNames[newLevel] || newLevel}
            </strong> 等级！
          </p>
          
          <p style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '16px',
            color: 'var(--ink-light)',
            margin: '0'
          }}>
            当前信誉分数：{newScore} 分
          </p>
          
          <div style={{
            marginTop: '20px',
            fontSize: '24px',
            animation: 'sparkle 2s ease-in-out infinite'
          }}>
            ✨ 继续加油！✨
          </div>
        </div>
      </div>
    </Modal>
  );
};

// 主通知组件
export const ReputationNotification: React.FC<ReputationNotificationProps> = ({
  notification,
  onClose
}) => {
  const [showLevelUp, setShowLevelUp] = useState(false);

  useEffect(() => {
    if (!notification) return;

    // 如果等级发生变化，显示升级庆祝
    if (notification.levelChanged && notification.newLevel) {
      setShowLevelUp(true);
      return;
    }

    // 显示普通的分数变更通知
    const isEarn = notification.changeType === 'earn';
    const icon = isEarn ? <IconArrowUp /> : <IconArrowDown />;
    const color = isEarn ? 'var(--accent-green)' : 'var(--accent-orange)';
    const prefix = isEarn ? '+' : '-';

    Toast.info({
      content: (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          fontFamily: 'var(--font-handwritten)'
        }}>
          <div style={{ color, fontSize: '18px' }}>
            {icon}
          </div>
          <div>
            <div style={{ fontWeight: '600', color: 'var(--ink-dark)' }}>
              {prefix}{notification.points}分 • {notification.reason}
            </div>
            <div style={{ fontSize: '12px', color: 'var(--ink-light)' }}>
              当前信誉：{notification.newScore}分
            </div>
          </div>
        </div>
      ),
      duration: 4,
      showClose: true,
      onClose: onClose
    });
  }, [notification, onClose]);

  const handleLevelUpClose = () => {
    setShowLevelUp(false);
    onClose();
  };

  return (
    <>
      {showLevelUp && notification?.newLevel && (
        <LevelUpCelebration
          newLevel={notification.newLevel}
          newScore={notification.newScore}
          visible={showLevelUp}
          onClose={handleLevelUpClose}
        />
      )}
    </>
  );
};

// Hook for managing reputation notifications
export const useReputationNotification = () => {
  const [notification, setNotification] = useState<ReputationChangeNotification | null>(null);

  const showNotification = (notificationData: ReputationChangeNotification) => {
    setNotification(notificationData);
  };

  const clearNotification = () => {
    setNotification(null);
  };

  return {
    notification,
    showNotification,
    clearNotification
  };
};

// CSS animations (should be added to global styles)
const styles = `
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
  50% { transform: scale(1.1) rotate(10deg); opacity: 0.8; }
}
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

export default ReputationNotification;
