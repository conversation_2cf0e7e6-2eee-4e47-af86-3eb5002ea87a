package com.wqh.publicexaminationassistant.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.response.GlobalRankingResponse;
import com.wqh.publicexaminationassistant.dto.response.RankingStatistics;
import com.wqh.publicexaminationassistant.dto.response.UserRankResponse;
import com.wqh.publicexaminationassistant.enums.RankingPeriod;
import com.wqh.publicexaminationassistant.enums.RankingType;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.RankingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 排行榜控制器
 * 提供排行榜相关的API接口
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@RestController
@RequestMapping("/v1/rankings")
@RequiredArgsConstructor
@Tag(name = "排行榜管理", description = "排行榜相关接口")
public class RankingController {

    private final RankingService rankingService;

    /**
     * 获取全站排行榜
     */
    @GetMapping("/global")
    @Operation(summary = "获取全站排行榜", description = "获取指定类型和周期的全站用户排行榜")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<?> getGlobalRanking(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "50")
            @RequestParam(defaultValue = "50") int size,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户查看全站排行榜: userId={}, type={}, period={}, page={}, size={}",
                userId, type, period, page, size);

        Page<GlobalRankingResponse> result = rankingService.getGlobalRanking(
                type, period, page, size, userId);

        // 转换为前端期望的格式
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("records", result.getRecords());
        responseData.put("total", result.getTotal());
        responseData.put("current", result.getCurrent());
        responseData.put("size", result.getSize());
        responseData.put("pages", result.getPages());

        return ApiResponse.success(responseData, "获取排行榜成功");
    }

    /**
     * 获取用户个人排名
     */
    @GetMapping("/my-rank")
    @Operation(summary = "获取个人排名", description = "获取当前用户在指定排行榜中的排名信息")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<UserRankResponse> getMyRank(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户查看个人排名: userId={}, type={}, period={}",
                userId, type, period);

        UserRankResponse result = rankingService.getUserRank(userId, type, period);

        return ApiResponse.success(result, "获取个人排名成功");
    }

    /**
     * 获取排行榜统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取排行榜统计", description = "获取指定排行榜的整体统计信息")
    public ApiResponse<RankingStatistics> getRankingStatistics(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period) {

        log.info("获取排行榜统计: type={}, period={}", type, period);

        RankingStatistics result = rankingService.getRankingStatistics(type, period);

        return ApiResponse.success(result, "获取统计信息成功");
    }

    /**
     * 获取排行榜配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "获取排行榜配置", description = "获取系统支持的排行榜类型和时间周期配置")
    public ApiResponse<Map<String, Object>> getRankingConfig() {
        log.info("获取排行榜配置信息");

        Map<String, Object> config = new HashMap<>();

        // 排行榜类型配置
        Map<String, Object> types = new HashMap<>();
        for (RankingType rankingType : RankingType.values()) {
            Map<String, String> typeInfo = new HashMap<>();
            typeInfo.put("name", rankingType.getName());
            typeInfo.put("description", rankingType.getDescription());
            types.put(rankingType.getCode(), typeInfo);
        }
        config.put("types", types);

        // 时间周期配置
        Map<String, Object> periods = new HashMap<>();
        for (RankingPeriod rankingPeriod : RankingPeriod.values()) {
            Map<String, String> periodInfo = new HashMap<>();
            periodInfo.put("name", rankingPeriod.getName());
            periodInfo.put("description", rankingPeriod.getDescription());
            periods.put(rankingPeriod.getCode(), periodInfo);
        }
        config.put("periods", periods);

        // 分页配置
        Map<String, Object> pagination = new HashMap<>();
        pagination.put("defaultPageSize", 50);
        pagination.put("maxPageSize", 100);
        pagination.put("minPageSize", 10);
        config.put("pagination", pagination);

        return ApiResponse.success(config, "获取配置信息成功");
    }

    /**
     * 手动触发排行榜计算（管理员接口）
     */
    @PostMapping("/calculate")
    @Operation(summary = "计算排行榜", description = "手动触发指定排行榜的计算更新（管理员功能）")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> calculateRanking(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("管理员触发排行榜计算: adminId={}, type={}, period={}",
                userId, type, period);

        // 这里应该添加管理员权限检查
        // @PreAuthorize("hasRole('ADMIN')")

        int updatedRecords = rankingService.calculateAndUpdateRanking(type, period);

        Map<String, Object> result = new HashMap<>();
        result.put("type", type);
        result.put("period", period);
        result.put("updatedRecords", updatedRecords);
        result.put("calculatedAt", java.time.LocalDateTime.now());

        return ApiResponse.success(result, "排行榜计算完成");
    }

    /**
     * 获取排行榜更新状态
     */
    @GetMapping("/update-status")
    @Operation(summary = "获取更新状态", description = "获取排行榜的最后更新时间和状态")
    public ApiResponse<Map<String, Object>> getUpdateStatus(
            @Parameter(description = "排行榜类型", example = "study_questions")
            @RequestParam(required = false) String type,
            @Parameter(description = "时间周期", example = "weekly")
            @RequestParam(required = false) String period) {

        log.info("获取排行榜更新状态: type={}, period={}", type, period);

        Map<String, Object> status = new HashMap<>();
        
        // 这里应该查询实际的更新状态
        status.put("lastUpdateTime", java.time.LocalDateTime.now().minusHours(1));
        status.put("nextUpdateTime", java.time.LocalDateTime.now().plusHours(23));
        status.put("isUpdating", false);
        status.put("updateFrequency", "每日凌晨2点自动更新");

        if (type != null && period != null) {
            status.put("type", type);
            status.put("period", period);
            status.put("recordCount", 1000); // 示例数据
        }

        return ApiResponse.success(status, "获取更新状态成功");
    }

    /**
     * 手动计算并生成排行榜数据
     */
    @PostMapping("/generate")
    @Operation(summary = "生成排行榜数据", description = "基于真实学习记录计算并生成排行榜数据")
    @SecurityRequirement(name = "Bearer Authentication")
    public ApiResponse<Map<String, Object>> generateRankingData(
            @Parameter(description = "排行榜类型", example = "study_questions", required = true)
            @RequestParam String type,
            @Parameter(description = "时间周期", example = "weekly", required = true)
            @RequestParam String period,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("用户触发排行榜生成: userId={}, type={}, period={}", userId, type, period);

        try {
            int updatedRecords = rankingService.calculateAndUpdateRanking(type, period);

            Map<String, Object> result = new HashMap<>();
            result.put("type", type);
            result.put("period", period);
            result.put("updatedRecords", updatedRecords);
            result.put("generatedAt", java.time.LocalDateTime.now());

            return ApiResponse.success(result, "排行榜数据生成完成");
        } catch (Exception e) {
            log.error("生成排行榜数据失败: type={}, period={}", type, period, e);
            return ApiResponse.error("生成排行榜数据失败: " + e.getMessage());
        }
    }
}
