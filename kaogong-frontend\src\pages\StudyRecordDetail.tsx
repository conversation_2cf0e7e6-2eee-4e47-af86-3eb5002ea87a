import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Progress,
  Spin,
  Empty,
  Toast,
  Popconfirm
} from '@douyinfe/semi-ui';
import {
  IconArrowLeft,
  IconEdit,
  IconDelete,
  IconCalendar,
  IconBookStroked,
  IconLineChartStroked,
  IconClock
} from '@douyinfe/semi-icons';
import { useNavigate, useParams } from 'react-router-dom';
import { studyService, StudyRecordResponse } from '../services/studyService';
import '../styles/theme.css';

const { Title, Text } = Typography;

// 学习模块映射 - 与后端API保持一致
const MODULE_TYPE_MAP: Record<string, string> = {
  'math': '数学运算',
  'logic': '逻辑推理', 
  'language': '言语理解',
  'knowledge': '常识判断',
  'essay': '申论写作'
};

// 浮动装饰组件
const FloatingDecorations: React.FC = () => {
  const decorations = ['📚', '✏️', '📝', '🎯', '💡', '⭐'];
  
  return (
    <>
      {decorations.map((emoji, index) => (
        <div
          key={index}
          className="floating-emoji"
          style={{
            position: 'absolute',
            top: `${15 + index * 12}%`,
            left: `${5 + (index % 2) * 90}%`,
            animationDelay: `${index * 0.8}s`,
            zIndex: 1
          }}
        >
          {emoji}
        </div>
      ))}
    </>
  );
};

// 格式化学习时长
const formatStudyDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours > 0) {
    return `${hours}小时${mins > 0 ? `${mins}分钟` : ''}`;
  }
  return `${mins}分钟`;
};

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
};

// 安全解析薄弱知识点数据
const parseWeakPoints = (weakPoints: string | string[] | null | undefined): string[] => {
  // 如果是null或undefined，返回空数组
  if (!weakPoints) {
    return [];
  }

  // 如果已经是数组，直接返回
  if (Array.isArray(weakPoints)) {
    return weakPoints;
  }

  // 如果是字符串，尝试解析JSON
  if (typeof weakPoints === 'string') {
    try {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(weakPoints);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      // 如果解析结果不是数组，按逗号分割
      return [parsed.toString()];
    } catch (error) {
      // JSON解析失败，按逗号分割字符串
      return weakPoints.split(',').map(point => point.trim()).filter(point => point.length > 0);
    }
  }

  // 其他情况返回空数组
  return [];
};

interface StudyRecordDetailProps {}

const StudyRecordDetail: React.FC<StudyRecordDetailProps> = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  // 状态管理
  const [record, setRecord] = useState<StudyRecordResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [notFound, setNotFound] = useState(false);

  // 获取记录详情
  const fetchRecordDetail = async (recordId: string) => {
    setLoading(true);
    setNotFound(false);
    try {
      const response = await studyService.getRecordDetail(recordId);
      setRecord(response);
    } catch (error: any) {
      console.error('获取记录详情失败:', error);
      if (error.response?.status === 404) {
        setNotFound(true);
      } else if (error.response?.status === 403) {
        Toast.error('无权限访问此记录');
        navigate('/study-records');
      } else {
        Toast.error(error.message || '获取记录详情失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (id) {
      fetchRecordDetail(id);
    }
  }, [id]);

  // 处理编辑
  const handleEdit = () => {
    navigate(`/study-records/${id}/edit`);
  };

  // 处理删除
  const handleDelete = async () => {
    if (!id) return;
    
    try {
      await studyService.deleteRecord(id);
      Toast.success('删除成功');
      navigate('/study-records');
    } catch (error: any) {
      console.error('删除记录失败:', error);
      Toast.error(error.message || '删除记录失败');
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/study-records');
  };

  // 重试加载
  const handleRetry = () => {
    if (id) {
      fetchRecordDetail(id);
    }
  };

  // 404页面
  if (notFound) {
    return (
      <div style={{ 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
        padding: '24px',
        position: 'relative'
      }}>
        <FloatingDecorations />
        
        <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 10 }}>
          <div style={{ textAlign: 'center', marginTop: '100px' }}>
            <Empty
              image={<IconBookStroked size="extra-large" />}
              title="记录不存在"
              description="您要查看的学习记录不存在或已被删除"
              style={{ padding: '40px 0' }}
            >
              <Button
                theme="solid"
                type="primary"
                icon={<IconArrowLeft />}
                onClick={handleBack}
                className="sketch-button primary"
              >
                返回记录列表
              </Button>
            </Empty>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
      padding: '24px',
      position: 'relative'
    }}>
      <FloatingDecorations />
      
      <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 10 }}>
        {/* 页面标题和操作栏 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '32px'
        }}>
          <div>
            <Button
              theme="borderless"
              icon={<IconArrowLeft />}
              onClick={handleBack}
              style={{ 
                marginBottom: '16px',
                transform: 'rotate(-0.5deg)'
              }}
            >
              返回列表
            </Button>
            <Title 
              heading={2} 
              className="handwritten-title large"
              style={{ 
                color: 'var(--ink-dark)',
                marginBottom: '8px'
              }}
            >
              📋 学习记录详情
            </Title>
            <Text 
              type="secondary" 
              style={{ 
                fontSize: 'var(--font-size-lg)',
                fontFamily: 'var(--font-handwritten)'
              }}
            >
              查看详细的学习数据和分析
            </Text>
          </div>
          
          {record && (
            <Space>
              <Button
                theme="solid"
                type="secondary"
                icon={<IconEdit />}
                onClick={handleEdit}
                className="sketch-button"
                style={{ transform: 'rotate(-0.5deg)' }}
              >
                编辑记录
              </Button>
              <Popconfirm
                title="确认删除"
                content="删除后无法恢复，确定要删除这条记录吗？"
                position="top"
                onConfirm={handleDelete}
                okText="确认删除"
                cancelText="取消"
                okType="danger"
              >
                <Button
                  theme="solid"
                  type="danger"
                  icon={<IconDelete />}
                  className="sketch-button"
                  style={{ transform: 'rotate(0.5deg)' }}
                >
                  删除记录
                </Button>
              </Popconfirm>
            </Space>
          )}
        </div>

        {/* 加载状态 */}
        <Spin spinning={loading}>
          {record && (
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '24px'
            }}>
              {/* 基本信息卡片 */}
              <Card
                className="sketch-card"
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <IconBookStroked />
                    <span style={{ fontFamily: 'var(--font-handwritten)' }}>基本信息</span>
                  </div>
                }
                style={{ gridColumn: 'span 2' }}
              >
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '20px'
                }}>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                      <IconCalendar style={{ marginRight: '4px' }} />
                      学习日期
                    </Text>
                    <Text style={{
                      fontSize: 'var(--font-size-lg)',
                      fontFamily: 'var(--font-handwritten)'
                    }}>
                      {formatDate(record.studyDate)}
                    </Text>
                  </div>

                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                      学习模块
                    </Text>
                    <Tag
                      color="blue"
                      size="large"
                      style={{
                        fontFamily: 'var(--font-handwritten)',
                        transform: 'rotate(-0.5deg)'
                      }}
                    >
                      {MODULE_TYPE_MAP[record.moduleType] || record.moduleType}
                    </Tag>
                  </div>

                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                      <IconClock style={{ marginRight: '4px' }} />
                      学习时长
                    </Text>
                    <Text style={{
                      fontSize: 'var(--font-size-lg)',
                      fontFamily: 'var(--font-handwritten)',
                      color: 'var(--accent-blue)'
                    }}>
                      {formatStudyDuration(record.studyDuration)}
                    </Text>
                  </div>
                </div>
              </Card>

              {/* 学习统计卡片 */}
              <Card
                className="sketch-card"
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <IconLineChartStroked />
                    <span style={{ fontFamily: 'var(--font-handwritten)' }}>学习统计</span>
                  </div>
                }
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  <div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '8px'
                    }}>
                      <Text strong>正确率</Text>
                      <Text style={{
                        fontSize: 'var(--font-size-xl)',
                        fontFamily: 'var(--font-handwritten)',
                        color: record.accuracyRate >= 80 ? 'var(--accent-green)' :
                               record.accuracyRate >= 60 ? 'var(--accent-orange)' : 'var(--accent-red)'
                      }}>
                        {record.accuracyRate.toFixed(1)}%
                      </Text>
                    </div>
                    <Progress
                      percent={record.accuracyRate}
                      stroke={
                        record.accuracyRate >= 80 ? 'var(--accent-green)' :
                        record.accuracyRate >= 60 ? 'var(--accent-orange)' : 'var(--accent-red)'
                      }
                      showInfo={false}
                      style={{ marginBottom: '16px' }}
                    />
                  </div>

                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gap: '16px',
                    textAlign: 'center'
                  }}>
                    <div>
                      <Text type="secondary" style={{ display: 'block', fontSize: '12px' }}>
                        题目总数
                      </Text>
                      <Text style={{
                        fontSize: 'var(--font-size-xl)',
                        fontFamily: 'var(--font-handwritten)',
                        color: 'var(--ink-dark)'
                      }}>
                        {record.questionCount}
                      </Text>
                    </div>
                    <div>
                      <Text type="secondary" style={{ display: 'block', fontSize: '12px' }}>
                        正确数量
                      </Text>
                      <Text style={{
                        fontSize: 'var(--font-size-xl)',
                        fontFamily: 'var(--font-handwritten)',
                        color: 'var(--accent-green)'
                      }}>
                        {record.correctCount}
                      </Text>
                    </div>
                    <div>
                      <Text type="secondary" style={{ display: 'block', fontSize: '12px' }}>
                        错误数量
                      </Text>
                      <Text style={{
                        fontSize: 'var(--font-size-xl)',
                        fontFamily: 'var(--font-handwritten)',
                        color: 'var(--accent-red)'
                      }}>
                        {record.questionCount - record.correctCount}
                      </Text>
                    </div>
                  </div>
                </div>
              </Card>

              {/* 学习笔记卡片 */}
              <Card
                className="sketch-card"
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    📝
                    <span style={{ fontFamily: 'var(--font-handwritten)' }}>学习笔记</span>
                  </div>
                }
              >
                {record.notes ? (
                  <div style={{
                    background: 'var(--paper-warm)',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '2px dashed var(--border-light)',
                    fontFamily: 'var(--font-system)',
                    lineHeight: '1.6',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {record.notes}
                  </div>
                ) : (
                  <div style={{
                    textAlign: 'center',
                    padding: '32px',
                    color: 'var(--ink-light)'
                  }}>
                    <Text type="tertiary">暂无学习笔记</Text>
                  </div>
                )}
              </Card>

              {/* 薄弱知识点卡片 */}
              <Card
                className="sketch-card"
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    🎯
                    <span style={{ fontFamily: 'var(--font-handwritten)' }}>薄弱知识点</span>
                  </div>
                }
              >
                {(() => {
                  const weakPointsArray = parseWeakPoints(record.weakPoints);
                  return weakPointsArray.length > 0 ? (
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {weakPointsArray.map((point, index) => (
                        <Tag
                          key={index}
                          color="orange"
                          style={{
                            margin: '2px',
                            transform: `rotate(${index % 2 === 0 ? '-0.5' : '0.5'}deg)`,
                            fontFamily: 'var(--font-handwritten)'
                          }}
                        >
                          {point}
                        </Tag>
                      ))}
                    </div>
                  ) : (
                    <div style={{
                      textAlign: 'center',
                      padding: '32px',
                      color: 'var(--ink-light)'
                    }}>
                      <Text type="tertiary">暂无薄弱知识点记录</Text>
                    </div>
                  );
                })()}
              </Card>
            </div>
          )}
        </Spin>

        {/* 错误状态重试 */}
        {!loading && !record && !notFound && (
          <div style={{ textAlign: 'center', marginTop: '100px' }}>
            <Empty
              title="加载失败"
              description="获取记录详情时出现错误"
            >
              <Button
                theme="solid"
                type="primary"
                onClick={handleRetry}
                className="sketch-button primary"
              >
                重试
              </Button>
            </Empty>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudyRecordDetail;
