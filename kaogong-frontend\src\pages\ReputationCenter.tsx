import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Card, Spin, Toast, Progress, Button } from '@douyinfe/semi-ui';
import {
  IconStar,
  IconPrizeStroked,
  IconCalendar,
  IconBookmark,
  IconHistory,
  IconShield,
  IconArrowRight
} from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';
import { useReputationStore } from '../stores/useReputationStore';
import {
  getLevelInfo,
  getNextLevelInfo,
  calculateLevelProgress,
  REPUTATION_LEVELS
} from '../types/reputation';
import '../styles/reputation.css';

const ReputationCenter: React.FC = () => {
  // 恢复与后端的数据交互
  const {
    userStats,
    isLoading,
    error,
    fetchUserStats,
    clearError
  } = useReputationStore();

  useEffect(() => {
    fetchUserStats();
  }, [fetchUserStats]);

  useEffect(() => {
    if (error) {
      Toast.error(error);
      clearError();
    }
  }, [error, clearError]);

  if (isLoading) {
    return (
      <div className="reputation-page">
        <Navigation />
        <div className="reputation-container" style={{ paddingTop: '80px' }}>
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <Spin size="large" />
            <p style={{ marginTop: '20px', color: 'var(--ink-medium)' }}>
              正在加载信誉信息...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!userStats) {
    return (
      <div className="reputation-page">
        <Navigation />
        <div className="reputation-container" style={{ paddingTop: '80px' }}>
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <div style={{ marginBottom: '20px' }}>
              <IconStar style={{ fontSize: '48px', color: '#ffc107' }} />
            </div>
            <h3 style={{ color: 'var(--ink-dark)', marginBottom: '16px' }}>
              信誉信息初始化中
            </h3>
            <p style={{ color: 'var(--ink-medium)', fontSize: '16px', marginBottom: '24px' }}>
              系统正在为您创建信誉档案，请稍候...
            </p>
            <Button
              type="primary"
              onClick={() => window.location.reload()}
              style={{ marginTop: '16px' }}
            >
              刷新页面
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // 使用真实的等级信息计算
  const currentLevelInfo = userStats ? getLevelInfo(userStats.currentLevel) : null;
  const nextLevelInfo = userStats ? getNextLevelInfo(userStats.currentLevel) : null;
  const progressInfo = userStats ? calculateLevelProgress(userStats.currentScore, userStats.currentLevel) : null;

  const formatTime = (hours: number): string => {
    if (hours <= 0) return '已结束';
    if (hours < 24) return `${hours}小时`;
    const days = Math.floor(hours / 24);
    return `${days}天`;
  };

  return (
    <div className="reputation-page">
      <Navigation />
      <div className="reputation-container" style={{ paddingTop: '80px' }}>
        {/* 页面标题 */}
        <div className="reputation-header">
          <h1 className="reputation-title">
            ⭐ 我的信誉中心 ⭐
          </h1>
          <p className="reputation-subtitle">
            信誉体系是您在平台上的信用记录，通过学习行为和社区贡献获得信誉分数
          </p>
        </div>

        {/* 信誉概览卡片 */}
        <Card className="reputation-overview-card">
          {/* 装饰性emoji */}
          <div className="floating-emoji top-left">🌟</div>
          <div className="floating-emoji top-right">🏆</div>
          <div className="floating-emoji bottom-left">📚</div>
          <div className="floating-emoji bottom-right">✨</div>

          {/* 当前分数和等级显示 */}
          <div className="score-display">
            <h2 className="current-score">{userStats.currentScore}</h2>
            <p className="score-label">当前信誉分数</p>
            <div className="score-change">
              {userStats.totalEarned > 0 && (
                <span className="earned">+{userStats.totalEarned} 累计获得</span>
              )}
              {userStats.totalDeducted > 0 && (
                <span className="deducted">-{userStats.totalDeducted} 累计扣除</span>
              )}
            </div>
          </div>

          {/* 等级徽章 */}
          <div style={{ textAlign: 'center', marginBottom: '25px' }}>
            <div
              className="level-badge"
              style={{ color: currentLevelInfo?.color || '#4299e1' }}
            >
              <IconPrizeStroked className="level-icon" />
              <span>{currentLevelInfo?.name || '未知等级'}</span>
            </div>
          </div>

          {/* 保护期提示 */}
          {userStats.isInProtection && (
            <div className="protection-notice">
              <div className="protection-header">
                <IconShield style={{ color: '#52c41a', fontSize: '18px' }} />
                <span className="protection-title">新手保护期</span>
              </div>
              <p className="protection-text">
                剩余时间: {formatTime(userStats.protectionHoursRemaining)}
                <br />
                <small>保护期内不会因为未学习而扣分</small>
              </p>
            </div>
          )}

          {/* 用户活跃度信息 */}
          <div className="activity-info">
            <div className="activity-item">
              <IconCalendar className="activity-icon" />
              <div className="activity-content">
                <span className="activity-label">连续登录</span>
                <span className="activity-value">{userStats.consecutiveLoginDays} 天</span>
              </div>
            </div>
            <div className="activity-item">
              <IconBookmark className="activity-icon" />
              <div className="activity-content">
                <span className="activity-label">连续学习</span>
                <span className="activity-value">{userStats.consecutiveStudyDays} 天</span>
              </div>
            </div>
            {userStats.consecutiveNoStudyDays > 0 && (
              <div className="activity-item warning">
                <IconCalendar className="activity-icon" />
                <div className="activity-content">
                  <span className="activity-label">连续未学习</span>
                  <span className="activity-value">{userStats.consecutiveNoStudyDays} 天</span>
                </div>
              </div>
            )}
          </div>

          {/* 等级进度条 */}
          {nextLevelInfo && progressInfo && (
            <div className="level-progress">
              <div className="progress-header">
                <span className="progress-label">
                  距离 {nextLevelInfo.name} 等级
                </span>
                <span className="progress-points">
                  还需 {progressInfo.pointsToNext} 分
                </span>
              </div>
              <div className="sketch-progress">
                <div
                  className="sketch-progress-bar"
                  style={{ width: `${progressInfo.progress}%` }}
                />
              </div>
            </div>
          )}

          {/* 统计信息网格 */}
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">📈</div>
              <div className="stat-value">{userStats.totalEarned}</div>
              <div className="stat-label">累计获得</div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📉</div>
              <div className="stat-value">{userStats.totalDeducted}</div>
              <div className="stat-label">累计扣除</div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🔥</div>
              <div className="stat-value">{userStats.consecutiveLoginDays}</div>
              <div className="stat-label">连续登录</div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">📖</div>
              <div className="stat-value">{userStats.consecutiveStudyDays}</div>
              <div className="stat-label">连续学习</div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="reputation-actions">
            <div className="action-buttons">
              <Link to="/reputation/logs" className="sketch-button primary">
                <IconHistory />
                查看信誉日志
                <IconArrowRight />
              </Link>
              <Link to="/study-records" className="sketch-button">
                <IconBookmark />
                开始学习
                <IconArrowRight />
              </Link>
            </div>

            {/* 快速统计 */}
            <div className="quick-stats">
              <div className="quick-stat-item">
                <span className="stat-label">本周扣分</span>
                <span className="stat-value">{userStats.weeklyDeductPoints || 0}</span>
              </div>
              <div className="quick-stat-item">
                <span className="stat-label">本月扣分</span>
                <span className="stat-value">{userStats.monthlyDeductPoints || 0}</span>
              </div>
              <div className="quick-stat-item">
                <span className="stat-label">最后更新</span>
                <span className="stat-value">
                  {userStats.lastScoreUpdate ?
                    new Date(userStats.lastScoreUpdate).toLocaleDateString() :
                    '未知'
                  }
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* 等级说明卡片 */}
        <Card
          title={
            <span style={{ fontFamily: 'var(--font-handwritten)', fontSize: '20px' }}>
              🏅 等级说明
            </span>
          }
          style={{
            background: 'var(--paper-warm)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            transform: 'rotate(0.3deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)'
          }}
        >
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '15px'
          }}>
            {/* 简化的等级显示 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              padding: '10px',
              background: 'var(--accent-blue-light)',
              border: '2px solid var(--accent-blue)',
              borderRadius: '10px'
            }}>
              <IconPrizeStroked style={{ color: '#CD7F32', fontSize: '18px' }} />
              <div>
                <div style={{
                  fontFamily: 'var(--font-handwritten)',
                  fontWeight: '600',
                  color: 'var(--ink-dark)'
                }}>
                  青铜
                </div>
                <div style={{
                  fontSize: '12px',
                  color: 'var(--ink-light)'
                }}>
                  100-299分
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              padding: '10px',
              background: 'var(--paper-cream)',
              border: '1px solid var(--border-light)',
              borderRadius: '10px'
            }}>
              <IconPrizeStroked style={{ color: '#C0C0C0', fontSize: '18px' }} />
              <div>
                <div style={{
                  fontFamily: 'var(--font-handwritten)',
                  fontWeight: '600',
                  color: 'var(--ink-dark)'
                }}>
                  白银
                </div>
                <div style={{
                  fontSize: '12px',
                  color: 'var(--ink-light)'
                }}>
                  300-599分
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ReputationCenter;
