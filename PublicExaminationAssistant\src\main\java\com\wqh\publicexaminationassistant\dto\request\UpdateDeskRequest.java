package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

/**
 * 更新小桌请求DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class UpdateDeskRequest {

    /**
     * 小桌名称
     */
    @Size(min = 2, max = 50, message = "小桌名称长度必须在2-50个字符之间")
    private String name;

    /**
     * 小桌描述
     */
    @Size(max = 500, message = "小桌描述不能超过500个字符")
    private String description;

    /**
     * 最大成员数（2-8人）
     */
    @Min(value = 2, message = "最大成员数不能少于2人")
    @Max(value = 8, message = "最大成员数不能超过8人")
    private Integer maxMembers;

    /**
     * 自动审核规则（JSON格式）
     */
    private String autoApproveRules;
}
