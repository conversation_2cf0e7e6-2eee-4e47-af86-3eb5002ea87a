import React from 'react';
import { Card, Space, Typography, Button, Alert } from '@douyinfe/semi-ui';

const { Title, Text } = Typography;

const IconDebug: React.FC = () => {
  // 动态导入图标以避免构建时错误
  const [icons, setIcons] = React.useState<any>({});
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    const loadIcons = async () => {
      try {
        // 动态导入所有需要的图标
        const iconModules = await Promise.all([
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconEyeOpened', component: module.IconEyeOpened })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconDelete', component: module.IconDelete })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconUpload', component: module.IconUpload })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconDownload', component: module.IconDownload })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconArrowLeft', component: module.IconArrowLeft })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconArrowRight', component: module.IconArrowRight })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconPlus', component: module.IconPlus })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconRefresh', component: module.IconRefresh })),
          import('@douyinfe/semi-icons').then(module => ({ name: 'IconFilter', component: module.IconFilter }))
        ]);

        const iconMap: any = {};
        iconModules.forEach(({ name, component }) => {
          iconMap[name] = component;
        });

        setIcons(iconMap);
        setLoading(false);
      } catch (err: any) {
        setError(err.message);
        setLoading(false);
      }
    };

    loadIcons();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Text>正在加载图标...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          type="error"
          message="图标加载失败"
          description={error}
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>图标动态加载测试</Title>
        <Text type="secondary">
          使用动态导入方式测试Semi Design图标
        </Text>
        
        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>图标状态检查</Title>
          <div style={{ 
            padding: '16px', 
            backgroundColor: 'var(--semi-color-fill-0)',
            borderRadius: '6px'
          }}>
            {Object.entries(icons).map(([name, IconComponent]) => (
              <div key={name} style={{ marginBottom: '8px' }}>
                <Text>
                  {name}: {IconComponent ? '✅ 加载成功' : '❌ 加载失败'}
                </Text>
              </div>
            ))}
          </div>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>图标渲染测试</Title>
          <Space wrap>
            {Object.entries(icons).map(([name, IconComponent]) => {
              if (!IconComponent) return null;
              
              return (
                <Button 
                  key={name}
                  icon={React.createElement(IconComponent)}
                  size="small"
                >
                  {name.replace('Icon', '')}
                </Button>
              );
            })}
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>单独图标测试</Title>
          <Space size="large">
            {Object.entries(icons).slice(0, 4).map(([name, IconComponent]) => {
              if (!IconComponent) return null;
              
              return (
                <div key={name} style={{ textAlign: 'center' }}>
                  {React.createElement(IconComponent, { size: 'large' })}
                  <div><Text size="small">{name}</Text></div>
                </div>
              );
            })}
          </Space>
        </div>

        <div style={{ 
          marginTop: '24px', 
          padding: '16px', 
          backgroundColor: 'var(--semi-color-success-light-default)',
          borderRadius: '6px'
        }}>
          <Text strong style={{ color: 'var(--semi-color-success)' }}>
            💡 解决方案建议：
          </Text>
          <div style={{ marginTop: '8px' }}>
            <Text>
              1. 清理Vite缓存：删除 node_modules/.vite 目录<br/>
              2. 重新安装依赖：npm install<br/>
              3. 重新启动开发服务器：npm run dev<br/>
              4. 如果问题持续，尝试使用动态导入方式
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IconDebug;
