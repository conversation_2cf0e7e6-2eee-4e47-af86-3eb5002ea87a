import React from 'react';
import { Table, Avatar, Tag, Typography, Space, Tooltip, Empty } from '@douyinfe/semi-ui';
import { IconArrowUp, IconArrowDown, IconMinus, IconUser } from '@douyinfe/semi-icons';
import { GlobalRankingResponse, RankingType, rankingUtils } from '../../services/rankingService';
import './RankingTable.css';

const { Text } = Typography;

interface RankingTableProps {
  data: GlobalRankingResponse[];
  loading?: boolean;
  type: RankingType;
  onPageChange?: (page: number) => void;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
  };
}

const RankingTable: React.FC<RankingTableProps> = ({
  data,
  loading = false,
  type,
  onPageChange,
  pagination
}) => {
  const getRankMedal = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return rank <= 10 ? '🏆' : '';
    }
  };

  const getRankChangeIcon = (change?: number) => {
    if (!change || change === 0) {
      return <IconMinus style={{ color: '#666', fontSize: '12px' }} />;
    }
    return change > 0 ?
      <IconArrowUp style={{ color: '#52c41a', fontSize: '12px' }} /> :
      <IconArrowDown style={{ color: '#ff4d4f', fontSize: '12px' }} />;
  };

  const getRankChangeText = (change?: number) => {
    if (!change || change === 0) return '-';
    const absChange = Math.abs(change);
    return change > 0 ? `+${absChange}` : `-${absChange}`;
  };

  const getReputationLevelTag = (level: string) => {
    const color = rankingUtils.getReputationLevelColor(level);
    const levelNames: Record<string, string> = {
      newbie: '新手',
      bronze: '青铜',
      silver: '白银',
      gold: '黄金',
      platinum: '铂金',
      diamond: '钻石',
      master: '大师',
      grandmaster: '宗师'
    };
    
    return (
      <Tag 
        color={color} 
        size="small"
        style={{ 
          fontSize: '10px', 
          padding: '2px 6px',
          borderRadius: '10px'
        }}
      >
        {levelNames[level] || level}
      </Tag>
    );
  };

  const columns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      fixed: false,
      render: (rank: number, record: GlobalRankingResponse) => (
        <div className={`rank-cell ${record.isCurrentUser ? 'current-user' : ''}`}>
          <span className="rank-medal">{getRankMedal(rank)}</span>
          <span className="rank-number">#{rank}</span>
        </div>
      ),
    },
    {
      title: '用户',
      dataIndex: 'username',
      key: 'user',
      width: 200,
      render: (username: string, record: GlobalRankingResponse) => (
        <div className={`user-cell ${record.isCurrentUser ? 'current-user' : ''}`}>
          <Avatar 
            size="small" 
            src={record.avatarUrl}
            style={{ 
              backgroundColor: record.isCurrentUser ? 'var(--accent-blue)' : 'var(--ink-medium)',
              marginRight: '8px'
            }}
          >
            {record.nickname?.[0] || record.username[0]}
          </Avatar>
          <div className="user-info">
            <Text strong={record.isCurrentUser} style={{ 
              color: record.isCurrentUser ? 'var(--accent-blue)' : 'var(--ink-dark)' 
            }}>
              {record.nickname || record.username}
            </Text>
            {record.isCurrentUser && (
              <Text size="small" type="secondary"> (我)</Text>
            )}
            <div>{getReputationLevelTag(record.reputationLevel)}</div>
          </div>
        </div>
      ),
    },
    {
      title: '分数',
      dataIndex: 'score',
      key: 'score',
      width: 120,
      sorter: true,
      render: (score: number, record: GlobalRankingResponse) => (
        <div className={`score-cell ${record.isCurrentUser ? 'current-user' : ''}`}>
          <Text 
            strong 
            style={{ 
              fontSize: '16px',
              color: record.isCurrentUser ? 'var(--accent-blue)' : 'var(--ink-dark)'
            }}
          >
            {rankingUtils.formatScore(score, type)}
          </Text>
        </div>
      ),
    },
    {
      title: '变化',
      dataIndex: 'rankChange',
      key: 'rankChange',
      width: 80,
      render: (change: number) => (
        <div className="change-cell">
          <Space size="small">
            {getRankChangeIcon(change)}
            <Text 
              size="small" 
              style={{ 
                color: rankingUtils.getRankChangeColor(change),
                fontWeight: '500'
              }}
            >
              {getRankChangeText(change)}
            </Text>
          </Space>
        </div>
      ),
    },
    {
      title: '详细数据',
      key: 'details',
      render: (record: GlobalRankingResponse) => (
        <div className="details-cell">
          <Space size="small" wrap>
            {record.extraStats.totalQuestions !== undefined && (
              <Tooltip content="题目数量">
                <Text size="small" type="tertiary">
                  📝 {record.extraStats.totalQuestions.toLocaleString()}
                </Text>
              </Tooltip>
            )}
            {record.extraStats.accuracyRate !== undefined && (
              <Tooltip content="正确率">
                <Text size="small" type="tertiary">
                  🎯 {record.extraStats.accuracyRate.toFixed(1)}%
                </Text>
              </Tooltip>
            )}
            {record.extraStats.studyTime !== undefined && (
              <Tooltip content="学习时长">
                <Text size="small" type="tertiary">
                  ⏱️ {Math.floor(record.extraStats.studyTime / 60)}h
                </Text>
              </Tooltip>
            )}
            {record.extraStats.studyDays !== undefined && (
              <Tooltip content="学习天数">
                <Text size="small" type="tertiary">
                  📅 {record.extraStats.studyDays}天
                </Text>
              </Tooltip>
            )}
          </Space>
        </div>
      ),
    },
  ];

  const paginationConfig = pagination ? {
    current: pagination.current,
    total: pagination.total,
    pageSize: pagination.pageSize,
    showSizeChanger: false,
    showQuickJumper: true,
    showTotal: false, // 隐藏默认的分页信息，我们将单独显示
    onChange: onPageChange,
    style: { marginTop: '16px', textAlign: 'center' }
  } : false;

  if (!loading && (!data || data.length === 0)) {
    return (
      <div className="ranking-table-container">
        <Empty
          image={<IconUser size="large" />}
          title="暂无排行榜数据"
          description="当前时间段内还没有用户数据，请稍后再试"
        />
      </div>
    );
  }

  return (
    <>
      <div className="ranking-table-container">
        <Table
          className="ranking-table"
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={paginationConfig}
          rowKey="userId"
          size="middle"
          scroll={{ x: 800 }}
          showHeader={true}
          bordered={false}
          rowClassName={(record) => record.isCurrentUser ? 'current-user-row' : ''}
        />
      </div>

      {/* 固定底部的分页信息 */}
      {pagination && (
        <div className="fixed-pagination-info">
          显示第 {((pagination.current - 1) * pagination.pageSize) + 1} 条-第 {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
        </div>
      )}
    </>
  );
};

export default RankingTable;
