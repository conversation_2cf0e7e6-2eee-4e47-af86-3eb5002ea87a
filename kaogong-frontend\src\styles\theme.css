/* 考公刷题系统 - 手绘温馨风格主题 */

/* 导入 Google Fonts - Kalam 手写字体 */
@import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');

/* CSS 变量定义 */
:root {
  /* 主色调 - 温馨纸质感 */
  --paper-bg: #fefdf8;
  --paper-warm: #faf9f4;
  --paper-cream: #f7f6f0;
  --paper-texture: #f5f4ef;
  --paper-shadow: #f0efe8;

  /* 墨水色彩 */
  --ink-dark: #2d3748;
  --ink-medium: #4a5568;
  --ink-light: #718096;
  --ink-lighter: #a0aec0;
  --ink-faded: #e2e8f0;

  /* 强调色彩 - 温馨活泼 */
  --accent-blue: #4299e1;
  --accent-blue-light: #90cdf4;
  --accent-green: #48bb78;
  --accent-green-light: #9ae6b4;
  --accent-orange: #ed8936;
  --accent-orange-light: #fbb360;
  --accent-purple: #9f7aea;
  --accent-purple-light: #c3a6f0;
  --accent-pink: #ed64a6;
  --accent-pink-light: #f687b3;
  
  /* 阴影色彩 */
  --shadow-light: rgba(45, 55, 72, 0.1);
  --shadow-medium: rgba(45, 55, 72, 0.15);
  --shadow-dark: rgba(45, 55, 72, 0.2);
  
  /* 边框色彩 */
  --border-light: #e2e8f0;
  --border-medium: #cbd5e0;
  --border-dark: var(--ink-light);
  
  /* 字体系统 */
  --font-handwritten: 'Kalam', cursive;
  --font-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* 字体大小层级 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-system);
  background: var(--paper-bg);
  color: var(--ink-dark);
  line-height: 1.6;
}

/* 手绘风格组件类 */

/* 1. 卡片组件 */
.sketch-card {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 15px;
  transform: rotate(-0.5deg);
  box-shadow: 3px 3px 0px var(--shadow-light);
  transition: all 0.3s ease;
  padding: 24px;
}

.sketch-card:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 5px 5px 0px var(--shadow-medium);
}

/* 2. 按钮组件 */
.sketch-button {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 25px;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  font-family: var(--font-handwritten);
  font-weight: 400;
  cursor: pointer;
  padding: 12px 24px;
  font-size: var(--font-size-base);
  color: var(--ink-dark);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.sketch-button:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 3px 3px 0px var(--shadow-medium);
  color: var(--ink-dark);
  text-decoration: none;
}

.sketch-button.primary {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.sketch-button.primary:hover {
  color: white;
  background: #3182ce;
  border-color: #3182ce;
}

/* 3. 输入框组件 */
.sketch-input {
  background: var(--paper-warm);
  border: 2px dashed var(--border-dark);
  border-radius: 12px;
  transform: rotate(-0.2deg);
  font-family: var(--font-system);
  padding: 12px 16px;
  font-size: var(--font-size-base);
  color: var(--ink-dark);
  width: 100%;
  transition: all 0.3s ease;
}

.sketch-input:focus {
  border-style: solid;
  border-color: var(--accent-blue);
  transform: rotate(0deg);
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  outline: none;
}

/* 4. 便利贴样式 */
.sticky-note {
  background: linear-gradient(135deg, #fff59d 0%, #fff176 100%);
  border-radius: 8px;
  transform: rotate(2deg);
  box-shadow: 2px 2px 8px var(--shadow-light);
  position: relative;
  padding: 16px;
  margin: 16px 0;
}

.sticky-note::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-left: 15px solid transparent;
  border-top: 15px solid #f57f17;
}

/* 5. 标题样式 */
.handwritten-title {
  font-family: var(--font-handwritten);
  font-weight: 700;
  color: var(--ink-dark);
  margin-bottom: 24px;
  text-align: center;
}

.handwritten-title.large {
  font-size: var(--font-size-3xl);
}

.handwritten-title.medium {
  font-size: var(--font-size-2xl);
}

/* 6. 浮动装饰 */
.floating-emoji {
  position: absolute;
  font-size: 24px;
  animation: float 6s ease-in-out infinite;
  pointer-events: none;
  z-index: 2;
  opacity: 0.6;
  filter: drop-shadow(2px 2px 4px rgba(45, 55, 72, 0.1));
  transition: all 0.3s ease;
  user-select: none;
}

.floating-emoji:hover {
  transform: scale(1.2);
  opacity: 0.8;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-8px) rotate(2deg); }
  50% { transform: translateY(-15px) rotate(5deg); }
  75% { transform: translateY(-5px) rotate(-2deg); }
}

/* 新增背景装饰动画 */
@keyframes gentleFloat {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-3px) translateX(2px); }
  66% { transform: translateY(2px) translateX(-1px); }
}

@keyframes colorShift {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.5; }
}

/* 7. 错误提示样式 */
.error-message {
  color: #e53e3e;
  font-size: var(--font-size-sm);
  margin-top: 8px;
  font-family: var(--font-handwritten);
  transform: rotate(-0.5deg);
}

/* 8. 成功提示样式 */
.success-message {
  color: var(--accent-green);
  font-size: var(--font-size-sm);
  margin-top: 8px;
  font-family: var(--font-handwritten);
  transform: rotate(0.5deg);
}

/* 9. 加载状态 */
.loading-spinner {
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--accent-blue);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 10. 页面布局 */
.auth-container {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.auth-card {
  max-width: 420px;
  width: 100%;
  position: relative;
  z-index: 10;
  margin: auto;
}

/* 11. 背景装饰 */
.auth-container::before {
  content: '📚';
  position: absolute;
  top: 10%;
  left: 10%;
  font-size: 48px;
  opacity: 0.1;
  animation: float 4s ease-in-out infinite;
}

.auth-container::after {
  content: '✏️';
  position: absolute;
  bottom: 15%;
  right: 15%;
  font-size: 36px;
  opacity: 0.1;
  animation: float 5s ease-in-out infinite reverse;
}

/* 12. 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    padding: 16px;
  }

  .auth-card {
    max-width: 100%;
    margin: 0;
  }

  .sketch-card {
    padding: 20px;
  }

  .floating-emoji {
    font-size: 20px;
  }

  .handwritten-title.large {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 12px;
  }

  .sketch-card {
    padding: 16px;
  }

  .floating-emoji {
    font-size: 16px;
  }

  .handwritten-title.large {
    font-size: var(--font-size-xl);
  }
}

/* 13. Popconfirm 手绘风格 */
.semi-popover.semi-popconfirm {
  border: 2px solid var(--ink-dark) !important;
  border-radius: 12px !important;
  box-shadow: 3px 3px 0px var(--shadow-medium) !important;
  background: var(--paper-bg) !important;
  transform: rotate(-0.3deg);
}

.semi-popconfirm-title {
  font-family: var(--font-handwritten) !important;
  font-weight: 600 !important;
  color: var(--ink-dark) !important;
}

.semi-popconfirm-content {
  font-family: var(--font-system) !important;
  color: var(--ink-medium) !important;
}

.semi-popconfirm .semi-button {
  border-radius: 20px !important;
  font-family: var(--font-handwritten) !important;
  transform: rotate(0.5deg);
  transition: all 0.3s ease !important;
}

.semi-popconfirm .semi-button:hover {
  transform: rotate(0deg) translateY(-1px) !important;
}

/* 14. 确保全局样式不干扰 */
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.App {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
}

/* 15. 用户下拉菜单动画 */
@keyframes dropdownAppear {
  0% {
    opacity: 0;
    transform: rotate(-0.5deg) translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: rotate(-0.5deg) translateY(0px) scale(1);
  }
}
