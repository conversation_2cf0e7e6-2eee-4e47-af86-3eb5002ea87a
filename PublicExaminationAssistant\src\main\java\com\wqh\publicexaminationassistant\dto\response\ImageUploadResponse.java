package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 图片上传响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
public class ImageUploadResponse {

    /**
     * 图片ID
     */
    private String id;

    /**
     * 图片类型：question|answer|explanation
     */
    private String imageType;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 是否已压缩
     */
    private Boolean isCompressed;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 描述信息
     */
    private String description;
}
