package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.entity.WrongQuestion;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 错题本表Mapper接口
 * 提供错题本数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface WrongQuestionMapper extends BaseMapper<WrongQuestion> {

    /**
     * 根据学习记录ID查找错题
     */
    @Select("SELECT * FROM wrong_questions WHERE study_record_id = #{studyRecordId} ORDER BY created_at DESC")
    List<WrongQuestion> findByStudyRecordId(@Param("studyRecordId") String studyRecordId);

    /**
     * 统计用户错题总数
     */
    @Select("SELECT COUNT(*) FROM wrong_questions WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") String userId);

    /**
     * 统计用户各掌握状态的错题数量
     */
    @Select("SELECT mastery_status, COUNT(*) as count FROM wrong_questions WHERE user_id = #{userId} GROUP BY mastery_status")
    List<Map<String, Object>> countByUserIdGroupByMasteryStatus(@Param("userId") String userId);

    /**
     * 统计用户各题目类型的错题数量
     */
    @Select("SELECT question_type, COUNT(*) as count FROM wrong_questions WHERE user_id = #{userId} GROUP BY question_type")
    List<Map<String, Object>> countByUserIdGroupByQuestionType(@Param("userId") String userId);

    /**
     * 统计用户各难度等级的错题数量
     */
    @Select("SELECT difficulty_level, COUNT(*) as count FROM wrong_questions WHERE user_id = #{userId} GROUP BY difficulty_level")
    List<Map<String, Object>> countByUserIdGroupByDifficultyLevel(@Param("userId") String userId);

    /**
     * 获取用户需要复习的错题 (未掌握和复习中的错题)
     */
    @Select("SELECT * FROM wrong_questions WHERE user_id = #{userId} " +
            "AND mastery_status IN ('not_mastered', 'reviewing') " +
            "ORDER BY review_count ASC, created_at ASC " +
            "LIMIT #{limit}")
    List<WrongQuestion> findReviewQuestionsByUserId(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 获取用户最近添加的错题
     */
    @Select("SELECT * FROM wrong_questions WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT 10")
    List<WrongQuestion> findTop10ByUserIdOrderByCreatedAtDesc(@Param("userId") String userId);

    /**
     * 更新错题掌握状态并增加复习次数
     */
    @Update("UPDATE wrong_questions SET mastery_status = #{masteryStatus}, " +
            "review_count = review_count + 1, reviewed_at = NOW() " +
            "WHERE id = #{id} AND user_id = #{userId}")
    int updateMasteryStatusAndIncrementReviewCount(@Param("id") String id,
                                                   @Param("userId") String userId,
                                                   @Param("masteryStatus") String masteryStatus);

    /**
     * 增加错题复习次数
     */
    @Update("UPDATE wrong_questions SET review_count = review_count + 1, reviewed_at = NOW() " +
            "WHERE id = #{id} AND user_id = #{userId}")
    int incrementReviewCount(@Param("id") String id, @Param("userId") String userId);

    /**
     * 删除学习记录关联的错题
     */
    @Delete("DELETE FROM wrong_questions WHERE study_record_id = #{studyRecordId}")
    int deleteByStudyRecordId(@Param("studyRecordId") String studyRecordId);

    /**
     * 按学习模块筛选错题（支持错题表直接字段和关联查询study_records表）
     */
    @Select("<script>" +
            "SELECT wq.* FROM wrong_questions wq " +
            "LEFT JOIN study_records sr ON wq.study_record_id = sr.id " +
            "WHERE wq.user_id = #{userId} " +
            "<if test='questionType != null and questionType != \"\"'>" +
            "AND wq.question_type = #{questionType} " +
            "</if>" +
            "<if test='moduleType != null and moduleType != \"\"'>" +
            "AND (wq.module_type = #{moduleType} OR sr.module_type = #{moduleType}) " +
            "</if>" +
            "<if test='masteryStatus != null and masteryStatus != \"\"'>" +
            "AND wq.mastery_status = #{masteryStatus} " +
            "</if>" +
            "ORDER BY wq.created_at DESC" +
            "</script>")
    Page<WrongQuestion> selectWrongQuestionsByModuleType(Page<WrongQuestion> page,
                                                         @Param("userId") String userId,
                                                         @Param("questionType") String questionType,
                                                         @Param("moduleType") String moduleType,
                                                         @Param("masteryStatus") String masteryStatus);
}
