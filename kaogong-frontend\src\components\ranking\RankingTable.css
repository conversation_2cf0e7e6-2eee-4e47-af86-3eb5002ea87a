/* 排行榜表格样式 */
.ranking-table-container {
  background: var(--paper-white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ranking-table {
  font-family: var(--font-system);
}

/* 强制显示表头 - 调试样式 */
.ranking-table thead,
.ranking-table .semi-table-thead {
  display: table-header-group !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
}

.ranking-table thead tr,
.ranking-table .semi-table-thead .semi-table-row {
  display: table-row !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ranking-table thead th,
.ranking-table .semi-table-thead .semi-table-row-head {
  display: table-cell !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: var(--accent-blue) !important;
  color: white !important;
}

/* 表格头部样式 */
.ranking-table .semi-table-thead {
  display: table-header-group !important;
  visibility: visible !important;
}

.ranking-table .semi-table-thead .semi-table-row {
  display: table-row !important;
  visibility: visible !important;
}

.ranking-table .semi-table-thead .semi-table-row .semi-table-row-head {
  background: linear-gradient(135deg, var(--accent-blue), #3182ce);
  color: white !important;
  font-weight: 600;
  font-family: var(--font-handwritten);
  border: none;
  padding: 16px 12px;
  display: table-cell !important;
  visibility: visible !important;
  height: auto !important;
  min-height: 48px;
}

.ranking-table .semi-table-thead .semi-table-row .semi-table-row-head:first-child {
  border-top-left-radius: 12px;
}

.ranking-table .semi-table-thead .semi-table-row .semi-table-row-head:last-child {
  border-top-right-radius: 12px;
}

/* 表格行样式 */
.ranking-table .semi-table-tbody .semi-table-row {
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-light);
}

.ranking-table .semi-table-tbody .semi-table-row:hover {
  background: var(--accent-blue-light);
  transform: translateX(4px);
}

.ranking-table .semi-table-tbody .semi-table-row.current-user-row {
  background: linear-gradient(90deg, rgba(var(--accent-blue-rgb), 0.1), rgba(var(--accent-blue-rgb), 0.05));
  border-left: 4px solid var(--accent-blue);
}

.ranking-table .semi-table-tbody .semi-table-row.current-user-row:hover {
  background: linear-gradient(90deg, rgba(var(--accent-blue-rgb), 0.2), rgba(var(--accent-blue-rgb), 0.1));
}

/* 单元格样式 */
.ranking-table .semi-table-tbody .semi-table-row .semi-table-row-cell {
  padding: 16px 12px;
  vertical-align: middle;
}

/* 排名单元格 */
.rank-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rank-medal {
  font-size: 18px;
  min-width: 20px;
}

.rank-number {
  font-family: var(--font-handwritten);
  font-weight: bold;
  font-size: 16px;
  color: var(--ink-dark);
}

.rank-cell.current-user .rank-number {
  color: var(--accent-blue);
  font-size: 18px;
}

/* 用户单元格 */
.user-cell {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-cell.current-user {
  position: relative;
}

.user-cell.current-user::before {
  content: '👑';
  position: absolute;
  left: -8px;
  top: -4px;
  font-size: 12px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-4px); }
  60% { transform: translateY(-2px); }
}

/* 分数单元格 */
.score-cell {
  text-align: center;
}

.score-cell.current-user {
  position: relative;
}

.score-cell.current-user::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-blue), transparent);
  border-radius: 1px;
}

/* 变化单元格 */
.change-cell {
  text-align: center;
}

/* 详细数据单元格 */
.details-cell {
  max-width: 200px;
}

/* 空状态样式 */
.ranking-table-container .semi-empty {
  padding: 60px 20px;
  background: var(--paper-warm);
  border-radius: 12px;
  margin: 20px;
}

/* 分页样式 */
.ranking-table .semi-page {
  background: var(--paper-cream);
  padding: 16px;
  border-top: 1px solid var(--border-light);
}

.ranking-table .semi-page-item {
  border-radius: 6px;
  font-family: var(--font-handwritten);
  font-weight: 500;
}

.ranking-table .semi-page-item.semi-page-item-active {
  background: var(--accent-blue);
  border-color: var(--accent-blue);
}

/* 加载状态样式 */
.ranking-table .semi-table-tbody .semi-table-row.semi-table-row-loading {
  background: var(--paper-warm);
}

.ranking-table .semi-skeleton {
  background: linear-gradient(90deg, var(--paper-cream), var(--paper-warm), var(--paper-cream));
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ranking-table-container {
    margin: 0 10px;
    border-radius: 8px;
  }
  
  .ranking-table .semi-table-thead .semi-table-row .semi-table-row-head {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .ranking-table .semi-table-tbody .semi-table-row .semi-table-row-cell {
    padding: 12px 8px;
  }
  
  .user-info {
    gap: 1px;
  }
  
  .details-cell {
    max-width: 150px;
  }
  
  .rank-medal {
    font-size: 14px;
  }
  
  .rank-number {
    font-size: 14px;
  }
  
  /* 隐藏部分列在小屏幕上 */
  .ranking-table .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(5) {
    display: none;
  }
  
  .ranking-table .semi-table-thead .semi-table-row .semi-table-row-head:nth-child(5) {
    display: none;
  }
}

@media (max-width: 480px) {
  .ranking-table .semi-table-tbody .semi-table-row .semi-table-row-cell:nth-child(4) {
    display: none;
  }

  .ranking-table .semi-table-thead .semi-table-row .semi-table-row-head:nth-child(4) {
    display: none;
  }
}

/* 固定分页信息样式 */
.fixed-pagination-info {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) rotate(-0.5deg);
  background:
    /* 纸质背景 */
    linear-gradient(135deg, #fefdf8 0%, #faf9f4 100%);
  border: 2px solid rgba(120, 119, 108, 0.15);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  color: #4a5568;
  font-family: var(--font-handwritten);
  font-weight: 500;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  z-index: 1000;
  white-space: nowrap;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.fixed-pagination-info:hover {
  transform: translateX(-50%) rotate(0deg) translateY(-2px);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 移动端分页信息调整 */
@media (max-width: 768px) {
  .fixed-pagination-info {
    bottom: 15px;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 16px;
  }
}
