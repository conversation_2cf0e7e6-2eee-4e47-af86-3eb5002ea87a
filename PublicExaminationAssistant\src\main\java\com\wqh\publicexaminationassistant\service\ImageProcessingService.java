package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;

/**
 * 图片处理服务
 * 提供图片压缩、缩略图生成等功能
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImageProcessingService {

    private final FileUploadProperties fileUploadProperties;
    private final FileUploadService fileUploadService;

    /**
     * 图片尺寸信息
     */
    public static class ImageDimension {
        private final int width;
        private final int height;

        public ImageDimension(int width, int height) {
            this.width = width;
            this.height = height;
        }

        public int getWidth() {
            return width;
        }

        public int getHeight() {
            return height;
        }
    }

    /**
     * 处理上传的图片（压缩和生成缩略图）
     * 
     * @param originalPath 原始图片路径
     * @return 缩略图路径，如果生成失败则返回null
     */
    public String processUploadedImage(String originalPath) {
        try {
            log.info("开始处理图片: {}", originalPath);

            Path absolutePath = fileUploadService.getAbsolutePath(originalPath);
            File originalFile = absolutePath.toFile();

            if (!originalFile.exists()) {
                log.error("原始图片文件不存在: {}", absolutePath);
                return null;
            }

            // 读取原始图片
            BufferedImage originalImage = ImageIO.read(originalFile);
            if (originalImage == null) {
                log.error("无法读取图片文件: {}", originalPath);
                return null;
            }

            // 检查是否需要压缩原图
            compressImageIfNeeded(originalImage, originalFile);

            // 生成缩略图
            String thumbnailPath = generateThumbnail(originalImage, originalPath);

            log.info("图片处理完成: originalPath={}, thumbnailPath={}", originalPath, thumbnailPath);
            return thumbnailPath;

        } catch (Exception e) {
            log.error("图片处理失败: originalPath={}", originalPath, e);
            return null;
        }
    }

    /**
     * 获取图片尺寸信息
     * 
     * @param imagePath 图片路径
     * @return 图片尺寸，如果获取失败则返回null
     */
    public ImageDimension getImageDimension(String imagePath) {
        try {
            Path absolutePath = fileUploadService.getAbsolutePath(imagePath);
            File imageFile = absolutePath.toFile();

            if (!imageFile.exists()) {
                return null;
            }

            BufferedImage image = ImageIO.read(imageFile);
            if (image == null) {
                return null;
            }

            return new ImageDimension(image.getWidth(), image.getHeight());

        } catch (Exception e) {
            log.error("获取图片尺寸失败: imagePath={}", imagePath, e);
            return null;
        }
    }

    /**
     * 如果需要则压缩原图
     * 
     * @param originalImage 原始图片
     * @param originalFile 原始文件
     */
    private void compressImageIfNeeded(BufferedImage originalImage, File originalFile) {
        try {
            FileUploadProperties.ImageConfig imageConfig = fileUploadProperties.getImage();
            int maxWidth = imageConfig.getMaxWidth();
            int maxHeight = imageConfig.getMaxHeight();

            // 检查是否需要压缩
            if (originalImage.getWidth() <= maxWidth && originalImage.getHeight() <= maxHeight) {
                log.debug("图片尺寸在允许范围内，无需压缩: {}x{}", 
                         originalImage.getWidth(), originalImage.getHeight());
                return;
            }

            // 计算压缩后的尺寸
            ImageDimension targetSize = calculateTargetSize(
                originalImage.getWidth(), originalImage.getHeight(), maxWidth, maxHeight);

            // 压缩图片
            BufferedImage compressedImage = resizeImage(originalImage, targetSize);

            // 保存压缩后的图片（覆盖原文件）
            String format = getImageFormat(originalFile.getName());
            ImageIO.write(compressedImage, format, originalFile);

            log.info("图片压缩完成: 原尺寸={}x{}, 压缩后={}x{}", 
                    originalImage.getWidth(), originalImage.getHeight(),
                    targetSize.getWidth(), targetSize.getHeight());

        } catch (Exception e) {
            log.error("图片压缩失败", e);
            // 压缩失败不影响主流程，继续使用原图
        }
    }

    /**
     * 生成缩略图
     * 
     * @param originalImage 原始图片
     * @param originalPath 原始图片路径
     * @return 缩略图路径
     */
    private String generateThumbnail(BufferedImage originalImage, String originalPath) {
        try {
            FileUploadProperties.ThumbnailConfig thumbnailConfig = fileUploadProperties.getThumbnail();
            int thumbnailWidth = thumbnailConfig.getWidth();
            int thumbnailHeight = thumbnailConfig.getHeight();

            // 计算缩略图尺寸（保持宽高比）
            ImageDimension thumbnailSize = calculateTargetSize(
                originalImage.getWidth(), originalImage.getHeight(), thumbnailWidth, thumbnailHeight);

            // 生成缩略图
            BufferedImage thumbnailImage = resizeImage(originalImage, thumbnailSize);

            // 生成缩略图文件路径
            String thumbnailPath = generateThumbnailPath(originalPath);
            Path thumbnailAbsolutePath = fileUploadService.getAbsolutePath(thumbnailPath);

            // 确保缩略图目录存在
            File thumbnailDir = thumbnailAbsolutePath.getParent().toFile();
            if (!thumbnailDir.exists()) {
                thumbnailDir.mkdirs();
            }

            // 保存缩略图
            String format = getImageFormat(originalPath);
            ImageIO.write(thumbnailImage, format, thumbnailAbsolutePath.toFile());

            log.info("缩略图生成成功: thumbnailPath={}, size={}x{}", 
                    thumbnailPath, thumbnailSize.getWidth(), thumbnailSize.getHeight());

            return thumbnailPath;

        } catch (Exception e) {
            log.error("缩略图生成失败: originalPath={}", originalPath, e);
            return null;
        }
    }

    /**
     * 调整图片尺寸
     * 
     * @param originalImage 原始图片
     * @param targetSize 目标尺寸
     * @return 调整后的图片
     */
    private BufferedImage resizeImage(BufferedImage originalImage, ImageDimension targetSize) {
        BufferedImage resizedImage = new BufferedImage(
            targetSize.getWidth(), targetSize.getHeight(), BufferedImage.TYPE_INT_RGB);

        Graphics2D g2d = resizedImage.createGraphics();
        
        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制调整后的图片
        g2d.drawImage(originalImage, 0, 0, targetSize.getWidth(), targetSize.getHeight(), null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 计算目标尺寸（保持宽高比）
     * 
     * @param originalWidth 原始宽度
     * @param originalHeight 原始高度
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 目标尺寸
     */
    private ImageDimension calculateTargetSize(int originalWidth, int originalHeight, 
                                             int maxWidth, int maxHeight) {
        if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
            return new ImageDimension(originalWidth, originalHeight);
        }

        double widthRatio = (double) maxWidth / originalWidth;
        double heightRatio = (double) maxHeight / originalHeight;
        double ratio = Math.min(widthRatio, heightRatio);

        int targetWidth = (int) (originalWidth * ratio);
        int targetHeight = (int) (originalHeight * ratio);

        return new ImageDimension(targetWidth, targetHeight);
    }

    /**
     * 生成缩略图路径
     * 
     * @param originalPath 原始图片路径
     * @return 缩略图路径
     */
    private String generateThumbnailPath(String originalPath) {
        // 将原路径中的子目录替换为thumbnails
        // 例如：wrong-questions/question/2024/07/19/abc.jpg -> wrong-questions/thumbnails/2024/07/19/abc.jpg
        String[] pathParts = originalPath.split("/");
        if (pathParts.length >= 2) {
            pathParts[1] = "thumbnails"; // 替换第二部分为thumbnails
        }
        return String.join("/", pathParts);
    }

    /**
     * 获取图片格式
     * 
     * @param filename 文件名
     * @return 图片格式
     */
    private String getImageFormat(String filename) {
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        
        // 统一使用jpg格式保存（兼容性更好）
        if ("jpeg".equals(extension) || "jpg".equals(extension)) {
            return "jpg";
        } else if ("png".equals(extension)) {
            return "png";
        } else if ("gif".equals(extension)) {
            return "gif";
        } else if ("webp".equals(extension)) {
            return "jpg"; // WebP转换为JPG保存
        } else {
            return "jpg"; // 默认使用JPG
        }
    }
}
