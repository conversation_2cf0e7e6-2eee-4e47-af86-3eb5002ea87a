import api from './api';
import { 
  UserReputationStats, 
  ReputationLog, 
  ProtectionInfo, 
  ReputationLevel 
} from '../types/reputation';

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

class ReputationService {
  /**
   * 获取用户信誉统计信息
   */
  async getUserReputationStats(): Promise<UserReputationStats> {
    const response = await api.get<ApiResponse<UserReputationStats>>('/api/v1/reputation/stats');
    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    return response.data.data;
  }

  /**
   * 获取用户信誉变更记录
   */
  async getReputationLogs(limit: number = 20): Promise<ReputationLog[]> {
    const response = await api.get<ApiResponse<ReputationLog[]>>('/api/v1/reputation/logs', {
      params: { limit }
    });
    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    return response.data.data;
  }

  /**
   * 获取指定分类的信誉记录
   */
  async getReputationLogsByCategory(category: string, limit: number = 10): Promise<ReputationLog[]> {
    const response = await api.get<ApiResponse<ReputationLog[]>>(`/api/v1/reputation/logs/${category}`, {
      params: { limit }
    });
    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    return response.data.data;
  }

  /**
   * 获取保护期状态
   */
  async getProtectionStatus(): Promise<ProtectionInfo> {
    const response = await api.get<ApiResponse<ProtectionInfo>>('/api/v1/reputation/protection');
    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    return response.data.data;
  }

  /**
   * 获取信誉等级配置信息
   */
  async getReputationLevels(): Promise<ReputationLevel[]> {
    const response = await api.get<ApiResponse<ReputationLevel[]>>('/api/v1/reputation/levels');
    if (response.data.code !== 200) {
      throw new Error(response.data.message);
    }
    return response.data.data;
  }
}

export default new ReputationService();
