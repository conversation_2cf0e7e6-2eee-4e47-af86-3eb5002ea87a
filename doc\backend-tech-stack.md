# 后端技术栈选型方案

## 基础环境

- **JDK版本**: Java 1.8 (OpenJDK 8 / Oracle JDK 8)
- **构建工具**: Maven 3.6+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

## 核心框架选择

### 1. Web框架 - Spring Boot 2.7.x

**选择理由:**
- 与JDK 1.8完美兼容
- 成熟稳定的生态系统
- 自动配置，开发效率高
- 丰富的Starter组件
- 良好的微服务支持

**核心依赖:**
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.7.18</version>
</dependency>
```

### 2. 数据库访问层

#### 2.1 ORM框架 - MyBatis Plus 3.5.x

**选择理由:**
- 基于MyBatis，学习成本低
- 强大的代码生成器
- 内置分页插件
- 支持多租户
- 性能优秀

**核心依赖:**
```xml
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3</version>
</dependency>
```

#### 2.2 数据库连接池 - HikariCP

**选择理由:**
- Spring Boot 2.x默认连接池
- 性能优秀，内存占用小
- 配置简单

#### 2.3 数据库版本管理 - Flyway

**选择理由:**
- 数据库版本控制
- 支持团队协作
- 自动化部署

```xml
<dependency>
    <groupId>org.flywaydb</groupId>
    <artifactId>flyway-core</artifactId>
    <version>8.5.13</version>
</dependency>
```

### 3. 认证授权

#### 3.1 JWT认证 - jjwt 0.11.x

**选择理由:**
- 无状态认证，适合分布式
- 支持微服务架构
- 性能好，扩展性强

```xml
<dependency>
    <groupId>io.jsonwebtoken</groupId>
    <artifactId>jjwt-api</artifactId>
    <version>0.11.5</version>
</dependency>
```

#### 3.2 权限控制 - Spring Security 5.7.x

**选择理由:**
- Spring生态原生支持
- 功能强大，安全性高
- 支持多种认证方式
- 与Spring Boot深度集成

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
    <version>2.7.18</version>
</dependency>
```

### 4. 缓存方案

#### 4.1 Redis集成 - Spring Data Redis

**选择理由:**
- Spring官方支持
- 支持多种数据结构
- 集群支持
- 事务支持

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
    <version>2.7.18</version>
</dependency>
```

#### 4.2 本地缓存 - Caffeine

**选择理由:**
- 高性能本地缓存
- 支持异步加载
- 丰富的缓存策略

```xml
<dependency>
    <groupId>com.github.ben-manes.caffeine</groupId>
    <artifactId>caffeine</artifactId>
    <version>2.9.3</version>
</dependency>
```

### 5. 消息队列

#### 5.1 RabbitMQ - Spring AMQP

**选择理由:**
- 可靠性高
- 支持多种消息模式
- 管理界面友好
- 集群支持

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
    <version>2.7.18</version>
</dependency>
```

### 6. 工具库

#### 6.1 JSON处理 - Jackson (Spring Boot默认)

#### 6.2 工具类库 - Hutool 5.8.x

**选择理由:**
- 国产工具库，文档友好
- 功能丰富，API简洁
- 与JDK 1.8兼容性好

```xml
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-all</artifactId>
    <version>5.8.22</version>
</dependency>
```

#### 6.3 参数验证 - Hibernate Validator

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
    <version>2.7.18</version>
</dependency>
```

#### 6.4 API文档 - Swagger 3.x

```xml
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
    <version>3.0.0</version>
</dependency>
```

### 7. 监控和日志

#### 7.1 应用监控 - Spring Boot Actuator

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
    <version>2.7.18</version>
</dependency>
```

#### 7.2 日志框架 - Logback (Spring Boot默认)

#### 7.3 分布式链路追踪 - Spring Cloud Sleuth

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-sleuth</artifactId>
    <version>3.1.9</version>
</dependency>
```

## 微服务支持

### 1. 服务注册发现 - Nacos 2.x

**选择理由:**
- 阿里巴巴开源，国内生态好
- 支持配置管理
- 控制台友好
- 性能优秀

```xml
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    <version>2021.0.5.0</version>
</dependency>
```

### 2. 配置中心 - Nacos Config

```xml
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    <version>2021.0.5.0</version>
</dependency>
```

### 3. 服务调用 - OpenFeign

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
    <version>3.1.8</version>
</dependency>
```

### 4. 熔断器 - Sentinel

```xml
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
    <version>2021.0.5.0</version>
</dependency>
```

### 5. 网关 - Spring Cloud Gateway

```xml
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-gateway</artifactId>
    <version>3.1.8</version>
</dependency>
```

## 项目结构建议

```
kaogong-backend/
├── kaogong-common/          # 公共模块
├── kaogong-gateway/         # 网关服务
├── kaogong-auth/           # 认证服务
├── kaogong-user/           # 用户服务
├── kaogong-study/          # 刷题记录服务
├── kaogong-desk/           # 小桌排行服务
├── kaogong-reputation/     # 信誉系统服务
├── kaogong-announcement/   # 考试公告服务
├── kaogong-analytics/      # 数据分析服务
└── kaogong-notification/   # 通知服务
```

## 开发规范

### 1. 代码规范
- 使用阿里巴巴Java开发手册
- 集成CheckStyle和SpotBugs
- 统一代码格式化规则

### 2. 接口规范
- RESTful API设计
- 统一响应格式
- 统一异常处理

### 3. 数据库规范
- 统一命名规范
- 索引优化策略
- 分库分表准备

这个技术栈选型基于JDK 1.8，选择了成熟稳定的组件，既保证了系统的可靠性，又具备良好的扩展性。你觉得这个方案如何？有需要调整的地方吗？
