package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.DeskMember;
import org.apache.ibatis.annotations.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 小桌成员表Mapper接口
 * 提供小桌成员数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface DeskMemberMapper extends BaseMapper<DeskMember> {

    /**
     * 查询小桌成员列表（包含用户信息）
     */
    @Select("SELECT dm.*, u.username, u.nickname, u.reputation_score, u.reputation_level " +
            "FROM desk_members dm " +
            "LEFT JOIN users u ON dm.user_id = u.id " +
            "WHERE dm.desk_id = #{deskId} AND dm.status = 'active' " +
            "ORDER BY dm.role DESC, dm.joined_at ASC")
    List<Map<String, Object>> findMembersByDeskId(@Param("deskId") String deskId);

    /**
     * 查询用户加入的小桌（包含小桌信息）
     */
    @Select("SELECT dm.*, d.name as desk_name, d.description, d.owner_id, d.current_members, d.max_members " +
            "FROM desk_members dm " +
            "LEFT JOIN desks d ON dm.desk_id = d.id " +
            "WHERE dm.user_id = #{userId} AND dm.status = 'active' AND d.status = 'active' " +
            "ORDER BY dm.joined_at DESC")
    List<Map<String, Object>> findDesksByUserId(@Param("userId") String userId);

    /**
     * 检查用户是否已是成员
     */
    @Select("SELECT COUNT(*) FROM desk_members WHERE desk_id = #{deskId} AND user_id = #{userId} AND status = 'active'")
    int countByDeskIdAndUserId(@Param("deskId") String deskId, @Param("userId") String userId);

    /**
     * 统计小桌活跃成员数
     */
    @Select("SELECT COUNT(*) FROM desk_members WHERE desk_id = #{deskId} AND status = 'active'")
    int countActiveMembersByDeskId(@Param("deskId") String deskId);

    /**
     * 获取小桌排行榜数据（基于学习记录）
     */
    @Select("SELECT dm.user_id, u.username, u.nickname, u.reputation_score, " +
            "COUNT(DISTINCT sr.id) as study_days, " +
            "COALESCE(SUM(sr.question_count), 0) as total_questions, " +
            "COALESCE(SUM(sr.correct_count), 0) as total_correct, " +
            "CASE WHEN SUM(sr.question_count) > 0 THEN " +
            "ROUND(SUM(sr.correct_count) * 100.0 / SUM(sr.question_count), 2) ELSE 0 END as accuracy_rate " +
            "FROM desk_members dm " +
            "LEFT JOIN users u ON dm.user_id = u.id " +
            "LEFT JOIN study_records sr ON dm.user_id = sr.user_id AND sr.study_date >= #{startDate} " +
            "WHERE dm.desk_id = #{deskId} AND dm.status = 'active' " +
            "GROUP BY dm.user_id, u.username, u.nickname, u.reputation_score " +
            "ORDER BY total_questions DESC, study_days DESC, accuracy_rate DESC")
    List<Map<String, Object>> getDeskRanking(@Param("deskId") String deskId, @Param("startDate") LocalDate startDate);

    /**
     * 查询小桌成员的详细信息（包含最近活跃度）
     */
    @Select("SELECT dm.*, u.username, u.nickname, u.reputation_score, u.reputation_level, " +
            "COUNT(DISTINCT sr.id) as recent_study_days, " +
            "COALESCE(SUM(sr.question_count), 0) as recent_questions " +
            "FROM desk_members dm " +
            "LEFT JOIN users u ON dm.user_id = u.id " +
            "LEFT JOIN study_records sr ON dm.user_id = sr.user_id AND sr.study_date >= #{startDate} " +
            "WHERE dm.desk_id = #{deskId} AND dm.status = 'active' " +
            "GROUP BY dm.id, dm.desk_id, dm.user_id, dm.role, dm.status, dm.join_reason, dm.joined_at, dm.last_active_at, " +
            "u.username, u.nickname, u.reputation_score, u.reputation_level " +
            "ORDER BY dm.role DESC, dm.joined_at ASC")
    List<Map<String, Object>> findMembersWithActivity(@Param("deskId") String deskId, @Param("startDate") LocalDate startDate);

    /**
     * 更新成员最后活跃时间
     */
    @Update("UPDATE desk_members SET last_active_at = NOW() WHERE desk_id = #{deskId} AND user_id = #{userId}")
    int updateLastActiveTime(@Param("deskId") String deskId, @Param("userId") String userId);

    /**
     * 查询用户在指定小桌中的成员信息
     */
    @Select("SELECT * FROM desk_members WHERE desk_id = #{deskId} AND user_id = #{userId} AND status = 'active'")
    DeskMember findByDeskIdAndUserId(@Param("deskId") String deskId, @Param("userId") String userId);

    /**
     * 统计用户加入的小桌数量
     */
    @Select("SELECT COUNT(*) FROM desk_members dm " +
            "LEFT JOIN desks d ON dm.desk_id = d.id " +
            "WHERE dm.user_id = #{userId} AND dm.status = 'active' AND d.status = 'active'")
    Long countActiveDeskssByUserId(@Param("userId") String userId);
}
