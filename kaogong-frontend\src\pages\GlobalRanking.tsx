import React, { useState, useEffect } from 'react';
import { Layout, Typography, Space, Toast, Spin, Card, Row, Col } from '@douyinfe/semi-ui';
import { IconRefresh, IconStar } from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';
import FilterPanel from '../components/ranking/FilterPanel';
import UserRankCard from '../components/ranking/UserRankCard';
import RankingTable from '../components/ranking/RankingTable';
import {
  rankingService,
  RankingType,
  RankingPeriod,
  GlobalRankingResponse,
  UserRankResponse,
  PageResponse
} from '../services/rankingService';
import { useAuthStore } from '../stores/useAuthStore';
import './GlobalRanking.css';

const { Content } = Layout;
const { Title, Text } = Typography;

const GlobalRanking: React.FC = () => {
  // 状态管理
  const [selectedType, setSelectedType] = useState<RankingType>('study_questions');
  const [selectedPeriod, setSelectedPeriod] = useState<RankingPeriod>('weekly');
  const [rankingData, setRankingData] = useState<PageResponse<GlobalRankingResponse> | null>(null);
  const [userRank, setUserRank] = useState<UserRankResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [userRankLoading, setUserRankLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);

  // 获取排行榜数据
  const fetchRankingData = async (page: number = 1) => {
    setLoading(true);
    try {
      console.log('开始获取排行榜数据:', { selectedType, selectedPeriod, page, pageSize });
      const response = await rankingService.getGlobalRanking(
        selectedType,
        selectedPeriod,
        page,
        pageSize
      );
      console.log('排行榜数据响应:', response);
      console.log('排行榜记录数量:', response?.records?.length || 0);
      setRankingData(response);
      setCurrentPage(page);
    } catch (error) {
      console.error('获取排行榜数据失败:', error);
      Toast.error('获取排行榜数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户个人排名
  const fetchUserRank = async () => {
    setUserRankLoading(true);
    try {
      console.log('开始获取个人排名:', { selectedType, selectedPeriod });

      // 检查用户认证状态
      const authStore = useAuthStore.getState();
      console.log('用户认证状态:', {
        isAuthenticated: authStore.isAuthenticated,
        hasToken: !!authStore.token,
        userId: authStore.user?.id
      });

      const response = await rankingService.getMyRank(selectedType, selectedPeriod);
      console.log('个人排名响应:', response);
      setUserRank(response);
    } catch (error) {
      console.error('获取个人排名失败:', error);
      // 不显示错误提示，因为用户可能未上榜
      setUserRank(null);
    } finally {
      setUserRankLoading(false);
    }
  };

  // 刷新数据
  const refreshData = async () => {
    await Promise.all([
      fetchRankingData(currentPage),
      fetchUserRank()
    ]);
    Toast.success('数据已刷新');
  };

  // 处理筛选条件变化
  const handleTypeChange = (type: RankingType) => {
    console.log('GlobalRanking - Type change:', type);
    setSelectedType(type);
    setCurrentPage(1);
  };

  const handlePeriodChange = (period: RankingPeriod) => {
    console.log('GlobalRanking - Period change:', period);
    setSelectedPeriod(period);
    setCurrentPage(1);
  };

  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchRankingData(page);
  };

  // 初始化和筛选条件变化时重新获取数据
  useEffect(() => {
    fetchRankingData(1);
    fetchUserRank();
  }, [selectedType, selectedPeriod]);

  return (
    <div className="global-ranking-page">
      <Navigation />

      {/* 手绘风格页面容器 */}
      <div className="ranking-paper-container">
        {/* 纸质背景装饰 */}
        <div className="paper-decoration">
          <div className="paper-corner top-left"></div>
          <div className="paper-corner top-right"></div>
          <div className="paper-corner bottom-left"></div>
          <div className="paper-corner bottom-right"></div>
        </div>

        {/* 手绘风格标题区域 */}
        <div className="handwritten-header">
          <div className="header-top-row">
            <div className="title-section">
              <div className="title-doodle">
                <span className="doodle-star">⭐</span>
                <h1 className="handwritten-title">全站排行榜</h1>
                <span className="doodle-trophy">🏆</span>
              </div>
            </div>

            {/* 刷新按钮移到右侧 */}
            <button
              className="handwritten-refresh-btn"
              onClick={refreshData}
              disabled={loading}
            >
              <IconRefresh className={loading ? 'spinning' : ''} />
              <span>刷新数据</span>
            </button>
          </div>

          <p className="handwritten-subtitle">
            与全站用户一起竞技，展示你的学习成果
          </p>
          <div className="title-underline"></div>
        </div>

        {/* 筛选区域 - 直接放在页面中，不用卡片包装 */}
        <div className="ranking-filters-section">
          <div className="filters-header">
            <span className="filter-emoji">🎯</span>
            <h2 className="filters-title">筛选条件</h2>
            <span className="filter-emoji">📊</span>
          </div>
          <FilterPanel
            selectedType={selectedType}
            selectedPeriod={selectedPeriod}
            onTypeChange={handleTypeChange}
            onPeriodChange={handlePeriodChange}
            loading={loading}
          />
        </div>

        {/* 主要内容区域 */}
        <div className="ranking-main-content">
          {/* 个人排名和排行榜的容器 */}
          <div className="ranking-content-wrapper">
            {/* 个人排名卡片 - sketch-card 风格 */}
            <div className="sketch-card user-rank-card">
              <div className="card-header">
                <span className="header-emoji">🏅</span>
                <h3 className="card-title">我的排名</h3>
              </div>
              <UserRankCard
                userRank={userRank}
                loading={userRankLoading}
                type={selectedType}
              />
            </div>

            {/* 排行榜主体 - sketch-card 风格 */}
            <div className="sketch-card ranking-table-card">
              <div className="card-header doodle-border">
                <div className="ranking-title-section">
                  <span className="ranking-emoji">
                    {selectedType === 'study_questions' && '📚'}
                    {selectedType === 'study_accuracy' && '🎯'}
                    {selectedType === 'study_time' && '⏱️'}
                    {selectedType === 'reputation_score' && '⭐'}
                    {selectedType === 'comprehensive' && '🏆'}
                  </span>
                  <h2 className="ranking-title">
                    {selectedType === 'study_questions' && '刷题数量排行榜'}
                    {selectedType === 'study_accuracy' && '正确率排行榜'}
                    {selectedType === 'study_time' && '学习时长排行榜'}
                    {selectedType === 'reputation_score' && '信誉分数排行榜'}
                    {selectedType === 'comprehensive' && '综合排行榜'}
                  </h2>
                </div>
                <div className="sticky-note period-note">
                  {selectedPeriod === 'daily' && '日榜'}
                  {selectedPeriod === 'weekly' && '周榜'}
                  {selectedPeriod === 'monthly' && '月榜'}
                  {selectedPeriod === 'all_time' && '总榜'}
                </div>
              </div>

              <div className="card-body">
                {loading && !rankingData ? (
                  <div className="sketch-loading">
                    <div className="loading-doodle">
                      <Spin size="large" />
                      <div className="loading-emoji">📊</div>
                    </div>
                    <p className="loading-message">正在加载排行榜数据...</p>
                  </div>
                ) : (
                  <RankingTable
                    data={rankingData?.records || []}
                    loading={loading}
                    type={selectedType}
                    onPageChange={handlePageChange}
                    pagination={rankingData ? {
                      current: currentPage,
                      total: rankingData.total,
                      pageSize: pageSize
                    } : undefined}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 底部说明 - 便利贴风格 */}
        <div className="sticky-note footer-note">
          <div className="note-content">
            <div className="note-icon">💡</div>
            <div className="note-text">
              <p className="note-main">排行榜每日凌晨2点自动更新</p>
              <p className="note-sub">数据统计基于用户的学习记录和信誉系统 | 继续努力学习，争取更好的排名！</p>
            </div>
            <div className="floating-emoji" style={{top: '10px', right: '20px'}}>📚</div>
            <div className="floating-emoji" style={{bottom: '15px', left: '25px'}}>🎯</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlobalRanking;
