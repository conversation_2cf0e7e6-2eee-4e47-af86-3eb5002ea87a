import React from 'react';
import { Table, Tag, Button, Select, Space, Tooltip } from '@douyinfe/semi-ui';
import { IconEyeOpened, IconDelete } from '@douyinfe/semi-icons';

/**
 * 表格布局测试组件
 * 用于验证错题列表表格的布局优化效果
 */
const TableLayoutTest: React.FC = () => {
  // 模拟数据
  const mockData = [
    {
      id: '1',
      moduleType: 'XINGCE',
      moduleName: '行测',
      difficultyLevel: 'MEDIUM',
      questionContent: '这是一道关于数量关系的题目，需要计算两个数的最大公约数和最小公倍数，题目内容比较长，用来测试文本截断效果',
      masteryStatus: 'NOT_MASTERED',
      reviewCount: 3,
      createdAt: '2024-07-20T10:30:00Z'
    },
    {
      id: '2',
      moduleType: 'SHENLUN',
      moduleName: '申论',
      difficultyLevel: 'HARD',
      questionContent: '申论写作题目',
      masteryStatus: 'PARTIALLY_MASTERED',
      reviewCount: 1,
      createdAt: '2024-07-19T15:20:00Z'
    }
  ];

  const MASTERY_STATUS_MAP = {
    NOT_MASTERED: { text: '未掌握', icon: '❌' },
    PARTIALLY_MASTERED: { text: '部分掌握', icon: '⚠️' },
    MASTERED: { text: '已掌握', icon: '✅' }
  };

  const DIFFICULTY_LEVEL_MAP = {
    EASY: { text: '简单', color: 'green' },
    MEDIUM: { text: '中等', color: 'orange' },
    HARD: { text: '困难', color: 'red' }
  };

  const MODULE_TYPE_MAP = {
    XINGCE: '行测',
    SHENLUN: '申论'
  };

  const columns = [
    {
      title: '学习模块',
      dataIndex: 'moduleType',
      key: 'moduleType',
      width: 120,
      render: (moduleType: string, record: any) => (
        <Tag color="cyan" size="small">
          {record.moduleName || MODULE_TYPE_MAP[moduleType as keyof typeof MODULE_TYPE_MAP] || moduleType}
        </Tag>
      )
    },
    {
      title: '难度',
      dataIndex: 'difficultyLevel',
      key: 'difficultyLevel',
      width: 80,
      render: (difficultyLevel: string) => {
        const difficulty = DIFFICULTY_LEVEL_MAP[difficultyLevel as keyof typeof DIFFICULTY_LEVEL_MAP];
        return (
          <Tag color={difficulty?.color || 'grey'} size="small">
            {difficulty?.text || difficultyLevel}
          </Tag>
        );
      }
    },
    {
      title: '题目内容',
      dataIndex: 'questionContent',
      key: 'questionContent',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: (content: string) => (
        <Tooltip content={content} position="topLeft">
          <div style={{ 
            maxWidth: '230px', 
            overflow: 'hidden', 
            textOverflow: 'ellipsis', 
            whiteSpace: 'nowrap',
            cursor: 'pointer'
          }}>
            {content || '暂无内容'}
          </div>
        </Tooltip>
      )
    },
    {
      title: '掌握状态',
      dataIndex: 'masteryStatus',
      key: 'masteryStatus',
      width: 140,
      render: (masteryStatus: string) => (
        <Select
          value={masteryStatus}
          size="small"
          style={{ width: 120 }}
          optionList={Object.entries(MASTERY_STATUS_MAP).map(([key, value]) => ({
            value: key,
            label: (
              <Space size="small">
                {value.icon}
                <span>{value.text}</span>
              </Space>
            )
          }))}
        />
      )
    },
    {
      title: '复习次数',
      dataIndex: 'reviewCount',
      key: 'reviewCount',
      width: 80,
      render: (count: number) => `${count || 0}次`
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (createdAt: string) => new Date(createdAt).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: () => (
        <Space size="small">
          <Tooltip content="查看详情">
            <Button
              type="tertiary"
              theme="borderless"
              size="small"
              icon={<IconEyeOpened />}
              style={{ padding: '4px' }}
            />
          </Tooltip>
          <Tooltip content="删除错题">
            <Button
              type="danger"
              theme="borderless"
              size="small"
              icon={<IconDelete />}
              style={{ padding: '4px' }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>表格布局测试</h2>
      <div style={{ border: '1px solid #ccc', borderRadius: '8px', overflow: 'hidden' }}>
        <Table
          columns={columns}
          dataSource={mockData}
          pagination={false}
          rowKey="id"
          size="small"
          scroll={{ 
            x: 'max-content',
            y: 400
          }}
          style={{ 
            minWidth: '800px'
          }}
        />
      </div>
      
      <div style={{ marginTop: '20px', padding: '16px', background: '#f5f5f5', borderRadius: '8px' }}>
        <h3>布局优化说明：</h3>
        <ul>
          <li>✅ 题目内容列限制最大宽度250px，超出显示省略号</li>
          <li>✅ 掌握状态列宽度增加到140px，确保文字完整显示</li>
          <li>✅ 操作列使用图标按钮，减少宽度到100px</li>
          <li>✅ 表格支持水平滚动，避免在小屏幕上布局破坏</li>
          <li>✅ 操作列固定在右侧，便于操作</li>
        </ul>
      </div>
    </div>
  );
};

export default TableLayoutTest;
