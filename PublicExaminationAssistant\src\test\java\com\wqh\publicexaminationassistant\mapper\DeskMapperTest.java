package com.wqh.publicexaminationassistant.mapper;

import com.wqh.publicexaminationassistant.entity.Desk;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DeskMapper测试类
 * 验证小桌数据访问层的基础功能
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class DeskMapperTest {

    @Autowired
    private DeskMapper deskMapper;

    @Test
    @DisplayName("测试创建和查询小桌")
    void testCreateAndFindDesk() {
        // 创建测试小桌
        Desk desk = new Desk();
        desk.setName("测试小桌");
        desk.setDescription("这是一个测试小桌");
        desk.setOwnerId("test-user-id");
        desk.setMaxMembers(6);
        desk.setCurrentMembers(1);
        desk.setStatus("active");

        // 插入数据库
        int result = deskMapper.insert(desk);
        assertEquals(1, result, "插入应该成功");
        assertNotNull(desk.getId(), "ID应该被自动生成");

        // 查询验证
        Desk foundDesk = deskMapper.selectById(desk.getId());
        assertNotNull(foundDesk, "应该能查询到刚创建的小桌");
        assertEquals("测试小桌", foundDesk.getName(), "名称应该匹配");
        assertEquals("test-user-id", foundDesk.getOwnerId(), "桌长ID应该匹配");
    }

    @Test
    @DisplayName("测试按桌长ID查询小桌")
    void testFindByOwnerId() {
        // 创建测试数据
        String ownerId = "test-owner-id";
        
        Desk desk1 = new Desk("小桌1", "描述1", ownerId);
        Desk desk2 = new Desk("小桌2", "描述2", ownerId);
        Desk desk3 = new Desk("小桌3", "描述3", "other-user-id");
        
        deskMapper.insert(desk1);
        deskMapper.insert(desk2);
        deskMapper.insert(desk3);

        // 查询指定桌长的小桌
        List<Desk> ownerDesks = deskMapper.findByOwnerId(ownerId);
        
        assertEquals(2, ownerDesks.size(), "应该查询到2个小桌");
        assertTrue(ownerDesks.stream().allMatch(d -> ownerId.equals(d.getOwnerId())), 
                  "所有小桌的桌长ID都应该匹配");
    }

    @Test
    @DisplayName("测试按名称搜索小桌")
    void testSearchByName() {
        // 创建测试数据
        Desk desk1 = new Desk("数学学习小桌", "专注数学学习", "user1");
        Desk desk2 = new Desk("英语交流小桌", "英语口语练习", "user2");
        Desk desk3 = new Desk("综合学习小桌", "各科目综合学习", "user3");
        
        deskMapper.insert(desk1);
        deskMapper.insert(desk2);
        deskMapper.insert(desk3);

        // 搜索包含"学习"的小桌
        List<Desk> searchResults = deskMapper.searchByName("学习");
        
        assertEquals(2, searchResults.size(), "应该找到2个包含'学习'的小桌");
        assertTrue(searchResults.stream().allMatch(d -> d.getName().contains("学习")), 
                  "所有结果都应该包含'学习'关键词");
    }

    @Test
    @DisplayName("测试更新成员数量")
    void testUpdateMemberCount() {
        // 创建测试小桌
        Desk desk = new Desk("测试小桌", "测试描述", "test-user");
        deskMapper.insert(desk);

        // 更新成员数量
        int result = deskMapper.updateMemberCount(desk.getId(), 5);
        assertEquals(1, result, "更新应该成功");

        // 验证更新结果
        Desk updatedDesk = deskMapper.selectById(desk.getId());
        assertEquals(5, updatedDesk.getCurrentMembers(), "成员数量应该被更新为5");
    }

    @Test
    @DisplayName("测试获取小桌统计信息")
    void testGetDeskStatistics() {
        // 创建测试数据
        Desk desk1 = new Desk("小桌1", "描述1", "user1");
        desk1.setCurrentMembers(3);
        Desk desk2 = new Desk("小桌2", "描述2", "user2");
        desk2.setCurrentMembers(5);
        Desk desk3 = new Desk("小桌3", "描述3", "user3");
        desk3.setStatus("dissolved"); // 已解散的小桌
        
        deskMapper.insert(desk1);
        deskMapper.insert(desk2);
        deskMapper.insert(desk3);

        // 获取统计信息
        Map<String, Object> statistics = deskMapper.getDeskStatistics();
        
        assertNotNull(statistics, "统计信息不应该为空");
        assertTrue(statistics.containsKey("total_desks"), "应该包含总小桌数");
        assertTrue(statistics.containsKey("active_desks"), "应该包含活跃小桌数");
        assertTrue(statistics.containsKey("avg_members"), "应该包含平均成员数");
    }

    @Test
    @DisplayName("测试统计用户创建的小桌数量")
    void testCountByOwnerId() {
        // 创建测试数据
        String ownerId = "test-owner";
        
        Desk desk1 = new Desk("小桌1", "描述1", ownerId);
        Desk desk2 = new Desk("小桌2", "描述2", ownerId);
        Desk desk3 = new Desk("小桌3", "描述3", "other-user");
        
        deskMapper.insert(desk1);
        deskMapper.insert(desk2);
        deskMapper.insert(desk3);

        // 统计指定用户创建的小桌数量
        Long count = deskMapper.countByOwnerId(ownerId);
        
        assertEquals(2L, count, "应该统计到2个小桌");
    }

    @Test
    @DisplayName("测试小桌实体类的业务方法")
    void testDeskEntityMethods() {
        Desk desk = new Desk("测试小桌", "测试描述", "test-user");
        desk.setMaxMembers(5);
        desk.setCurrentMembers(5);

        // 测试是否已满员
        assertTrue(desk.isFull(), "当前成员数等于最大成员数时应该返回true");

        desk.setCurrentMembers(3);
        assertFalse(desk.isFull(), "当前成员数小于最大成员数时应该返回false");

        // 测试是否活跃
        assertTrue(desk.isActive(), "默认状态应该是活跃的");

        desk.setStatus("dissolved");
        assertTrue(desk.isDissolved(), "状态为dissolved时应该返回true");

        // 测试是否为桌长
        assertTrue(desk.isOwner("test-user"), "桌长ID匹配时应该返回true");
        assertFalse(desk.isOwner("other-user"), "桌长ID不匹配时应该返回false");
    }
}
