package com.wqh.publicexaminationassistant.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件上传配置验证测试
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@SpringBootTest
@ActiveProfiles({"dev", "file-upload"})
class FileUploadConfigurationTest {

    @Autowired
    private FileUploadProperties fileUploadProperties;

    @Test
    @DisplayName("验证文件上传配置属性是否正确加载")
    void testFileUploadPropertiesLoaded() {
        // 验证配置属性是否正确加载
        assertNotNull(fileUploadProperties, "FileUploadProperties应该被正确注入");
        
        // 验证基础配置
        assertEquals(10485760L, fileUploadProperties.getMaxSize(), "文件最大大小应该是10MB");
        assertEquals("/uploads", fileUploadProperties.getPath(), "上传路径应该是/uploads");
        assertNotNull(fileUploadProperties.getAllowedTypes(), "允许的文件类型不应该为空");
        assertTrue(fileUploadProperties.getAllowedTypes().contains("jpg"), "应该允许jpg格式");
        assertTrue(fileUploadProperties.getAllowedTypes().contains("png"), "应该允许png格式");
        
        System.out.println("✓ 基础配置验证通过");
    }

    @Test
    void testImageConfiguration() {
        // 验证图片配置
        FileUploadProperties.ImageConfig imageConfig = fileUploadProperties.getImage();
        assertNotNull(imageConfig, "图片配置不应该为空");
        assertEquals(1920, imageConfig.getMaxWidth(), "图片最大宽度应该是1920");
        assertEquals(1080, imageConfig.getMaxHeight(), "图片最大高度应该是1080");
        assertEquals(85, imageConfig.getQuality(), "图片质量应该是85");
        
        System.out.println("✓ 图片配置验证通过");
    }

    @Test
    void testThumbnailConfiguration() {
        // 验证缩略图配置
        FileUploadProperties.ThumbnailConfig thumbnailConfig = fileUploadProperties.getThumbnail();
        assertNotNull(thumbnailConfig, "缩略图配置不应该为空");
        assertEquals(200, thumbnailConfig.getWidth(), "缩略图宽度应该是200");
        assertEquals(200, thumbnailConfig.getHeight(), "缩略图高度应该是200");
        assertEquals(80, thumbnailConfig.getQuality(), "缩略图质量应该是80");
        
        System.out.println("✓ 缩略图配置验证通过");
    }

    @Test
    void testStorageConfiguration() {
        // 验证存储配置
        FileUploadProperties.StorageConfig storageConfig = fileUploadProperties.getStorage();
        assertNotNull(storageConfig, "存储配置不应该为空");
        assertEquals("local", storageConfig.getType(), "存储类型应该是local");
        assertEquals("/api/files", storageConfig.getBaseUrl(), "基础URL应该是/api/files");
        
        System.out.println("✓ 存储配置验证通过");
    }

    @Test
    void testFileValidationMethods() {
        // 测试文件验证方法
        assertTrue(fileUploadProperties.isAllowedType("test.jpg"), "应该允许jpg文件");
        assertTrue(fileUploadProperties.isAllowedType("test.PNG"), "应该允许PNG文件（大小写不敏感）");
        assertFalse(fileUploadProperties.isAllowedType("test.txt"), "不应该允许txt文件");
        assertFalse(fileUploadProperties.isAllowedType(null), "null文件类型应该返回false");
        
        assertTrue(fileUploadProperties.isAllowedMimeType("image/jpeg"), "应该允许image/jpeg");
        assertTrue(fileUploadProperties.isAllowedMimeType("image/png"), "应该允许image/png");
        assertFalse(fileUploadProperties.isAllowedMimeType("text/plain"), "不应该允许text/plain");
        assertFalse(fileUploadProperties.isAllowedMimeType(null), "null MIME类型应该返回false");
        
        assertTrue(fileUploadProperties.isAllowedSize(1024L), "1KB文件应该被允许");
        assertTrue(fileUploadProperties.isAllowedSize(10485760L), "10MB文件应该被允许");
        assertFalse(fileUploadProperties.isAllowedSize(10485761L), "超过10MB的文件不应该被允许");
        assertFalse(fileUploadProperties.isAllowedSize(0L), "0字节文件不应该被允许");
        
        System.out.println("✓ 文件验证方法测试通过");
    }

    @Test
    void testUploadDirectoryStructure() {
        // 验证上传目录结构
        String projectRoot = System.getProperty("user.dir");
        Path uploadsPath = Paths.get(projectRoot, "uploads");
        
        assertTrue(Files.exists(uploadsPath), "uploads目录应该存在");
        assertTrue(Files.isDirectory(uploadsPath), "uploads应该是一个目录");
        
        // 验证子目录
        String[] subDirs = {"wrong-questions", "temp"};
        for (String subDir : subDirs) {
            Path subDirPath = uploadsPath.resolve(subDir);
            assertTrue(Files.exists(subDirPath), subDir + "目录应该存在");
            assertTrue(Files.isDirectory(subDirPath), subDir + "应该是一个目录");
        }
        
        // 验证错题图片子目录
        Path wrongQuestionsPath = uploadsPath.resolve("wrong-questions");
        String[] imageTypes = {"question", "answer", "explanation", "thumbnails"};
        for (String imageType : imageTypes) {
            Path imageTypePath = wrongQuestionsPath.resolve(imageType);
            assertTrue(Files.exists(imageTypePath), imageType + "目录应该存在");
            assertTrue(Files.isDirectory(imageTypePath), imageType + "应该是一个目录");
        }
        
        System.out.println("✓ 上传目录结构验证通过");
    }

    @Test
    void testDirectoryPermissions() {
        // 验证目录权限（仅在非Windows系统上测试）
        if (!System.getProperty("os.name").toLowerCase().contains("windows")) {
            String projectRoot = System.getProperty("user.dir");
            Path uploadsPath = Paths.get(projectRoot, "uploads");
            
            assertTrue(Files.isReadable(uploadsPath), "uploads目录应该可读");
            assertTrue(Files.isWritable(uploadsPath), "uploads目录应该可写");
            
            System.out.println("✓ 目录权限验证通过");
        } else {
            System.out.println("⚠ Windows系统跳过权限验证");
        }
    }

    @Test
    void testConfigurationPrint() {
        // 打印完整配置信息用于调试
        System.out.println("\n==================== 文件上传配置信息 ====================");
        System.out.println("最大文件大小: " + fileUploadProperties.getMaxSize() + " 字节 (" + 
                          (fileUploadProperties.getMaxSize() / 1024 / 1024) + " MB)");
        System.out.println("允许的文件类型: " + String.join(", ", fileUploadProperties.getAllowedTypes()));
        System.out.println("上传路径: " + fileUploadProperties.getPath());
        System.out.println("图片最大尺寸: " + fileUploadProperties.getImage().getMaxWidth() + "x" + 
                          fileUploadProperties.getImage().getMaxHeight());
        System.out.println("图片质量: " + fileUploadProperties.getImage().getQuality());
        System.out.println("缩略图尺寸: " + fileUploadProperties.getThumbnail().getWidth() + "x" + 
                          fileUploadProperties.getThumbnail().getHeight());
        System.out.println("存储类型: " + fileUploadProperties.getStorage().getType());
        System.out.println("基础URL: " + fileUploadProperties.getStorage().getBaseUrl());
        System.out.println("========================================================\n");
    }
}
