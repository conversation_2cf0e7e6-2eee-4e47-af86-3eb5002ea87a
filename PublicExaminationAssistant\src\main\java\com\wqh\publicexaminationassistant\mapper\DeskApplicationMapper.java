package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.entity.DeskApplication;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 小桌申请表Mapper接口
 * 提供小桌申请数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface DeskApplicationMapper extends BaseMapper<DeskApplication> {

    /**
     * 查询小桌的申请列表（包含申请者信息）
     */
    @Select("SELECT da.*, u.username, u.nickname, u.reputation_score, u.reputation_level " +
            "FROM desk_applications da " +
            "LEFT JOIN users u ON da.user_id = u.id " +
            "WHERE da.desk_id = #{deskId} " +
            "ORDER BY da.applied_at DESC")
    List<Map<String, Object>> findApplicationsByDeskId(@Param("deskId") String deskId);

    /**
     * 查询小桌的待处理申请列表
     */
    @Select("SELECT da.*, u.username, u.nickname, u.reputation_score, u.reputation_level " +
            "FROM desk_applications da " +
            "LEFT JOIN users u ON da.user_id = u.id " +
            "WHERE da.desk_id = #{deskId} AND da.status = 'pending' " +
            "ORDER BY da.applied_at ASC")
    List<Map<String, Object>> findPendingApplicationsByDeskId(@Param("deskId") String deskId);

    /**
     * 查询用户的申请历史（包含小桌信息）
     */
    @Select("SELECT da.*, d.name as desk_name, d.description, d.owner_id, d.current_members, d.max_members " +
            "FROM desk_applications da " +
            "LEFT JOIN desks d ON da.desk_id = d.id " +
            "WHERE da.user_id = #{userId} " +
            "ORDER BY da.applied_at DESC")
    List<Map<String, Object>> findApplicationsByUserId(@Param("userId") String userId);

    /**
     * 查询用户的待处理申请
     */
    @Select("SELECT da.*, d.name as desk_name " +
            "FROM desk_applications da " +
            "LEFT JOIN desks d ON da.desk_id = d.id " +
            "WHERE da.user_id = #{userId} AND da.status = 'pending' " +
            "ORDER BY da.applied_at DESC")
    List<Map<String, Object>> findPendingApplicationsByUserId(@Param("userId") String userId);

    /**
     * 检查用户是否已申请指定小桌
     */
    @Select("SELECT COUNT(*) FROM desk_applications WHERE desk_id = #{deskId} AND user_id = #{userId} AND status = 'pending'")
    int countPendingByDeskIdAndUserId(@Param("deskId") String deskId, @Param("userId") String userId);

    /**
     * 统计小桌的待处理申请数
     */
    @Select("SELECT COUNT(*) FROM desk_applications WHERE desk_id = #{deskId} AND status = 'pending'")
    int countPendingByDeskId(@Param("deskId") String deskId);

    /**
     * 统计用户的申请总数
     */
    @Select("SELECT COUNT(*) FROM desk_applications WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") String userId);

    /**
     * 统计用户的待处理申请数
     */
    @Select("SELECT COUNT(*) FROM desk_applications WHERE user_id = #{userId} AND status = 'pending'")
    Long countPendingByUserId(@Param("userId") String userId);

    /**
     * 查询申请详情（包含小桌和申请者信息）
     */
    @Select("SELECT da.*, d.name as desk_name, d.description, d.owner_id, d.current_members, d.max_members, " +
            "u.username, u.nickname, u.reputation_score, u.reputation_level " +
            "FROM desk_applications da " +
            "LEFT JOIN desks d ON da.desk_id = d.id " +
            "LEFT JOIN users u ON da.user_id = u.id " +
            "WHERE da.id = #{applicationId}")
    Map<String, Object> findApplicationDetailById(@Param("applicationId") String applicationId);

    /**
     * 批量更新申请状态
     */
    @Update("UPDATE desk_applications SET status = #{status}, processed_at = NOW(), processed_by = #{processedBy} " +
            "WHERE id IN (${applicationIds}) AND status = 'pending'")
    int batchUpdateStatus(@Param("applicationIds") String applicationIds,
                         @Param("status") String status,
                         @Param("processedBy") String processedBy);

    /**
     * 查询最近的申请（分页）
     */
    @Select("SELECT da.*, d.name as desk_name, u.username, u.nickname " +
            "FROM desk_applications da " +
            "LEFT JOIN desks d ON da.desk_id = d.id " +
            "LEFT JOIN users u ON da.user_id = u.id " +
            "ORDER BY da.applied_at DESC")
    Page<Map<String, Object>> findRecentApplications(Page<Map<String, Object>> page);
}
