package com.wqh.publicexaminationassistant.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.CreateStudyRecordRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateMasteryStatusRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateStudyRecordRequest;
import com.wqh.publicexaminationassistant.dto.response.StudyRecordResponse;
import com.wqh.publicexaminationassistant.dto.response.StudyStatisticsResponse;
import com.wqh.publicexaminationassistant.dto.response.WrongQuestionResponse;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.StudyRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;

/**
 * 刷题记录控制器
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Slf4j
@RestController
@RequestMapping("/v1/study-records")
@RequiredArgsConstructor
@Validated
@Tag(name = "刷题记录管理", description = "刷题记录的创建、查询、统计等功能")
public class StudyRecordController extends BaseController {

    private final StudyRecordService studyRecordService;

    @PostMapping
    @Operation(summary = "创建刷题记录", description = "用户手动输入刷题数据，系统自动计算正确率")
    public ApiResponse<StudyRecordResponse> createRecord(
            @Valid @RequestBody CreateStudyRecordRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户创建刷题记录: userId={}, moduleType={}", 
                userDetails.getId(), request.getModuleType());
        
        StudyRecordResponse response = studyRecordService.createRecord(request, userDetails.getId());
        return ApiResponse.success(response, "刷题记录创建成功");
    }

    @GetMapping
    @Operation(summary = "获取刷题记录列表", description = "分页查询用户的刷题记录，支持按模块类型和日期范围筛选")
    public ApiResponse<Page<StudyRecordResponse>> getRecords(
            @Parameter(description = "页码，从1开始") 
            @RequestParam(defaultValue = "1") @Min(1) int page,
            
            @Parameter(description = "每页大小，最大100") 
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,
            
            @Parameter(description = "模块类型筛选") 
            @RequestParam(required = false) 
            @Pattern(regexp = "^(math|logic|language|knowledge|essay)$", 
                     message = "模块类型必须是: math, logic, language, knowledge, essay") 
            String moduleType,
            
            @Parameter(description = "开始日期") 
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") 
            LocalDate startDate,
            
            @Parameter(description = "结束日期") 
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") 
            LocalDate endDate,
            
            @Parameter(description = "排序字段") 
            @RequestParam(defaultValue = "studyDate") 
            @Pattern(regexp = "^(studyDate|accuracyRate|questionCount|studyDuration)$", 
                     message = "排序字段必须是: studyDate, accuracyRate, questionCount, studyDuration") 
            String sortBy,
            
            @Parameter(description = "排序方向") 
            @RequestParam(defaultValue = "desc") 
            @Pattern(regexp = "^(asc|desc)$", message = "排序方向必须是: asc, desc") 
            String sortOrder,
            
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询刷题记录: userId={}, page={}, size={}", 
                userDetails.getId(), page, size);
        
        Page<StudyRecordResponse> result = studyRecordService.getRecords(
                userDetails.getId(), page, size, moduleType, startDate, endDate, sortBy, sortOrder);
        
        return ApiResponse.success(result, "获取刷题记录成功");
    }

    @GetMapping("/{recordId}")
    @Operation(summary = "获取刷题记录详情", description = "根据记录ID获取详细信息")
    public ApiResponse<StudyRecordResponse> getRecordDetail(
            @Parameter(description = "记录ID") 
            @PathVariable String recordId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询刷题记录详情: userId={}, recordId={}", 
                userDetails.getId(), recordId);
        
        StudyRecordResponse response = studyRecordService.getRecordDetail(recordId, userDetails.getId());
        return ApiResponse.success(response, "获取记录详情成功");
    }

    @PutMapping("/{recordId}")
    @Operation(summary = "更新刷题记录", description = "只能更新学习笔记和薄弱知识点")
    public ApiResponse<Void> updateRecord(
            @Parameter(description = "记录ID") 
            @PathVariable String recordId,
            @Valid @RequestBody UpdateStudyRecordRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户更新刷题记录: userId={}, recordId={}", 
                userDetails.getId(), recordId);
        
        studyRecordService.updateRecord(recordId, request, userDetails.getId());
        return ApiResponse.success(null, "记录更新成功");
    }

    @DeleteMapping("/{recordId}")
    @Operation(summary = "删除刷题记录", description = "删除指定的刷题记录及其关联的错题")
    public ApiResponse<Void> deleteRecord(
            @Parameter(description = "记录ID") 
            @PathVariable String recordId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户删除刷题记录: userId={}, recordId={}", 
                userDetails.getId(), recordId);
        
        studyRecordService.deleteRecord(recordId, userDetails.getId());
        return ApiResponse.success(null, "记录删除成功");
    }

    @GetMapping("/stats/overview")
    @Operation(summary = "获取学习统计概览", description = "获取用户的整体学习统计数据")
    public ApiResponse<StudyStatisticsResponse> getStatisticsOverview(
            @Parameter(description = "统计周期") 
            @RequestParam(defaultValue = "week") 
            @Pattern(regexp = "^(week|month|year)$", message = "统计周期必须是: week, month, year") 
            String period,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询学习统计概览: userId={}, period={}", 
                userDetails.getId(), period);
        
        StudyStatisticsResponse response = studyRecordService.getStatisticsOverview(userDetails.getId(), period);
        return ApiResponse.success(response, "获取学习统计成功");
    }

    @GetMapping("/stats/modules")
    @Operation(summary = "获取模块统计", description = "按模块类型统计学习数据")
    public ApiResponse<StudyStatisticsResponse> getModuleStatistics(
            @Parameter(description = "开始日期") 
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") 
            LocalDate startDate,
            
            @Parameter(description = "结束日期") 
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") 
            LocalDate endDate,
            
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询模块统计: userId={}, startDate={}, endDate={}", 
                userDetails.getId(), startDate, endDate);
        
        StudyStatisticsResponse response = studyRecordService.getModuleStatistics(
                userDetails.getId(), startDate, endDate);
        return ApiResponse.success(response, "获取模块统计成功");
    }

    @GetMapping("/stats/trends")
    @Operation(summary = "获取学习趋势", description = "获取指定时间段的学习趋势数据")
    public ApiResponse<StudyStatisticsResponse> getStudyTrends(
            @Parameter(description = "统计周期") 
            @RequestParam(defaultValue = "week") 
            @Pattern(regexp = "^(week|month)$", message = "统计周期必须是: week, month") 
            String period,
            
            @Parameter(description = "数据粒度") 
            @RequestParam(defaultValue = "day") 
            @Pattern(regexp = "^(day|week)$", message = "数据粒度必须是: day, week") 
            String granularity,
            
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询学习趋势: userId={}, period={}, granularity={}", 
                userDetails.getId(), period, granularity);
        
        StudyStatisticsResponse response = studyRecordService.getStudyTrends(
                userDetails.getId(), period, granularity);
        return ApiResponse.success(response, "获取学习趋势成功");
    }

    @GetMapping("/calendar")
    @Operation(summary = "获取学习日历", description = "获取指定年月的学习日历数据")
    public ApiResponse<StudyStatisticsResponse> getStudyCalendar(
            @Parameter(description = "年份") 
            @RequestParam @Min(2020) @Max(2030) int year,
            
            @Parameter(description = "月份") 
            @RequestParam @Min(1) @Max(12) int month,
            
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询学习日历: userId={}, year={}, month={}", 
                userDetails.getId(), year, month);
        
        StudyStatisticsResponse response = studyRecordService.getStudyCalendar(
                userDetails.getId(), year, month);
        return ApiResponse.success(response, "获取学习日历成功");
    }

    @GetMapping("/wrong-questions")
    @Operation(summary = "获取错题列表", description = "分页查询用户的错题，支持按题目类型、学习模块和掌握状态筛选")
    public ApiResponse<Page<WrongQuestionResponse>> getWrongQuestions(
            @Parameter(description = "页码，从1开始")
            @RequestParam(defaultValue = "1") @Min(1) int page,

            @Parameter(description = "每页大小，最大100")
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,

            @Parameter(description = "题目类型筛选")
            @RequestParam(required = false)
            @Pattern(regexp = "^(single_choice|multiple_choice|judgment|fill_blank|essay)$",
                     message = "题目类型必须是: single_choice, multiple_choice, judgment, fill_blank, essay")
            String questionType,

            @Parameter(description = "学习模块筛选")
            @RequestParam(required = false)
            @Pattern(regexp = "^(math|logic|language|knowledge|essay)$",
                     message = "学习模块必须是: math, logic, language, knowledge, essay")
            String moduleType,

            @Parameter(description = "掌握状态筛选")
            @RequestParam(required = false)
            @Pattern(regexp = "^(not_mastered|reviewing|mastered)$",
                     message = "掌握状态必须是: not_mastered, reviewing, mastered")
            String masteryStatus,

            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户查询错题列表: userId={}, page={}, size={}, questionType={}, moduleType={}, masteryStatus={}",
                userDetails.getId(), page, size, questionType, moduleType, masteryStatus);

        Page<WrongQuestionResponse> result = studyRecordService.getWrongQuestions(
                userDetails.getId(), page, size, questionType, moduleType, masteryStatus);

        return ApiResponse.success(result, "获取错题列表成功");
    }

    @PutMapping("/wrong-questions/{wrongQuestionId}/mastery")
    @Operation(summary = "更新错题掌握状态", description = "更新指定错题的掌握状态并增加复习次数")
    public ApiResponse<Void> updateMasteryStatus(
            @Parameter(description = "错题ID")
            @PathVariable String wrongQuestionId,
            @Valid @RequestBody UpdateMasteryStatusRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户更新错题掌握状态: userId={}, wrongQuestionId={}, masteryStatus={}",
                userDetails.getId(), wrongQuestionId, request.getMasteryStatus());

        studyRecordService.updateMasteryStatus(wrongQuestionId, request, userDetails.getId());
        return ApiResponse.success(null, "错题状态更新成功");
    }
}
