import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { authService } from '../services/authService'
import type { User, LoginRequest, RegisterRequest } from '../types/auth'

interface AuthState {
  // 状态
  isAuthenticated: boolean
  user: User | null
  token: string | null
  refreshToken: string | null
  isLoading: boolean
  error: string | null

  // 操作方法
  login: (credentials: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => void
  setTokens: (token: string, refreshToken: string) => void
  updateUser: (user: User) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  refreshUserProfile: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      isLoading: false,
      error: null,

      // 登录方法
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authService.login(credentials);
          set({
            isAuthenticated: true,
            token: response.token,
            refreshToken: response.refreshToken,
            user: response.userInfo,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '登录失败'
          });
          throw error;
        }
      },

      // 注册方法
      register: async (data: RegisterRequest) => {
        set({ isLoading: true, error: null });
        try {
          await authService.register(data);
          set({ isLoading: false, error: null });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.message || '注册失败'
          });
          throw error;
        }
      },

      // 登出方法
      logout: () => {
        try {
          authService.logout();
        } catch (error) {
          console.error('登出接口调用失败:', error);
        }
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          refreshToken: null,
          error: null
        });
      },

      // 设置Token
      setTokens: (token: string, refreshToken: string) => set({
        token,
        refreshToken,
        isAuthenticated: true
      }),

      // 更新用户信息
      updateUser: (user: User) => set({ user }),

      // 清除错误
      clearError: () => set({ error: null }),

      // 设置加载状态
      setLoading: (loading: boolean) => set({ isLoading: loading }),

      // 刷新用户资料
      refreshUserProfile: async () => {
        try {
          const user = await authService.getUserProfile();
          set({ user });
        } catch (error: any) {
          console.error('刷新用户资料失败:', error);
          set({ error: error.message || '获取用户信息失败' });
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken
      })
    }
  )
)