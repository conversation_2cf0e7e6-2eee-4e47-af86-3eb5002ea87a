import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Typography, Space, Toast } from '@douyinfe/semi-ui';
import { authService } from '../services/authService';

const { Title, Text, Paragraph } = Typography;

export const ApiTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testApiConnection = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      // 测试基础连接
      addResult('🔍 开始测试API连接...');
      
      // 测试邀请码验证接口（这个接口不需要认证）
      try {
        await authService.validateInviteCode('TEST1234');
        addResult('✅ 邀请码验证接口连接成功');
      } catch (error: any) {
        if (error.message.includes('邀请码')) {
          addResult('✅ 邀请码验证接口连接成功（返回业务错误，说明接口可达）');
        } else {
          addResult(`❌ 邀请码验证接口错误: ${error.message}`);
        }
      }

      // 测试登录接口
      try {
        await authService.login({ username: 'test', password: 'test' });
        addResult('✅ 登录接口连接成功');
      } catch (error: any) {
        if (error.message.includes('用户名') || error.message.includes('密码') || error.message.includes('不存在')) {
          addResult('✅ 登录接口连接成功（返回业务错误，说明接口可达）');
        } else {
          addResult(`❌ 登录接口错误: ${error.message}`);
        }
      }

      addResult('🎉 API连接测试完成');
      Toast.success('API连接测试完成，请查看结果');
      
    } catch (error: any) {
      addResult(`❌ 测试过程中发生错误: ${error.message}`);
      Toast.error('API连接测试失败');
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ 
      padding: '40px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'var(--font-system)'
    }}>
      <Card style={{ marginBottom: '20px' }}>
        <Title level={2} style={{ marginBottom: '16px' }}>
          🔧 API连接测试工具
        </Title>
        <Paragraph>
          这个页面用于测试前端与后端API的连接状态。请确保后端服务已启动在 http://localhost:8080
        </Paragraph>
        
        <Space>
          <Button 
            type="primary" 
            onClick={testApiConnection}
            loading={isLoading}
            disabled={isLoading}
          >
            {isLoading ? '测试中...' : '开始测试API连接'}
          </Button>
          <Button onClick={clearResults} disabled={isLoading}>
            清除结果
          </Button>
        </Space>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <Title level={3} style={{ marginBottom: '16px' }}>
            📋 测试结果
          </Title>
          <div style={{ 
            backgroundColor: '#f8f9fa',
            padding: '16px',
            borderRadius: '8px',
            fontFamily: 'monospace',
            fontSize: '14px',
            maxHeight: '400px',
            overflowY: 'auto'
          }}>
            {testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '8px' }}>
                {result}
              </div>
            ))}
          </div>
        </Card>
      )}

      <Card style={{ marginTop: '20px' }}>
        <Title level={3} style={{ marginBottom: '16px' }}>
          📊 配置信息
        </Title>
        <Space vertical style={{ width: '100%' }}>
          <Text>
            <strong>前端地址:</strong> http://localhost:5173
          </Text>
          <Text>
            <strong>API基础URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api'}
          </Text>
          <Text>
            <strong>环境:</strong> {import.meta.env.MODE}
          </Text>
        </Space>
      </Card>
    </div>
  );
};

export default ApiTest;
