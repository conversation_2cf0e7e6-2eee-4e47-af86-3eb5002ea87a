package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.entity.ScheduledTaskLog;
import com.wqh.publicexaminationassistant.mapper.ScheduledTaskLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 排行榜计算定时任务
 * 负责定期计算和更新各种排行榜数据
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RankingCalculationTask {

    private final RankingService rankingService;
    private final ScheduledTaskLogMapper scheduledTaskLogMapper;

    // 排行榜类型
    private static final String[] RANKING_TYPES = {
        "study_questions", "study_accuracy", "study_time", "comprehensive"
    };

    // 时间周期
    private static final String[] RANKING_PERIODS = {
        "daily", "weekly", "monthly", "all_time"
    };

    /**
     * 应用启动完成后执行一次排行榜计算
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始执行初始排行榜计算");
        
        // 异步执行，避免阻塞应用启动
        CompletableFuture.runAsync(() -> {
            try {
                // 等待3秒，确保应用完全启动
                Thread.sleep(3000);
                calculateAllRankings("应用启动初始化");
            } catch (Exception e) {
                log.error("应用启动时排行榜计算失败", e);
            }
        });
    }

    /**
     * 每日凌晨2点执行排行榜计算任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void calculateDailyRankings() {
        LocalDate today = LocalDate.now();
        LocalDateTime startTime = LocalDateTime.now();
        String taskName = ScheduledTaskLog.TaskName.RANKING_CALCULATION;
        
        // 检查今日是否已执行成功
        if (scheduledTaskLogMapper.isTaskCompletedToday(taskName, today)) {
            log.info("排行榜计算任务今日已执行成功，跳过执行");
            return;
        }

        // 创建任务执行记录
        ScheduledTaskLog taskLog = new ScheduledTaskLog();
        taskLog.setTaskName(taskName);
        taskLog.setExecutionDate(today);
        taskLog.setStartTime(startTime);
        taskLog.setStatus(ScheduledTaskLog.Status.RUNNING);
        taskLog.setProcessedCount(0);
        scheduledTaskLogMapper.insert(taskLog);
        
        log.info("开始执行每日排行榜计算任务");
        
        try {
            int totalUpdated = calculateAllRankings("定时任务");
            
            // 更新任务状态为完成
            updateTaskCompleted(taskLog.getId(), startTime, totalUpdated);
            
            log.info("每日排行榜计算任务完成，总计更新记录数: {}", totalUpdated);
            
        } catch (Exception e) {
            log.error("每日排行榜计算任务执行失败", e);
            updateTaskFailed(taskLog.getId(), startTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 计算所有排行榜
     */
    private int calculateAllRankings(String trigger) {
        log.info("开始计算所有排行榜，触发方式: {}", trigger);
        
        int totalUpdated = 0;
        int successCount = 0;
        int failCount = 0;
        
        for (String type : RANKING_TYPES) {
            for (String period : RANKING_PERIODS) {
                try {
                    log.debug("计算排行榜: type={}, period={}", type, period);
                    int updated = rankingService.calculateAndUpdateRanking(type, period);
                    totalUpdated += updated;
                    successCount++;
                    
                    if (updated > 0) {
                        log.info("排行榜计算成功: type={}, period={}, records={}", type, period, updated);
                    } else {
                        log.debug("排行榜无数据: type={}, period={}", type, period);
                    }
                    
                    // 避免数据库压力，每次计算后稍作延迟
                    Thread.sleep(100);
                    
                } catch (Exception e) {
                    failCount++;
                    log.error("排行榜计算失败: type={}, period={}", type, period, e);
                    // 继续计算其他排行榜，不中断整个任务
                }
            }
        }
        
        log.info("排行榜计算完成统计: 成功={}, 失败={}, 总更新记录数={}",
                successCount, failCount, totalUpdated);

        // 清除所有排行榜缓存
        if (totalUpdated > 0) {
            try {
                rankingService.clearAllRankingCache();
                log.info("排行榜缓存清除完成");
            } catch (Exception e) {
                log.error("清除排行榜缓存失败", e);
            }
        }

        return totalUpdated;
    }

    /**
     * 手动触发排行榜计算（用于测试和紧急更新）
     */
    public int manualCalculateRankings() {
        log.info("手动触发排行榜计算");
        return calculateAllRankings("手动触发");
    }

    /**
     * 计算指定类型的排行榜
     */
    public int calculateSpecificRanking(String type, String period) {
        log.info("计算指定排行榜: type={}, period={}", type, period);
        
        try {
            int updated = rankingService.calculateAndUpdateRanking(type, period);
            log.info("指定排行榜计算完成: type={}, period={}, records={}", type, period, updated);
            return updated;
        } catch (Exception e) {
            log.error("指定排行榜计算失败: type={}, period={}", type, period, e);
            throw e;
        }
    }

    /**
     * 清理过期的排行榜数据
     */
    @Scheduled(cron = "0 30 3 * * ?") // 每日凌晨3:30执行
    public void cleanupOldRankings() {
        log.info("开始清理过期排行榜数据");
        
        try {
            // 清理30天前的排行榜数据
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            
            for (String type : RANKING_TYPES) {
                try {
                    int deleted = rankingService.deleteOldRankings(cutoffTime, type);
                    if (deleted > 0) {
                        log.info("清理过期排行榜数据: type={}, deleted={}", type, deleted);
                    }
                } catch (Exception e) {
                    log.error("清理排行榜数据失败: type={}", type, e);
                }
            }
            
            log.info("排行榜数据清理任务完成");
            
        } catch (Exception e) {
            log.error("排行榜数据清理任务失败", e);
        }
    }

    /**
     * 更新任务状态为完成
     */
    private void updateTaskCompleted(String taskId, LocalDateTime startTime, int processedCount) {
        try {
            ScheduledTaskLog updateLog = new ScheduledTaskLog();
            updateLog.setId(taskId);
            updateLog.setStatus(ScheduledTaskLog.Status.COMPLETED);
            updateLog.setEndTime(LocalDateTime.now());
            updateLog.setProcessedCount(processedCount);
            // 注意：执行时长通过startTime和endTime自动计算，不需要手动设置
            scheduledTaskLogMapper.updateById(updateLog);
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新任务状态为失败
     */
    private void updateTaskFailed(String taskId, LocalDateTime startTime, String errorMessage) {
        try {
            ScheduledTaskLog updateLog = new ScheduledTaskLog();
            updateLog.setId(taskId);
            updateLog.setStatus(ScheduledTaskLog.Status.FAILED);
            updateLog.setEndTime(LocalDateTime.now());
            updateLog.setErrorMessage(errorMessage);
            // 注意：执行时长通过startTime和endTime自动计算，不需要手动设置
            scheduledTaskLogMapper.updateById(updateLog);
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 获取排行榜计算统计信息
     */
    public String getRankingCalculationStats() {
        int totalCombinations = RANKING_TYPES.length * RANKING_PERIODS.length;
        return String.format("排行榜类型: %d, 时间周期: %d, 总组合数: %d", 
                RANKING_TYPES.length, RANKING_PERIODS.length, totalCombinations);
    }
}
