package com.wqh.publicexaminationassistant.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传配置属性
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.upload")
public class FileUploadProperties {

    /**
     * 文件上传最大大小(字节)
     */
    private long maxSize = 10485760L; // 10MB

    /**
     * 允许上传的文件类型
     */
    private List<String> allowedTypes = Arrays.asList("jpg", "jpeg", "png", "gif", "webp");

    /**
     * 文件上传根路径
     */
    private String path = "uploads";

    /**
     * 图片处理配置
     */
    private ImageConfig image = new ImageConfig();

    /**
     * 缩略图配置
     */
    private ThumbnailConfig thumbnail = new ThumbnailConfig();

    /**
     * 存储配置
     */
    private StorageConfig storage = new StorageConfig();

    @Data
    public static class ImageConfig {
        /**
         * 图片最大宽度(像素)
         */
        private int maxWidth = 1920;

        /**
         * 图片最大高度(像素)
         */
        private int maxHeight = 1080;

        /**
         * 图片压缩质量(1-100)
         */
        private int quality = 85;
    }

    @Data
    public static class ThumbnailConfig {
        /**
         * 缩略图宽度(像素)
         */
        private int width = 200;

        /**
         * 缩略图高度(像素)
         */
        private int height = 200;

        /**
         * 缩略图质量(1-100)
         */
        private int quality = 80;
    }

    @Data
    public static class StorageConfig {
        /**
         * 存储类型: local|oss|s3
         */
        private String type = "local";

        /**
         * 文件访问基础URL
         */
        private String baseUrl = "/api/files";
    }

    /**
     * 检查文件类型是否允许
     */
    public boolean isAllowedType(String fileType) {
        if (fileType == null) {
            return false;
        }
        String lowerType = fileType.toLowerCase();
        return allowedTypes.stream().anyMatch(lowerType::endsWith);
    }

    /**
     * 检查MIME类型是否允许
     */
    public boolean isAllowedMimeType(String mimeType) {
        if (mimeType == null) {
            return false;
        }
        return mimeType.startsWith("image/") && 
               (mimeType.equals("image/jpeg") || 
                mimeType.equals("image/jpg") || 
                mimeType.equals("image/png") || 
                mimeType.equals("image/gif") || 
                mimeType.equals("image/webp"));
    }

    /**
     * 检查文件大小是否允许
     */
    public boolean isAllowedSize(long fileSize) {
        return fileSize > 0 && fileSize <= maxSize;
    }
}
