package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 更新错题掌握状态请求DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class UpdateMasteryStatusRequest {

    /**
     * 掌握状态
     */
    @NotBlank(message = "掌握状态不能为空")
    @Pattern(regexp = "^(not_mastered|reviewing|mastered)$", 
             message = "掌握状态必须是: not_mastered, reviewing, mastered")
    private String masteryStatus;
}
