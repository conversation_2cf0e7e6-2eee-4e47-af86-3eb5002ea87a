import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Form, 
  Card, 
  Button, 
  Select, 
  InputNumber, 
  DatePicker, 
  TextArea,
  Toast,
  Space,
  Typography,
  Divider
} from '@douyinfe/semi-ui';
import { IconArrowLeft, IconSave, IconRefresh } from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';
import { studyService, type CreateStudyRecordRequest } from '../services/studyService';
import '../styles/create-study-record.css';

const { Title, Text } = Typography;

// 辅助函数：将Date对象转换为本地日期字符串 (YYYY-MM-DD)
const formatDateToLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 创建刷题记录页面
 */
const CreateStudyRecord: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formApi, setFormApi] = useState<any>(null);

  // 模块类型选项
  const moduleOptions = [
    { value: 'math', label: '📊 数学运算' },
    { value: 'logic', label: '🧠 逻辑推理' },
    { value: 'language', label: '📝 言语理解' },
    { value: 'knowledge', label: '📚 常识判断' },
    { value: 'essay', label: '✍️ 申论' },
  ];

  // 简化的验证函数（仅用于显示，实际验证在提交时进行）
  const validateCorrectCount = (rule: any, value: number, callback: any) => {
    // 简化验证，主要验证在提交时进行
    callback();
  };

  const validateStudyDate = (rule: any, value: Date, callback: any) => {
    // 简化验证，主要验证在提交时进行
    callback();
  };

  // 处理表单提交
  const handleSubmit = async (values: any, errors: any) => {
    setLoading(true);

    try {

      // 转换数据格式
      let studyDateStr: string;
      if (values.studyDate instanceof Date) {
        studyDateStr = formatDateToLocal(values.studyDate);
      } else if (typeof values.studyDate === 'string') {
        // 如果是ISO字符串，提取日期部分
        studyDateStr = values.studyDate.split('T')[0];
      } else {
        // 默认使用今天的日期
        studyDateStr = formatDateToLocal(new Date());
      }

      // Handle optional fields properly - weakPoints as string array
      const processWeakPoints = (weakPoints: string): string[] | null => {
        if (!weakPoints || weakPoints.trim() === '') {
          return null;
        }
        // Convert comma-separated string to array
        const points = weakPoints.split(',').map(point => point.trim()).filter(point => point.length > 0);
        return points.length > 0 ? points : null;
      };

      const requestData: CreateStudyRecordRequest = {
        moduleType: values.moduleType,
        questionCount: Number(values.questionCount),
        correctCount: Number(values.correctCount),
        studyDuration: Number(values.studyDuration),
        studyDate: studyDateStr,
        notes: values.notes && values.notes.trim() !== '' ? values.notes.trim() : null,
        weakPoints: processWeakPoints(values.weakPoints),
      };

      // 调用API创建记录
      const response = await studyService.createRecord(requestData);

      Toast.success({
        content: '🎉 刷题记录创建成功！',
        duration: 3,
      });

      // 跳转到记录列表页面
      setTimeout(() => {
        navigate('/study-records');
      }, 1000);

    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || error?.message || '创建失败，请重试';
      Toast.error({
        content: `❌ ${errorMessage}`,
        duration: 5,
      });
    } finally {
      setLoading(false);
    }
  };

  // 手动提交表单
  const handleManualSubmit = async () => {
    if (!formApi) {
      Toast.error('表单未初始化，请刷新页面重试');
      return;
    }

    try {
      // 获取表单值
      const values = formApi.getValues();

      // 手动验证必填字段
      const requiredFields = ['moduleType', 'questionCount', 'correctCount', 'studyDuration', 'studyDate'];
      const missingFields = requiredFields.filter(field => !values[field] && values[field] !== 0);

      if (missingFields.length > 0) {
        Toast.error({
          content: `❌ 请填写必填字段: ${missingFields.join(', ')}`,
          duration: 3,
        });
        return;
      }

      // 验证数值范围
      if (values.correctCount > values.questionCount) {
        Toast.error({
          content: '❌ 正确数量不能超过题目数量',
          duration: 3,
        });
        return;
      }

      // 验证日期
      const studyDate = new Date(values.studyDate);
      const today = new Date();
      today.setHours(23, 59, 59, 999);

      if (studyDate > today) {
        Toast.error({
          content: '❌ 学习日期不能是未来日期',
          duration: 3,
        });
        return;
      }

      // 调用提交处理函数
      await handleSubmit(values, null);
    } catch (error) {
      Toast.error({
        content: '❌ 提交过程中出现错误，请重试',
        duration: 3,
      });
    }
  };

  // 处理表单重置
  const handleReset = () => {
    formApi?.reset();
    Toast.info('表单已重置');
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <>
      <Navigation />
      <div className="create-study-record-container" style={{ paddingTop: '80px' }}>
        {/* 浮动装饰元素 */}
        <div className="floating-emoji record-emoji-1">📝</div>
        <div className="floating-emoji record-emoji-2">📊</div>
        <div className="floating-emoji record-emoji-3">🎯</div>
        <div className="floating-emoji record-emoji-4">⏰</div>

        <div className="create-record-content">
          {/* 页面标题 */}
          <div className="page-header">
            {/* <Button
              icon={<IconArrowLeft />}
              theme="borderless"
              onClick={handleGoBack}
              className="back-button"
            >
              返回
            </Button> */}
            <Title heading={2} className="page-title">
              📝 记录今日刷题
            </Title>
            <Text type="secondary" className="page-subtitle">
              记录你的学习成果，追踪进步轨迹
            </Text>
          </div>

          <Divider />

          {/* 创建表单 */}
          <Card className="create-form-card sketch-card">
            <Form
              layout="vertical"
              onSubmit={handleSubmit}
              getFormApi={(api) => setFormApi(api)}
              className="create-study-form"
              initValues={{
                studyDate: new Date()
              }}
            >
              <div className="form-section">
                <Title heading={4} className="section-title">📚 基本信息</Title>
                
                <div className="form-row" >
                  <Form.Select
                    field="moduleType"
                    label="学习模块"
                    placeholder="选择学习的模块类型"
                    optionList={moduleOptions}
                    rules={[
                      { required: true, message: '请选择学习模块' }
                    ]}
                    style={{ width: '100%',height: '50px' }}
                    className="sketch-input"
                  />
                </div>

                <div className="form-row-group">
                  <div className="form-col">
                    <Form.InputNumber
                      field="questionCount"
                      label="题目数量"
                      placeholder="输入做题数量"
                      min={1}
                      max={1000}
                      rules={[
                        { required: true, message: '请输入题目数量' },
                        { type: 'number', min: 1, message: '题目数量至少为1' },
                        { type: 'number', max: 1000, message: '题目数量不能超过1000' }
                      ]}
                      style={{ width: '100%' }}
                      className="sketch-input"
                      suffix="题"
                      showClear={true}
                    />
                  </div>
                  
                  <div className="form-col">
                    <Form.InputNumber
                      field="correctCount"
                      label="正确数量"
                      placeholder="输入正确数量"
                      min={0}
                      rules={[
                        { required: true, message: '请输入正确数量' },
                        { type: 'number', min: 0, message: '正确数量不能为负数' },
                        { validator: validateCorrectCount }
                      ]}
                      style={{ width: '100%' }}
                      className="sketch-input"
                      suffix="题"
                      showClear={true}
                    />
                  </div>
                </div>

                <div className="form-row-group">
                  <div className="form-col">
                    <Form.InputNumber
                      field="studyDuration"
                      label="学习时长"
                      placeholder="输入学习时长"
                      min={1}
                      max={1440}
                      rules={[
                        { required: true, message: '请输入学习时长' },
                        { type: 'number', min: 1, message: '学习时长至少为1分钟' },
                        { type: 'number', max: 1440, message: '学习时长不能超过1440分钟' }
                      ]}
                      style={{ width: '100%' }}
                      className="sketch-input"
                      suffix="分钟"
                      showClear={true}
                    />
                  </div>
                  
                  <div className="form-col">
                    <Form.DatePicker
                      field="studyDate"
                      label="学习日期"
                      placeholder="选择学习日期"
                      rules={[
                        { required: true, message: '请选择学习日期' },
                        { validator: validateStudyDate }
                      ]}
                      style={{ width: '100%' }}
                      className="sketch-input"
                      initValue={new Date()}
                      format="yyyy-MM-dd"
                    />
                  </div>
                </div>
              </div>

              <Divider />

              <div className="form-section">
                <Title heading={4} className="section-title">📝 学习笔记</Title>
                
                <Form.TextArea
                  field="notes"
                  label="学习笔记"
                  placeholder="记录今天的学习心得、遇到的问题或重要知识点..."
                  maxCount={1000}
                  autosize={{ minRows: 3, maxRows: 6 }}
                  showClear
                  className="sketch-textarea"
                />

                <Form.TextArea
                  field="weakPoints"
                  label="薄弱知识点"
                  placeholder="记录需要加强的知识点，用逗号分隔，如：排列组合,概率计算,逻辑推理"
                  maxCount={500}
                  autosize={{ minRows: 2, maxRows: 4 }}
                  showClear
                  className="sketch-textarea"
                />
              </div>

              {/* 提交按钮 */}
              <div className="form-actions">
                <Space size="large">
                  <Button
                    htmlType="reset"
                    icon={<IconRefresh />}
                    onClick={handleReset}
                    className="sketch-button secondary"
                    size="large"
                  >
                    重置表单
                  </Button>
                  
                  <Button
                    type="primary"
                    icon={<IconSave />}
                    loading={loading}
                    onClick={handleManualSubmit}
                    className="sketch-button primary"
                    size="large"
                  >
                    {loading ? '保存中...' : '保存记录'}
                  </Button>
                </Space>
              </div>
            </Form>
          </Card>
        </div>
      </div>
    </>
  );
};

export default CreateStudyRecord;
