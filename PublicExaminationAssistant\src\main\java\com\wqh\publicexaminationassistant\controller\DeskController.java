package com.wqh.publicexaminationassistant.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.CreateDeskRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateDeskRequest;
import com.wqh.publicexaminationassistant.dto.response.DeskResponse;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.DeskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 小桌管理控制器
 * 提供小桌创建、查询、更新、解散等RESTful API
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@RestController
@RequestMapping("/v1/desks")
@RequiredArgsConstructor
@Tag(name = "小桌管理", description = "小桌创建、查询、管理等功能")
public class DeskController {

    private final DeskService deskService;

    /**
     * 创建小桌
     */
    @PostMapping
    @Operation(summary = "创建小桌", description = "创建新的学习小桌")
    public ApiResponse<DeskResponse> createDesk(
            @Valid @RequestBody CreateDeskRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户创建小桌: userId={}, name={}", userDetails.getId(), request.getName());
        
        DeskResponse response = deskService.createDesk(request, userDetails.getId());
        return ApiResponse.success(response, "小桌创建成功");
    }

    /**
     * 获取小桌详情
     */
    @GetMapping("/{deskId}")
    @Operation(summary = "获取小桌详情", description = "根据ID获取小桌的详细信息")
    public ApiResponse<DeskResponse> getDeskById(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查看小桌详情: userId={}, deskId={}", userDetails.getId(), deskId);
        
        DeskResponse response = deskService.getDeskById(deskId, userDetails.getId());
        return ApiResponse.success(response, "获取小桌详情成功");
    }

    /**
     * 更新小桌信息
     */
    @PutMapping("/{deskId}")
    @Operation(summary = "更新小桌信息", description = "更新小桌的基本信息（仅桌长可操作）")
    public ApiResponse<DeskResponse> updateDesk(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @Valid @RequestBody UpdateDeskRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户更新小桌: userId={}, deskId={}", userDetails.getId(), deskId);
        
        DeskResponse response = deskService.updateDesk(deskId, request, userDetails.getId());
        return ApiResponse.success(response, "小桌信息更新成功");
    }

    /**
     * 解散小桌
     */
    @DeleteMapping("/{deskId}")
    @Operation(summary = "解散小桌", description = "解散小桌并停用所有成员关系（仅桌长可操作）")
    public ApiResponse<Void> dissolveDesk(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户解散小桌: userId={}, deskId={}", userDetails.getId(), deskId);
        
        deskService.dissolveDesk(deskId, userDetails.getId());
        return ApiResponse.success(null, "小桌解散成功");
    }

    /**
     * 搜索/列表小桌
     */
    @GetMapping
    @Operation(summary = "搜索小桌", description = "搜索活跃的小桌，支持关键词搜索和分页")
    public ApiResponse<Page<DeskResponse>> searchDesks(
            @Parameter(description = "搜索关键词（可选）") 
            @RequestParam(required = false) String keyword,
            @Parameter(description = "页码，默认1") 
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小，默认20") 
            @RequestParam(defaultValue = "20") int size,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户搜索小桌: userId={}, keyword={}, page={}, size={}", 
                userDetails.getId(), keyword, page, size);
        
        Page<DeskResponse> response = deskService.searchDesks(keyword, page, size, userDetails.getId());
        return ApiResponse.success(response, "搜索小桌成功");
    }

    /**
     * 获取我的小桌
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的小桌", description = "获取当前用户创建的和加入的所有小桌")
    public ApiResponse<List<DeskResponse>> getMyDesks(
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户获取我的小桌: userId={}", userDetails.getId());
        
        List<DeskResponse> response = deskService.getMyDesks(userDetails.getId());
        return ApiResponse.success(response, "获取我的小桌成功");
    }

    /**
     * 获取小桌统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取小桌统计信息", description = "获取平台小桌的统计数据")
    public ApiResponse<Map<String, Object>> getDeskStatistics() {
        
        log.info("获取小桌统计信息");
        
        Map<String, Object> response = deskService.getDeskStatistics();
        return ApiResponse.success(response, "获取统计信息成功");
    }
}
