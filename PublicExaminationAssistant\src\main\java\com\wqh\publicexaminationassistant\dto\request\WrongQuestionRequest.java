package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 错题请求DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class WrongQuestionRequest {

    /**
     * 关联的学习记录ID (可选)
     */
    @Size(max = 36, message = "学习记录ID格式不正确")
    private String studyRecordId;

    /**
     * 学习模块类型 (用于智能关联学习记录)
     */
    @Pattern(regexp = "^(math|logic|language|knowledge|essay)$",
             message = "学习模块必须是: math, logic, language, knowledge, essay")
    private String moduleType;

    /**
     * 题目类型
     */
    @NotBlank(message = "题目类型不能为空")
    @Pattern(regexp = "^(single_choice|multiple_choice|judgment|fill_blank|essay)$", 
             message = "题目类型必须是: single_choice, multiple_choice, judgment, fill_blank, essay")
    private String questionType;

    /**
     * 难度等级
     */
    @NotBlank(message = "难度等级不能为空")
    @Pattern(regexp = "^(easy|medium|hard)$", 
             message = "难度等级必须是: easy, medium, hard")
    private String difficultyLevel;

    /**
     * 题目内容
     */
    @NotBlank(message = "题目内容不能为空")
    @Size(max = 5000, message = "题目内容不能超过5000个字符")
    private String questionContent;

    /**
     * 用户答案
     */
    @Size(max = 1000, message = "用户答案不能超过1000个字符")
    private String userAnswer;

    /**
     * 正确答案
     */
    @NotBlank(message = "正确答案不能为空")
    @Size(max = 1000, message = "正确答案不能超过1000个字符")
    private String correctAnswer;

    /**
     * 解析内容
     */
    @Size(max = 5000, message = "解析内容不能超过5000个字符")
    private String explanation;

    /**
     * 掌握状态 (默认为未掌握)
     */
    @Pattern(regexp = "^(not_mastered|reviewing|mastered)$", 
             message = "掌握状态必须是: not_mastered, reviewing, mastered")
    private String masteryStatus = "not_mastered";
}
