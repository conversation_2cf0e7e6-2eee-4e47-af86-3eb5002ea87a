package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 考试公告表实体类
 * 存储考试公告信息，支持全文搜索
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("announcements")
public class Announcement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 公告标题
     */
    @TableField("title")
    private String title;

    /**
     * 公告内容
     */
    @TableField("content")
    private String content;

    /**
     * 地区
     */
    @TableField("region")
    private String region;

    /**
     * 考试类型
     */
    @TableField("exam_type")
    private String examType;

    /**
     * 重要时间节点(JSON格式)
     */
    @TableField("important_dates")
    private String importantDates;

    /**
     * 优先级
     */
    @TableField("priority")
    private String priority;

    /**
     * 状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建者ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 发布时间
     */
    @TableField("published_at")
    private LocalDateTime publishedAt;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
