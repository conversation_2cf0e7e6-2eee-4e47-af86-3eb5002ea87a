// 信誉系统相关类型定义

export interface UserReputationStats {
  userId: string;
  currentScore: number;
  currentLevel: string;
  totalEarned: number;
  totalDeducted: number;
  consecutiveLoginDays: number;
  consecutiveStudyDays: number;
  consecutiveNoStudyDays: number;
  lastLoginDate: string | null;
  lastStudyDate: string | null;
  lastScoreUpdate: string;
  protectionEndTime: string | null;
  weeklyDeductPoints: number;
  monthlyDeductPoints: number;
  isInProtection: boolean;
  protectionHoursRemaining: number;
}

export interface ReputationLog {
  id: string;
  userId: string;
  changeType: 'earn' | 'deduct';
  points: number;
  reason: string;
  category: string;
  relatedId: string | null;
  consecutiveDays: number | null;
  processedBy: 'system' | 'manual' | 'admin';
  createdAt: string;
}

export interface ProtectionInfo {
  isInProtection: boolean;
  endTime: string | null;
  remainingHours: number;
  remainingMinutes: number;
}

export interface ReputationLevel {
  level: string;
  name: string;
  minScore: number;
  maxScore: number;
  color: string;
}

// 信誉变更分类
export const REPUTATION_CATEGORIES = {
  DAILY_LOGIN: 'daily_login',
  DAILY_STUDY: 'daily_study',
  STUDY_QUALITY: 'study_quality',
  DESK_PERFORMANCE: 'desk_performance',
  DESK_VIOLATION: 'desk_violation',
  PLATFORM_VIOLATION: 'platform_violation',
  INACTIVE: 'inactive',
  RECOVERY: 'recovery'
} as const;

// 信誉变更分类显示名称
export const REPUTATION_CATEGORY_NAMES = {
  [REPUTATION_CATEGORIES.DAILY_LOGIN]: '每日登录',
  [REPUTATION_CATEGORIES.DAILY_STUDY]: '每日学习',
  [REPUTATION_CATEGORIES.STUDY_QUALITY]: '学习质量',
  [REPUTATION_CATEGORIES.DESK_PERFORMANCE]: '小桌表现',
  [REPUTATION_CATEGORIES.DESK_VIOLATION]: '小桌违规',
  [REPUTATION_CATEGORIES.PLATFORM_VIOLATION]: '平台违规',
  [REPUTATION_CATEGORIES.INACTIVE]: '不活跃',
  [REPUTATION_CATEGORIES.RECOVERY]: '恢复奖励'
} as const;

// 信誉等级配置
export const REPUTATION_LEVELS: ReputationLevel[] = [
  { level: 'newbie', name: '新手', minScore: 0, maxScore: 99, color: '#87d068' },
  { level: 'bronze', name: '青铜', minScore: 100, maxScore: 299, color: '#CD7F32' },
  { level: 'silver', name: '白银', minScore: 300, maxScore: 599, color: '#C0C0C0' },
  { level: 'gold', name: '黄金', minScore: 600, maxScore: 999, color: '#FFD700' },
  { level: 'platinum', name: '铂金', minScore: 1000, maxScore: 1999, color: '#E5E4E2' },
  { level: 'diamond', name: '钻石', minScore: 2000, maxScore: 4999, color: '#B9F2FF' },
  { level: 'master', name: '大师', minScore: 5000, maxScore: 9999, color: '#FF6B6B' },
  { level: 'grandmaster', name: '宗师', minScore: 10000, maxScore: 999999, color: '#9B59B6' }
];

// 获取等级信息
export const getLevelInfo = (level: string): ReputationLevel | undefined => {
  return REPUTATION_LEVELS.find(l => l.level === level);
};

// 获取下一等级信息
export const getNextLevelInfo = (currentLevel: string): ReputationLevel | null => {
  const currentIndex = REPUTATION_LEVELS.findIndex(l => l.level === currentLevel);
  if (currentIndex === -1 || currentIndex === REPUTATION_LEVELS.length - 1) {
    return null;
  }
  return REPUTATION_LEVELS[currentIndex + 1];
};

// 计算等级进度
export const calculateLevelProgress = (currentScore: number, currentLevel: string): {
  progress: number;
  currentLevelScore: number;
  nextLevelScore: number;
  pointsToNext: number;
} => {
  const currentLevelInfo = getLevelInfo(currentLevel);
  const nextLevelInfo = getNextLevelInfo(currentLevel);
  
  if (!currentLevelInfo) {
    return { progress: 0, currentLevelScore: 0, nextLevelScore: 0, pointsToNext: 0 };
  }
  
  if (!nextLevelInfo) {
    // 已达到最高等级
    return { 
      progress: 100, 
      currentLevelScore: currentLevelInfo.minScore,
      nextLevelScore: currentLevelInfo.maxScore,
      pointsToNext: 0 
    };
  }
  
  const currentLevelScore = currentLevelInfo.minScore;
  const nextLevelScore = nextLevelInfo.minScore;
  const levelRange = nextLevelScore - currentLevelScore;
  const currentProgress = currentScore - currentLevelScore;
  const progress = Math.min(100, Math.max(0, (currentProgress / levelRange) * 100));
  const pointsToNext = Math.max(0, nextLevelScore - currentScore);
  
  return {
    progress,
    currentLevelScore,
    nextLevelScore,
    pointsToNext
  };
};
