package com.wqh.publicexaminationassistant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 更新个人资料请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "更新个人资料请求")
public class UpdateProfileRequest {

    @Size(max = 50, message = "昵称长度不能超过50字符")
    @Schema(description = "昵称", example = "新昵称")
    private String nickname;

    @Size(max = 500, message = "头像URL长度不能超过500字符")
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Size(max = 100, message = "目标岗位长度不能超过100字符")
    @Schema(description = "目标岗位", example = "公务员")
    private String targetPosition;

    @Size(max = 20, message = "手机号长度不能超过20字符")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;
}
