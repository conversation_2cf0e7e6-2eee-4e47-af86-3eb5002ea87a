package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.nio.file.Files;
import java.nio.file.Path;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.request;
import org.springframework.http.HttpMethod;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 文件访问控制器测试
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@WebMvcTest(FileAccessController.class)
class FileAccessControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileUploadProperties fileUploadProperties;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        when(fileUploadProperties.getPath()).thenReturn(tempDir.toString());
    }

    @Test
    void testGetExistingFile() throws Exception {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "Hello World".getBytes());

        // 测试文件访问
        mockMvc.perform(get("/files/test.txt"))
                .andExpect(status().isOk())
                .andExpect(content().string("Hello World"))
                .andExpect(header().string("Content-Type", "text/plain"));
    }

    @Test
    void testGetNonExistentFile() throws Exception {
        // 测试不存在的文件
        mockMvc.perform(get("/files/nonexistent.txt"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testHeadExistingFile() throws Exception {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.txt");
        Files.write(testFile, "Hello World".getBytes());

        // 测试HEAD请求
        mockMvc.perform(request(HttpMethod.HEAD, "/files/test.txt"))
                .andExpect(status().isOk());
    }

    @Test
    void testHeadNonExistentFile() throws Exception {
        // 测试不存在的文件的HEAD请求
        mockMvc.perform(request(HttpMethod.HEAD, "/files/nonexistent.txt"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testGetImageFile() throws Exception {
        // 创建测试图片文件路径
        Path imageDir = tempDir.resolve("wrong-questions/thumbnails/2025/07/19");
        Files.createDirectories(imageDir);
        Path imageFile = imageDir.resolve("test.png");
        Files.write(imageFile, "fake png content".getBytes());

        // 测试图片文件访问
        mockMvc.perform(get("/files/wrong-questions/thumbnails/2025/07/19/test.png"))
                .andExpect(status().isOk())
                .andExpect(content().bytes("fake png content".getBytes()));
    }
}
