package com.wqh.publicexaminationassistant.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 刷题记录响应DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class StudyRecordResponse {

    /**
     * 记录ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 模块类型
     */
    private String moduleType;

    /**
     * 模块名称 (中文显示)
     */
    private String moduleName;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 正确数量
     */
    private Integer correctCount;

    /**
     * 错题数量
     */
    private Integer wrongCount;

    /**
     * 正确率(%)
     */
    private BigDecimal accuracyRate;

    /**
     * 学习时长(分钟)
     */
    private Integer studyDuration;

    /**
     * 薄弱知识点
     */
    private String weakPoints;

    /**
     * 学习日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate studyDate;

    /**
     * 学习笔记
     */
    private String notes;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 关联的错题数量
     */
    private Integer wrongQuestionCount;

    /**
     * 用户信息 (可选)
     */
    private UserBasicInfo user;

    /**
     * 用户基本信息内部类
     */
    @Data
    public static class UserBasicInfo {
        private String id;
        private String username;
        private String nickname;
        private String avatarUrl;
    }
}
