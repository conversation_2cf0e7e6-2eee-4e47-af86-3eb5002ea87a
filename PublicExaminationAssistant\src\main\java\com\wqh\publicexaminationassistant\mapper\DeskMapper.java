package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.entity.Desk;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 小桌表Mapper接口
 * 提供小桌数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface DeskMapper extends BaseMapper<Desk> {

    /**
     * 查询用户创建的小桌
     */
    @Select("SELECT * FROM desks WHERE owner_id = #{userId} AND status != 'dissolved' ORDER BY created_at DESC")
    List<Desk> findByOwnerId(@Param("userId") String userId);

    /**
     * 查询活跃的小桌（分页）
     */
    @Select("SELECT * FROM desks WHERE status = 'active' ORDER BY current_members DESC, created_at DESC")
    Page<Desk> findActiveDesks(Page<Desk> page);

    /**
     * 搜索小桌（按名称）
     */
    @Select("SELECT * FROM desks WHERE status = 'active' AND name LIKE CONCAT('%', #{keyword}, '%') ORDER BY current_members DESC")
    List<Desk> searchByName(@Param("keyword") String keyword);

    /**
     * 更新成员数量
     */
    @Update("UPDATE desks SET current_members = #{count}, updated_at = NOW() WHERE id = #{deskId}")
    int updateMemberCount(@Param("deskId") String deskId, @Param("count") Integer count);

    /**
     * 获取小桌统计信息
     */
    @Select("SELECT COUNT(*) as total_desks, AVG(current_members) as avg_members, " +
            "SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_desks " +
            "FROM desks WHERE status != 'dissolved'")
    Map<String, Object> getDeskStatistics();

    /**
     * 查询用户参与的所有小桌（包括创建的和加入的）
     */
    @Select("SELECT DISTINCT d.* FROM desks d " +
            "LEFT JOIN desk_members dm ON d.id = dm.desk_id " +
            "WHERE (d.owner_id = #{userId} OR (dm.user_id = #{userId} AND dm.status = 'active')) " +
            "AND d.status != 'dissolved' " +
            "ORDER BY d.created_at DESC")
    List<Desk> findDesksByUserId(@Param("userId") String userId);

    /**
     * 统计用户创建的小桌数量
     */
    @Select("SELECT COUNT(*) FROM desks WHERE owner_id = #{userId} AND status != 'dissolved'")
    Long countByOwnerId(@Param("userId") String userId);

    /**
     * 查询最受欢迎的小桌（按成员数排序）
     */
    @Select("SELECT * FROM desks WHERE status = 'active' ORDER BY current_members DESC LIMIT #{limit}")
    List<Desk> findPopularDesks(@Param("limit") Integer limit);

    /**
     * 查询最新创建的小桌
     */
    @Select("SELECT * FROM desks WHERE status = 'active' ORDER BY created_at DESC LIMIT #{limit}")
    List<Desk> findLatestDesks(@Param("limit") Integer limit);
}
