package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.*;
import com.wqh.publicexaminationassistant.dto.response.*;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户管理", description = "用户注册、登录、个人资料管理等功能")
public class UserController {

    private final UserService userService;

    @Operation(summary = "用户注册", description = "使用邀请码注册新用户账户")
    @PostMapping("/auth/register")
    public ApiResponse<RegisterResponse> register(@Valid @RequestBody UserRegisterRequest request) {
        log.info("用户注册请求: {}", request.getUsername());
        RegisterResponse response = userService.register(request);
        return ApiResponse.success(response, "注册成功");
    }

    @Operation(summary = "用户登录", description = "使用用户名或邮箱登录，返回JWT Token")
    @PostMapping("/auth/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody UserLoginRequest request) {
        log.info("用户登录请求: {}", request.getUsername());
        LoginResponse response = userService.login(request);
        return ApiResponse.success(response, "登录成功");
    }

    @Operation(summary = "用户登出", description = "用户登出（将Token加入黑名单）")
    @SecurityRequirement(name = "Bearer Authentication")
    @PostMapping("/auth/logout")
    public ApiResponse<Void> logout(@AuthenticationPrincipal JwtUserDetails userDetails) {
        log.info("用户登出请求: userId={}", userDetails.getId());
        // TODO: 实现Token黑名单功能
        return ApiResponse.success(null, "登出成功");
    }

    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    @PostMapping("/auth/refresh")
    public ApiResponse<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        log.info("刷新Token请求");
        LoginResponse response = userService.refreshToken(request);
        return ApiResponse.success(response, "Token刷新成功");
    }

    @Operation(summary = "获取个人资料", description = "获取当前登录用户的个人资料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @GetMapping("/users/profile")
    public ApiResponse<UserInfoResponse> getProfile(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {

        // 检查用户认证信息
        if (userDetails == null) {
            log.error("用户认证信息为空");
            return ApiResponse.error("用户认证信息无效");
        }

        String userId = userDetails.getId();
        if (userId == null || userId.trim().isEmpty()) {
            log.error("用户ID为空: userDetails={}", userDetails);
            return ApiResponse.error("用户ID无效");
        }

        log.info("获取用户资料请求: userId={}, username={}", userId, userDetails.getUsername());
        UserInfoResponse response = userService.getUserInfo(userId);
        return ApiResponse.success(response);
    }

    @Operation(summary = "更新个人资料", description = "更新当前登录用户的个人资料信息")
    @SecurityRequirement(name = "Bearer Authentication")
    @PutMapping("/users/profile")
    public ApiResponse<UserInfoResponse> updateProfile(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails,
            @Valid @RequestBody UpdateProfileRequest request) {
        log.info("更新用户资料请求: userId={}", userDetails.getId());
        UserInfoResponse response = userService.updateProfile(userDetails.getId(), request);
        return ApiResponse.success(response, "更新成功");
    }

    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    @SecurityRequirement(name = "Bearer Authentication")
    @PutMapping("/users/password")
    public ApiResponse<Void> changePassword(
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails,
            @Valid @RequestBody ChangePasswordRequest request) {
        log.info("修改密码请求: userId={}", userDetails.getId());
        userService.changePassword(userDetails.getId(), request);
        return ApiResponse.success(null, "密码修改成功");
    }

    @Operation(summary = "上传头像", description = "上传用户头像图片")
    @SecurityRequirement(name = "Bearer Authentication")
    @PostMapping("/users/avatar")
    public ApiResponse<UserInfoResponse> uploadAvatar(
            @Parameter(description = "头像图片文件")
            @RequestParam("file") MultipartFile file,
            @Parameter(hidden = true) @AuthenticationPrincipal JwtUserDetails userDetails) {
        log.info("用户上传头像: userId={}, fileName={}, fileSize={}",
                userDetails.getId(), file.getOriginalFilename(), file.getSize());

        UserInfoResponse response = userService.uploadAvatar(userDetails.getId(), file);
        return ApiResponse.success(response, "头像上传成功");
    }
}
