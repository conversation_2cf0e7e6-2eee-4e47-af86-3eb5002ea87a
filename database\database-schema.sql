-- =====================================================
-- 考公刷题记录系统 - 数据库建表脚本
-- =====================================================
-- 版本: v1.0
-- 数据库: MySQL 8.0+
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- 创建时间: 2024
-- =====================================================
-- 设置数据库配置
SET
  NAMES utf8mb4;

SET
  FOREIGN_KEY_CHECKS = 0;

-- 配置MySQL全文搜索ngram分词器
SET
  GLOBAL ngram_token_size = 2;

-- =====================================================
-- 1. 用户管理模块
-- =====================================================
-- 1.1 用户表 (users)
-- 功能: 存储用户基本信息和认证数据
DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `id` VARCHAR(36) NOT NULL COMMENT '用户唯一标识(UUID)',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `email` VARCHAR(100) NOT NULL COMMENT '邮箱地址',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希值',
  `avatar_url` VARCHAR(500) NULL DEFAULT NULL COMMENT '头像URL',
  `nickname` VARCHAR(50) NULL DEFAULT NULL COMMENT '昵称',
  `target_position` VARCHAR(100) NULL DEFAULT NULL COMMENT '目标岗位',
  `reputation_score` INT NOT NULL DEFAULT 100 COMMENT '信誉分数',
  `reputation_level` VARCHAR(20) NOT NULL DEFAULT 'newbie' COMMENT '信誉等级',
  `system_role` VARCHAR(20) NOT NULL DEFAULT 'user' COMMENT '系统角色',
  `phone` VARCHAR(20) NULL DEFAULT NULL COMMENT '手机号',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '账户状态',
  `last_login_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  INDEX `idx_reputation_score` (`reputation_score`),
  INDEX `idx_reputation_level` (`reputation_level`),
  INDEX `idx_system_role` (`system_role`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表';

-- 1.2 邀请码表 (invite_codes)
-- 功能: 管理邀请码的生成、使用和追踪
DROP TABLE IF EXISTS `invite_codes`;

CREATE TABLE `invite_codes` (
  `id` VARCHAR(36) NOT NULL COMMENT '邀请码ID',
  `code` VARCHAR(20) NOT NULL COMMENT '邀请码',
  `created_by` VARCHAR(36) NOT NULL COMMENT '创建者ID',
  `used_by` VARCHAR(36) NULL DEFAULT NULL COMMENT '使用者ID',
  `expires_at` TIMESTAMP NOT NULL COMMENT '过期时间',
  `used_at` TIMESTAMP NULL DEFAULT NULL COMMENT '使用时间',
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否有效',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  INDEX `idx_created_by` (`created_by`),
  INDEX `idx_used_by` (`used_by`),
  INDEX `idx_expires_at` (`expires_at`),
  CONSTRAINT `fk_invite_codes_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invite_codes_used_by` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE
  SET
    NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '邀请码表';

-- =====================================================
-- 2. 刷题记录模块
-- =====================================================
-- 2.1 刷题记录表 (study_records)
-- 功能: 记录每次刷题的详细数据
DROP TABLE IF EXISTS `study_records`;

CREATE TABLE `study_records` (
  `id` VARCHAR(36) NOT NULL COMMENT '记录ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `module_type` VARCHAR(50) NOT NULL COMMENT '模块类型',
  `question_count` INT NOT NULL COMMENT '题目数量',
  `correct_count` INT NOT NULL COMMENT '正确数量',
  `accuracy_rate` DECIMAL(5, 2) NOT NULL COMMENT '正确率(%)',
  `study_duration` INT NOT NULL COMMENT '学习时长(分钟)',
  `weak_points` JSON NULL DEFAULT NULL COMMENT '薄弱知识点',
  `study_date` DATE NOT NULL COMMENT '学习日期',
  `notes` TEXT NULL DEFAULT NULL COMMENT '学习笔记',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_study_date` (`study_date`),
  INDEX `idx_module_type` (`module_type`),
  INDEX `idx_user_date` (`user_id`, `study_date`),
  CONSTRAINT `fk_study_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '刷题记录表';

-- 2.2 错题本表 (wrong_questions)
-- 功能: 存储用户的错题信息和复习状态
DROP TABLE IF EXISTS `wrong_questions`;

CREATE TABLE `wrong_questions` (
  `id` VARCHAR(36) NOT NULL COMMENT '错题ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `study_record_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '关联的学习记录',
  `question_type` VARCHAR(50) NOT NULL COMMENT '题目类型',
  `difficulty_level` VARCHAR(20) NOT NULL COMMENT '难度等级',
  `question_content` TEXT NOT NULL COMMENT '题目内容',
  `user_answer` TEXT NULL DEFAULT NULL COMMENT '用户答案',
  `correct_answer` TEXT NOT NULL COMMENT '正确答案',
  `explanation` TEXT NULL DEFAULT NULL COMMENT '解析',
  `mastery_status` VARCHAR(20) NOT NULL DEFAULT 'not_mastered' COMMENT '掌握状态',
  `review_count` INT NOT NULL DEFAULT 0 COMMENT '复习次数',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `reviewed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后复习时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_mastery_status` (`mastery_status`),
  INDEX `idx_question_type` (`question_type`),
  CONSTRAINT `fk_wrong_questions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_wrong_questions_study_record_id` FOREIGN KEY (`study_record_id`) REFERENCES `study_records` (`id`) ON DELETE
  SET
    NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错题本表';

-- 2.3 错题图片表 (wrong_question_images)
-- 功能: 存储错题相关的图片信息
DROP TABLE IF EXISTS `wrong_question_images`;

CREATE TABLE `wrong_question_images` (
  `id` VARCHAR(36) NOT NULL COMMENT '图片ID',
  `wrong_question_id` VARCHAR(36) NOT NULL COMMENT '错题ID',
  `image_type` VARCHAR(20) NOT NULL COMMENT '图片类型：question|answer|explanation',
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件存储路径',
  `file_size` BIGINT NOT NULL COMMENT '文件大小(字节)',
  `mime_type` VARCHAR(100) NOT NULL COMMENT 'MIME类型',
  `width` INT NULL COMMENT '图片宽度(像素)',
  `height` INT NULL COMMENT '图片高度(像素)',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_compressed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已压缩',
  `thumbnail_path` VARCHAR(500) NULL COMMENT '缩略图路径',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_wrong_question_id` (`wrong_question_id`),
  INDEX `idx_image_type` (`image_type`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_wrong_question_type` (`wrong_question_id`, `image_type`),
  INDEX `idx_wrong_question_sort` (`wrong_question_id`, `image_type`, `sort_order`),
  CONSTRAINT `fk_wrong_question_images_wrong_question_id` FOREIGN KEY (`wrong_question_id`) REFERENCES `wrong_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_image_type` CHECK (
    `image_type` IN ('question', 'answer', 'explanation')
  ),
  CONSTRAINT `chk_file_size` CHECK (
    `file_size` > 0
    AND `file_size` <= 10485760
  ),
  CONSTRAINT `chk_sort_order` CHECK (`sort_order` >= 0)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错题图片表';

-- 2.4 学习计划表 (study_plans)
-- 功能: 管理用户的学习计划和目标
DROP TABLE IF EXISTS `study_plans`;

CREATE TABLE `study_plans` (
  `id` VARCHAR(36) NOT NULL COMMENT '计划ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `title` VARCHAR(100) NOT NULL COMMENT '计划标题',
  `description` TEXT NULL DEFAULT NULL COMMENT '计划描述',
  `target_exam_date` DATE NULL DEFAULT NULL COMMENT '目标考试日期',
  `daily_target_questions` INT NULL DEFAULT NULL COMMENT '每日目标题量',
  `daily_target_time` INT NULL DEFAULT NULL COMMENT '每日目标时长(分钟)',
  `modules` JSON NOT NULL COMMENT '学习模块配置',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '计划状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_target_exam_date` (`target_exam_date`),
  CONSTRAINT `fk_study_plans_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学习计划表';

-- =====================================================
-- 3. 小桌系统模块 🌟
-- =====================================================
-- 3.1 小桌表 (desks)
-- 功能: 存储小桌的基本信息和配置
DROP TABLE IF EXISTS `desks`;

CREATE TABLE `desks` (
  `id` VARCHAR(36) NOT NULL COMMENT '小桌ID',
  `name` VARCHAR(100) NOT NULL COMMENT '小桌名称',
  `description` TEXT NULL DEFAULT NULL COMMENT '小桌描述',
  `owner_id` VARCHAR(36) NOT NULL COMMENT '桌长ID',
  `max_members` INT NOT NULL DEFAULT 8 COMMENT '最大成员数',
  `current_members` INT NOT NULL DEFAULT 1 COMMENT '当前成员数',
  `auto_approve_rules` JSON NULL DEFAULT NULL COMMENT '自动审核规则',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '小桌状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_owner_id` (`owner_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`),
  CONSTRAINT `fk_desks_owner_id` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌表';

-- 3.2 小桌成员表 (desk_members)
-- 功能: 管理小桌成员关系和状态
DROP TABLE IF EXISTS `desk_members`;

CREATE TABLE `desk_members` (
  `id` VARCHAR(36) NOT NULL COMMENT '成员关系ID',
  `desk_id` VARCHAR(36) NOT NULL COMMENT '小桌ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `role` VARCHAR(20) NOT NULL DEFAULT 'member' COMMENT '角色(owner/member)',
  `status` VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '成员状态',
  `join_reason` TEXT NULL DEFAULT NULL COMMENT '加入理由',
  `joined_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `last_active_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_desk_user` (`desk_id`, `user_id`),
  INDEX `idx_desk_id` (`desk_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  CONSTRAINT `fk_desk_members_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_desk_members_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌成员表';

-- 3.3 小桌申请表 (desk_applications)
-- 功能: 管理加入小桌的申请流程
DROP TABLE IF EXISTS `desk_applications`;

CREATE TABLE `desk_applications` (
  `id` VARCHAR(36) NOT NULL COMMENT '申请ID',
  `desk_id` VARCHAR(36) NOT NULL COMMENT '小桌ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '申请者ID',
  `reason` TEXT NOT NULL COMMENT '申请理由',
  `study_plan` TEXT NULL DEFAULT NULL COMMENT '学习计划',
  `status` VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `applied_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `processed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '处理时间',
  `processed_by` VARCHAR(36) NULL DEFAULT NULL COMMENT '处理人ID',
  PRIMARY KEY (`id`),
  INDEX `idx_desk_id` (`desk_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_applied_at` (`applied_at`),
  CONSTRAINT `fk_desk_applications_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_desk_applications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_desk_applications_processed_by` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE
  SET
    NULL
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌申请表';

-- =====================================================
-- 4. 信誉系统模块 🌟
-- =====================================================
-- 4.1 信誉记录表 (reputation_logs)
-- 功能: 记录信誉分数的变化历史和原因
DROP TABLE IF EXISTS `reputation_logs`;

CREATE TABLE `reputation_logs` (
  `id` VARCHAR(36) NOT NULL COMMENT '记录ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `change_type` ENUM('earn', 'deduct') NOT NULL COMMENT '变化类型: earn-获得, deduct-扣除',
  `points` INT NOT NULL COMMENT '分数变化',
  `reason` VARCHAR(200) NOT NULL COMMENT '变化原因',
  `category` ENUM(
    'daily_login',
    'daily_study',
    'study_quality',
    'desk_performance',
    'desk_violation',
    'platform_violation',
    'inactive',
    'recovery'
  ) NOT NULL COMMENT '分类',
  `related_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '关联ID(如小桌ID、学习记录ID等)',
  `consecutive_days` INT DEFAULT 0 COMMENT '连续天数(用于连续学习/未学习记录)',
  `processed_by` VARCHAR(50) DEFAULT 'system' COMMENT '处理方式: system-系统, manual-手动, admin-管理员',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_change_type` (`change_type`),
  INDEX `idx_category` (`category`),
  INDEX `idx_created_at` (`created_at`),
  CONSTRAINT `fk_reputation_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '信誉记录表';

-- 4.2 用户信誉统计表 (user_reputation_stats)
-- 功能: 存储用户信誉统计信息和状态
DROP TABLE IF EXISTS `user_reputation_stats`;

CREATE TABLE `user_reputation_stats` (
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `current_score` INT NOT NULL DEFAULT 100 COMMENT '当前信誉分数',
  `current_level` VARCHAR(20) NOT NULL DEFAULT 'newbie' COMMENT '当前信誉等级',
  `total_earned` INT NOT NULL DEFAULT 0 COMMENT '累计获得分数',
  `total_deducted` INT NOT NULL DEFAULT 0 COMMENT '累计扣除分数',
  `consecutive_login_days` INT NOT NULL DEFAULT 0 COMMENT '连续登录天数',
  `consecutive_study_days` INT NOT NULL DEFAULT 0 COMMENT '连续学习天数',
  `consecutive_no_study_days` INT NOT NULL DEFAULT 0 COMMENT '连续未学习天数',
  `last_login_date` DATE NULL DEFAULT NULL COMMENT '最后登录日期',
  `last_study_date` DATE NULL DEFAULT NULL COMMENT '最后学习日期',
  `last_score_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后分数更新时间',
  `protection_end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '保护期结束时间',
  `weekly_deduct_points` INT NOT NULL DEFAULT 0 COMMENT '本周已扣分数',
  `monthly_deduct_points` INT NOT NULL DEFAULT 0 COMMENT '本月已扣分数',
  `last_weekly_reset` DATE NULL DEFAULT NULL COMMENT '上次周重置日期',
  `last_monthly_reset` DATE NULL DEFAULT NULL COMMENT '上次月重置日期',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  INDEX `idx_current_score` (`current_score`),
  INDEX `idx_current_level` (`current_level`),
  INDEX `idx_protection_end_time` (`protection_end_time`),
  CONSTRAINT `fk_user_reputation_stats_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户信誉统计表';

-- 4.3 定时任务执行记录表 (scheduled_task_logs)
-- 功能: 记录定时任务的执行情况
DROP TABLE IF EXISTS `scheduled_task_logs`;

CREATE TABLE `scheduled_task_logs` (
  `id` VARCHAR(36) NOT NULL COMMENT '记录ID',
  `task_name` VARCHAR(100) NOT NULL COMMENT '任务名称',
  `execution_date` DATE NOT NULL COMMENT '执行日期',
  `start_time` TIMESTAMP NOT NULL COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  `status` ENUM('running', 'completed', 'failed') NOT NULL COMMENT '执行状态',
  `processed_count` INT DEFAULT 0 COMMENT '处理数量',
  `error_message` TEXT NULL DEFAULT NULL COMMENT '错误信息',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_task_date` (`task_name`, `execution_date`),
  INDEX `idx_status` (`status`),
  INDEX `idx_execution_date` (`execution_date`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '定时任务执行记录表';

-- =====================================================
-- 5. 排行榜模块
-- =====================================================
-- 5.1 排行榜表 (rankings)
-- 功能: 存储各种维度的排行榜数据
DROP TABLE IF EXISTS `rankings`;

CREATE TABLE `rankings` (
  `id` VARCHAR(36) NOT NULL COMMENT '排行记录ID',
  `desk_id` VARCHAR(36) NULL DEFAULT NULL COMMENT '小桌ID(NULL为全站)',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `ranking_type` VARCHAR(50) NOT NULL COMMENT '排行类型',
  `score` INT NOT NULL COMMENT '排行分数',
  `rank_position` INT NOT NULL COMMENT '排名位置',
  `calculated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
  PRIMARY KEY (`id`),
  INDEX `idx_desk_id` (`desk_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_ranking_type` (`ranking_type`),
  INDEX `idx_rank_position` (`rank_position`),
  UNIQUE KEY `uk_desk_user_type_date` (
    `desk_id`,
    `user_id`,
    `ranking_type`,
    `calculated_at`
  ),
  CONSTRAINT `fk_rankings_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_rankings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '排行榜表';

-- =====================================================
-- 6. 考试公告模块
-- =====================================================
-- 6.1 考试公告表 (announcements)
-- 功能: 存储考试公告信息，支持全文搜索
DROP TABLE IF EXISTS `announcements`;

CREATE TABLE `announcements` (
  `id` VARCHAR(36) NOT NULL COMMENT '公告ID',
  `title` VARCHAR(200) NOT NULL COMMENT '公告标题',
  `content` TEXT NOT NULL COMMENT '公告内容',
  `region` VARCHAR(50) NULL DEFAULT NULL COMMENT '地区',
  `exam_type` VARCHAR(50) NULL DEFAULT NULL COMMENT '考试类型',
  `important_dates` JSON NULL DEFAULT NULL COMMENT '重要时间节点',
  `priority` VARCHAR(20) NOT NULL DEFAULT 'normal' COMMENT '优先级',
  `status` VARCHAR(20) NOT NULL DEFAULT 'published' COMMENT '状态',
  `created_by` VARCHAR(36) NOT NULL COMMENT '创建者ID',
  `published_at` TIMESTAMP NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_region` (`region`),
  INDEX `idx_exam_type` (`exam_type`),
  INDEX `idx_published_at` (`published_at`),
  INDEX `idx_status` (`status`),
  FULLTEXT KEY `ft_title_content` (`title`, `content`) WITH PARSER ngram,
  CONSTRAINT `fk_announcements_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '考试公告表';

-- =====================================================
-- 7. 通知系统模块
-- =====================================================
-- 7.1 通知表 (notifications)
-- 功能: 存储系统通知和用户消息
DROP TABLE IF EXISTS `notifications`;

CREATE TABLE `notifications` (
  `id` VARCHAR(36) NOT NULL COMMENT '通知ID',
  `user_id` VARCHAR(36) NOT NULL COMMENT '用户ID',
  `type` VARCHAR(50) NOT NULL COMMENT '通知类型',
  `title` VARCHAR(200) NOT NULL COMMENT '通知标题',
  `content` TEXT NOT NULL COMMENT '通知内容',
  `metadata` JSON NULL DEFAULT NULL COMMENT '额外数据',
  `is_read` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`),
  INDEX `idx_is_read` (`is_read`),
  INDEX `idx_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知表';

-- =====================================================
-- 脚本执行完成
-- =====================================================
-- 恢复外键检查
SET
  FOREIGN_KEY_CHECKS = 1;

-- 显示创建的表
SHOW TABLES;

-- =====================================================
-- 建表脚本执行完成
-- 总计创建表数量: 17张
-- 包含模块:
-- 1. 用户管理模块 (2张表): users, invite_codes
-- 2. 刷题记录模块 (3张表): study_records, wrong_questions, study_plans
-- 3. 小桌系统模块 (3张表): desks, desk_members, desk_applications
-- 4. 信誉系统模块 (3张表): reputation_logs, user_reputation_stats, scheduled_task_logs
-- 5. 排行榜模块 (1张表): rankings
-- 6. 考试公告模块 (1张表): announcements
-- 7. 通知系统模块 (1张表): notifications
-- =====================================================