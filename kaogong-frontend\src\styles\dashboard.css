/* 仪表板页面 - 社交学习型样式 */

/* 1. 页面布局 */
.dashboard-container {
  min-height: calc(100vh - 80px);
  background:
    /* 纸质纹理层 */
    radial-gradient(circle at 20% 80%, rgba(237, 137, 54, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(66, 153, 225, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(72, 187, 120, 0.02) 0%, transparent 50%),
    /* 纸质基础渐变 */
    linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 50%, var(--paper-cream) 100%),
    /* 纸质纹理噪点 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(45, 55, 72, 0.005) 2px,
      rgba(45, 55, 72, 0.005) 4px
    );
  padding: 20px;
  font-family: var(--font-system);
  animation: fadeIn 0.6s ease-out;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰元素 */
.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    /* 大装饰圆点 */
    radial-gradient(circle at 15% 25%, var(--accent-blue-light) 1px, transparent 1px),
    radial-gradient(circle at 85% 75%, var(--accent-orange-light) 1px, transparent 1px),
    radial-gradient(circle at 70% 15%, var(--accent-green-light) 1px, transparent 1px),
    radial-gradient(circle at 25% 85%, var(--accent-purple-light) 1px, transparent 1px);
  background-size: 200px 200px, 180px 180px, 220px 220px, 160px 160px;
  opacity: 0.3;
  pointer-events: none;
  z-index: 1;
  animation: gentleFloat 15s ease-in-out infinite;
}

.dashboard-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    /* 小装饰点 */
    radial-gradient(circle at 30% 60%, var(--accent-pink-light) 0.5px, transparent 0.5px),
    radial-gradient(circle at 90% 30%, var(--accent-blue-light) 0.5px, transparent 0.5px),
    radial-gradient(circle at 10% 90%, var(--accent-green-light) 0.5px, transparent 0.5px);
  background-size: 120px 120px, 100px 100px, 140px 140px;
  opacity: 0.2;
  pointer-events: none;
  z-index: 1;
  animation: float 20s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-content > * {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.dashboard-content > *:nth-child(1) { animation-delay: 0.1s; }
.dashboard-content > *:nth-child(2) { animation-delay: 0.2s; }
.dashboard-content > *:nth-child(3) { animation-delay: 0.3s; }
.dashboard-content > *:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 2. 个人信息横幅 */
.user-banner {
  background:
    /* 渐变背景 */
    linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 50%, var(--paper-cream) 100%),
    /* 纸质纹理 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 3px,
      rgba(66, 153, 225, 0.01) 3px,
      rgba(66, 153, 225, 0.01) 6px
    );
  border: 2px solid var(--ink-dark);
  border-radius: 20px;
  padding: 24px;
  transform: rotate(-0.3deg);
  box-shadow:
    /* 主阴影 */
    6px 6px 0px var(--shadow-light),
    /* 内发光 */
    inset 0 2px 0 rgba(255, 255, 255, 0.6),
    /* 外发光 */
    0 0 30px rgba(237, 137, 54, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.user-banner::before {
  content: '🌟';
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 24px;
  opacity: 0.3;
  animation: twinkle 2s ease-in-out infinite;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--ink-dark);
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: white;
  font-weight: bold;
  transform: rotate(5deg);
  box-shadow: 2px 2px 8px var(--shadow-medium);
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  font-size: 28px;
  font-family: var(--font-handwritten);
  font-weight: 700;
  color: var(--ink-dark);
  margin: 0;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.reputation-badge {
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-pink));
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: 2px solid var(--ink-dark);
  transform: rotate(-2deg);
  box-shadow: 2px 2px 0px var(--shadow-medium);
}

.study-status {
  background: var(--accent-green);
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--ink-dark);
  transform: rotate(1deg);
}

/* 3. 核心功能区域 */
.main-features {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
}

.feature-card {
  background:
    /* 纸质纹理 */
    linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%),
    /* 细微噪点 */
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 1px,
      rgba(45, 55, 72, 0.008) 1px,
      rgba(45, 55, 72, 0.008) 2px
    );
  border: 2px solid var(--ink-dark);
  border-radius: 16px;
  padding: 20px;
  transform: rotate(0.5deg);
  box-shadow:
    /* 主阴影 */
    4px 4px 0px var(--shadow-light),
    /* 内阴影模拟纸质厚度 */
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    /* 外发光 */
    0 0 20px rgba(66, 153, 225, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 10;
}

.feature-card:nth-child(2) {
  transform: rotate(-0.5deg);
}

.feature-card:nth-child(3) {
  transform: rotate(0.8deg);
}

.feature-card:hover {
  transform: rotate(0deg) translateY(-6px);
  box-shadow:
    /* 增强主阴影 */
    8px 8px 0px var(--shadow-medium),
    /* 内阴影保持 */
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    /* 增强外发光 */
    0 0 30px rgba(66, 153, 225, 0.15),
    /* 新增彩色发光 */
    0 0 50px rgba(237, 137, 54, 0.08);
}

.feature-card-title {
  font-size: 18px;
  font-family: var(--font-handwritten);
  font-weight: 600;
  color: var(--ink-dark);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 4. 我的小桌卡片 */
.desk-ranking {
  
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px dashed var(--border-light);
}

.ranking-item:last-child {
  border-bottom: none;
}

.ranking-position {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--accent-blue);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.ranking-position.me {
  background: var(--accent-orange);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.member-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--accent-purple);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.member-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--ink-dark);
}

.member-score {
  font-size: 12px;
  color: var(--ink-medium);
  font-weight: 600;
}

/* 5. 学习概览卡片 */
.study-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.study-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--paper-warm);
  border: 1px dashed var(--border-medium);
  border-radius: 12px;
  transform: rotate(-0.5deg);
}

.study-stat:nth-child(even) {
  transform: rotate(0.5deg);
}

.stat-label {
  font-size: 14px;
  color: var(--ink-medium);
  font-weight: 500;
}

.stat-value {
  font-size: 20px;
  font-family: var(--font-handwritten);
  font-weight: 700;
  color: var(--accent-blue);
}

/* 6. 快速开始卡片 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-button {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 25px;
  padding: 16px 20px;
  font-size: 16px;
  font-family: var(--font-handwritten);
  font-weight: 600;
  color: var(--ink-dark);
  cursor: pointer;
  transition: all 0.3s ease;
  transform: rotate(-1deg);
  text-align: center;
  text-decoration: none;
  display: block;
}

.action-button:nth-child(even) {
  transform: rotate(1deg);
}

.action-button:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 3px 3px 0px var(--shadow-medium);
  background: var(--accent-blue);
  color: white;
}

.action-button.primary {
  background: var(--accent-blue);
  color: white;
  font-size: 18px;
  padding: 20px;
}

.action-button.primary:hover {
  background: #3182ce;
}

/* 7. 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
    background-size: 150px 150px, 130px 130px, 170px 170px, 120px 120px;
  }

  .dashboard-container::before,
  .dashboard-container::after {
    opacity: 0.2;
  }

  .main-features {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .user-banner {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    transform: rotate(0deg);
    padding: 20px;
  }

  .user-status {
    justify-content: center;
  }

  .feature-card {
    transform: rotate(0deg) !important;
    padding: 16px;
  }

  .floating-emoji {
    font-size: 20px;
    opacity: 0.3;
  }

  /* 移动端图表样式优化 */
  .study-trends {
    min-height: 180px;
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .dashboard-content {
    gap: 16px;
  }

  .user-name {
    font-size: 24px;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  /* 小屏幕图表样式优化 */
  .study-trends {
    min-height: 160px;
    padding: 8px;
  }

  .feature-card-title {
    font-size: 16px;
  }
}

/* 8. 动态信息流 */
.activity-feed {
  margin-top: 8px;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background:
    linear-gradient(135deg, var(--paper-warm) 0%, var(--paper-cream) 100%),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(72, 187, 120, 0.005) 2px,
      rgba(72, 187, 120, 0.005) 4px
    );
  border: 1px dashed var(--border-medium);
  border-radius: 12px;
  transform: rotate(-0.3deg);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(45, 55, 72, 0.05);
}

.activity-item:nth-child(even) {
  transform: rotate(0.3deg);
}

.activity-item:hover {
  transform: rotate(0deg);
  background: var(--paper-bg);
  box-shadow: 2px 2px 4px var(--shadow-light);
}

.activity-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--paper-bg);
  border-radius: 50%;
  border: 1px solid var(--border-medium);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-text {
  font-size: 14px;
  color: var(--ink-dark);
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: var(--ink-light);
}

/* 9. 推荐区域 */
.recommendations {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 8px;
}

.recommendation-section {
  display: flex;
  flex-direction: column;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--paper-warm);
  border: 1px dashed var(--border-light);
  border-radius: 10px;
  margin-bottom: 8px;
  transform: rotate(-0.2deg);
  transition: all 0.3s ease;
}

.recommendation-item:nth-child(even) {
  transform: rotate(0.2deg);
}

.recommendation-item:hover {
  transform: rotate(0deg);
  background: var(--paper-bg);
  box-shadow: 2px 2px 4px var(--shadow-light);
}

.rec-icon {
  font-size: 18px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--paper-bg);
  border-radius: 50%;
  border: 1px solid var(--border-medium);
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.rec-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--ink-dark);
}

.rec-desc {
  font-size: 12px;
  color: var(--ink-medium);
  line-height: 1.3;
}

/* 10. 响应式设计扩展 */
@media (max-width: 768px) {
  .recommendations {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item,
  .recommendation-item {
    transform: rotate(0deg) !important;
  }
}

@media (max-width: 480px) {
  .activities-list {
    max-height: 250px;
  }

  .activity-item {
    padding: 10px;
  }

  .recommendation-item {
    padding: 10px;
  }

  .rec-content {
    gap: 1px;
  }
}

/* 学习趋势样式 */
.study-trends {
  padding: 12px 16px;
  min-height: 200px;
  width: 100%;
  text-align: center;
}

/* 图表容器样式 */
.study-trends .recharts-responsive-container {
  margin: 0 auto;
  display: inline-block;
}

.trends-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 8px;
  height: 120px;
  padding: 16px 0;
}

.trend-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.trend-date {
  font-size: 11px;
  color: var(--semi-color-text-2);
  margin-bottom: 8px;
  white-space: nowrap;
}

.trend-bar {
  width: 100%;
  height: 60px;
  background: var(--semi-color-fill-0);
  border-radius: 4px 4px 0 0;
  position: relative;
  display: flex;
  align-items: flex-end;
  margin-bottom: 4px;
}

.trend-progress {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  min-height: 2px;
}

.trend-value {
  font-size: 11px;
  font-weight: 600;
  color: var(--semi-color-text-0);
  margin-bottom: 2px;
}

.trend-accuracy {
  font-size: 10px;
  color: var(--semi-color-text-2);
}

/* 趋势图表动画 */
.trend-progress {
  animation: trendGrow 0.8s ease-out;
}

@keyframes trendGrow {
  from {
    height: 0;
  }
  to {
    height: var(--final-height, 100%);
  }
}
