/* 考公刷题系统 - 排行榜页面 (手绘温馨风格) */
.global-ranking-page {
  min-height: 100vh;
  background:
    /* 纸质斑点纹理 - 增强对比度 */
    radial-gradient(circle at 20% 50%, rgba(237, 137, 54, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 80% 20%, rgba(66, 153, 225, 0.08) 0%, transparent 25%),
    radial-gradient(circle at 40% 80%, rgba(72, 187, 120, 0.06) 0%, transparent 25%),
    radial-gradient(circle at 60% 30%, rgba(159, 122, 234, 0.06) 0%, transparent 25%),
    /* 纸质细纹理 - 增强可见度 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(120, 119, 108, 0.025) 2px,
      rgba(120, 119, 108, 0.025) 4px
    ),
    repeating-linear-gradient(
      -45deg,
      transparent,
      transparent 3px,
      rgba(120, 119, 108, 0.015) 3px,
      rgba(120, 119, 108, 0.015) 6px
    ),
    /* 纸质基础渐变 - 使用正确的变量 */
    linear-gradient(135deg, #fefdf8 0%, #faf9f4 50%, #f7f6f0 100%);
  position: relative;
}

/* 纸质背景容器 - 增强手绘效果 */
.ranking-paper-container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 90px 24px 32px;
  position: relative;
  background:
    /* 纸质水印效果 - 增强对比度 */
    radial-gradient(circle at 15% 25%, rgba(237, 137, 54, 0.05) 0%, transparent 35%),
    radial-gradient(circle at 85% 75%, rgba(66, 153, 225, 0.05) 0%, transparent 35%),
    /* 纸质纹理细节 - 增强可见度 */
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 1px,
      rgba(120, 119, 108, 0.03) 1px,
      rgba(120, 119, 108, 0.03) 2px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(120, 119, 108, 0.02) 2px,
      rgba(120, 119, 108, 0.02) 4px
    ),
    /* 纸质基础 - 使用具体颜色值 */
    #faf9f4;
  border-radius: 12px;
  box-shadow:
    /* 纸张立体阴影 - 增强效果 */
    0 6px 12px rgba(0, 0, 0, 0.08),
    0 16px 32px rgba(0, 0, 0, 0.06),
    /* 内部高光 */
    inset 0 2px 0 rgba(255, 255, 255, 0.7),
    inset 0 -2px 0 rgba(120, 119, 108, 0.08);
  border: 2px solid rgba(120, 119, 108, 0.15);
  /* 轻微的纸张弯曲效果 */
  transform: perspective(1000px) rotateX(0.5deg);
  /* 确保在背景层之上 */
  position: relative;
  z-index: 2;
}

/* 纸质装饰和动画效果 */
.paper-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.paper-corner {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(120, 119, 108, 0.15);
  border-radius: 50%;
  opacity: 0.1;
  animation: paperFloat 6s ease-in-out infinite;
}

/* 增强纸质背景可见度 */
.global-ranking-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    /* 更明显的纸质斑点 */
    radial-gradient(circle at 25% 25%, rgba(237, 137, 54, 0.06) 0%, transparent 20%),
    radial-gradient(circle at 75% 75%, rgba(66, 153, 225, 0.06) 0%, transparent 20%),
    radial-gradient(circle at 75% 25%, rgba(72, 187, 120, 0.04) 0%, transparent 20%),
    radial-gradient(circle at 25% 75%, rgba(159, 122, 234, 0.04) 0%, transparent 20%);
  pointer-events: none;
  z-index: 1;
}

/* 纸质背景动画 */
@keyframes paperFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: translateY(-3px) rotate(1deg);
    opacity: 0.15;
  }
}

/* 页面加载动画 */
.global-ranking-page {
  animation: pageLoad 0.8s ease-out;
}

@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.paper-corner.top-left { top: 24px; left: 24px; }
.paper-corner.top-right { top: 24px; right: 24px; }
.paper-corner.bottom-left { bottom: 24px; left: 24px; }
.paper-corner.bottom-right { bottom: 24px; right: 24px; }

/* 优化标题区域 - 支持水平布局 */
.handwritten-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 20px;
}

.title-section {
  flex: 1;
  display: flex;
  justify-content: center;
}

.title-doodle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.doodle-star, .doodle-trophy {
  font-size: 1.5rem;
  animation: float 4s ease-in-out infinite;
}

.doodle-trophy {
  animation-delay: 2s;
}

.handwritten-title {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--accent-blue);
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.08);
  transform: rotate(-0.5deg);
}

.handwritten-subtitle {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  color: var(--ink-medium);
  margin: 8px 0 16px;
  transform: rotate(0.2deg);
}

.title-underline {
  width: 160px;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-orange), transparent);
  margin: 0 auto 20px;
  border-radius: 1px;
  transform: rotate(-0.2deg);
}

/* sketch-button 风格刷新按钮 - 右侧位置优化 */
.handwritten-refresh-btn {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 20px;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  font-family: var(--font-handwritten);
  color: var(--ink-dark);
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-shrink: 0;
  white-space: nowrap;
  box-shadow: 2px 2px 0px rgba(45, 55, 72, 0.1);
}

.handwritten-refresh-btn:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 3px 3px 0px rgba(45, 55, 72, 0.15);
  background: var(--accent-blue);
  color: white;
}

.handwritten-refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: rotate(-1deg);
}

.handwritten-refresh-btn .spinning {
  animation: spin 1s linear infinite;
}

/* 简化筛选区域 */
.ranking-filters-section {
  margin-bottom: 28px;
  text-align: center;
  background: var(--paper-warm);
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid var(--ink-lighter);
  opacity: 0.95;
}

.filters-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.filter-emoji {
  font-size: 1.2rem;
  animation: float 5s ease-in-out infinite;
}

.filter-emoji:last-child {
  animation-delay: 2.5s;
}

.filters-title {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-lg);
  color: var(--ink-dark);
  margin: 0;
  transform: rotate(-0.2deg);
}

/* 主要内容区域 - 改为单列布局 */
.ranking-main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 8px;
}

/* 个人排名和排行榜的容器 */
.ranking-content-wrapper {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}

/* 优化sketch-card基础样式 */
.sketch-card {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 12px;
  transform: rotate(-0.3deg);
  box-shadow: 2px 2px 0px rgba(45, 55, 72, 0.1);
  transition: all 0.3s ease;
  padding: 18px;
}

.sketch-card:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 4px 4px 0px rgba(45, 55, 72, 0.15);
}

/* 个人排名卡片 - 减少旋转 */
.user-rank-card {
  transform: rotate(0.4deg);
  border-color: var(--accent-orange);
  background: var(--paper-warm);
}

.user-rank-card:hover {
  transform: rotate(0deg) translateY(-2px);
}

/* 排行榜表格卡片 - 突出主要内容 */
.ranking-table-card {
  transform: rotate(-0.2deg);
  border-color: var(--accent-purple);
  padding: 0;
  overflow: hidden;
  box-shadow: 3px 3px 0px rgba(159, 122, 234, 0.1);
}

.ranking-table-card:hover {
  transform: rotate(0deg) translateY(-1px);
  box-shadow: 5px 5px 0px rgba(159, 122, 234, 0.15);
}

/* 优化卡片头部样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px dashed var(--ink-lighter);
}

.header-emoji {
  font-size: 1.1rem;
  animation: float 6s ease-in-out infinite;
}

.card-title {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  color: var(--ink-dark);
  margin: 0;
  transform: rotate(-0.2deg);
  font-weight: 600;
}

/* 排行榜卡片头部 - 突出主要内容 */
.ranking-table-card .card-header {
  background: linear-gradient(135deg, var(--accent-purple), #a78bfa);
  color: white;
  padding: 16px 20px;
  margin: 0;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ranking-emoji {
  font-size: 1.3rem;
  animation: float 8s ease-in-out infinite;
}

.ranking-title {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-lg);
  color: white;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  transform: rotate(-0.3deg);
  font-weight: 700;
}

/* 优化便利贴样式 */
.sticky-note {
  background: linear-gradient(135deg, #fff59d 0%, #fff176 100%);
  border-radius: 6px;
  transform: rotate(1.5deg);
  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.sticky-note::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  border-left: 12px solid transparent;
  border-top: 12px solid #f57f17;
}

.period-note {
  padding: 5px 10px;
  font-family: var(--font-handwritten);
  font-weight: 600;
  font-size: var(--font-size-xs);
  color: var(--ink-dark);
  transform: rotate(-2deg);
}

/* 卡片内容区域 */
.card-body {
  background: var(--paper-warm);
  border-radius: 0 0 10px 10px;
}

/* 优化加载状态 */
.sketch-loading {
  padding: 40px 20px;
  text-align: center;
  background: var(--paper-cream);
  border-radius: 10px;
  margin: 16px;
  border: 1px dashed var(--ink-lighter);
}

.loading-doodle {
  position: relative;
  display: inline-block;
  margin-bottom: 12px;
}

.loading-emoji {
  position: absolute;
  top: -8px;
  right: -12px;
  font-size: 1rem;
  animation: bounce 2s ease-in-out infinite;
}

.loading-message {
  font-family: var(--font-handwritten);
  color: var(--ink-medium);
  margin: 0;
  font-size: var(--font-size-sm);
  transform: rotate(-0.3deg);
}

/* 优化底部便利贴 */
.footer-note {
  margin-top: 32px;
  padding: 16px;
  transform: rotate(0.8deg);
  max-width: 580px;
  margin-left: auto;
  margin-right: auto;
}

.note-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  position: relative;
}

.note-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 1px;
}

.note-text {
  flex: 1;
}

.note-main {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-sm);
  color: var(--ink-dark);
  margin: 0 0 4px;
  font-weight: 600;
}

.note-sub {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-xs);
  color: var(--ink-medium);
  margin: 0;
  line-height: 1.3;
}

/* 优化浮动装饰 */
.floating-emoji {
  position: absolute;
  font-size: 1rem;
  animation: float 6s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.4;
}

/* 优化动画效果 - 减少幅度和频率 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-6px) rotate(2deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-4px); }
  60% { transform: translateY(-2px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 优化响应式设计 */
@media (max-width: 1024px) {
  .ranking-content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .user-rank-card {
    order: 2;
    max-width: 400px;
    margin: 0 auto;
  }

  .ranking-table-card {
    order: 1;
  }

  /* 移动端标题区域优化 */
  .header-top-row {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .handwritten-refresh-btn {
    font-size: var(--font-size-xs);
    padding: 6px 12px;
  }

  .handwritten-title {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 768px) {
  .ranking-paper-container {
    margin: 8px;
    padding: 80px 16px 28px;
  }

  .handwritten-title {
    font-size: var(--font-size-2xl);
  }

  .sketch-card {
    transform: rotate(0deg);
    padding: 16px;
  }

  .sketch-card:hover {
    transform: translateY(-1px);
  }

  .ranking-table-card .card-header {
    padding: 14px 16px;
    flex-direction: column;
    gap: 6px;
    text-align: center;
  }

  .ranking-title {
    font-size: var(--font-size-base);
  }

  .filters-header {
    gap: 6px;
  }

  .filters-title {
    font-size: var(--font-size-base);
  }

  .ranking-filters-section {
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .ranking-paper-container {
    padding: 70px 12px 24px;
  }

  .handwritten-title {
    font-size: var(--font-size-xl);
  }

  .sketch-card {
    padding: 14px;
  }

  .footer-note {
    padding: 12px;
    margin-top: 24px;
  }

  .note-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .ranking-main-content {
    gap: 16px;
  }
}

/* 排行榜表格优化 - 修复宽度和样式 */
.ranking-table-card .semi-table {
  background: transparent;
  width: 100% !important;
}

.ranking-table-card .semi-table-wrapper {
  width: 100% !important;
  overflow-x: auto;
}

.ranking-table-card .semi-table-thead {
  background: var(--paper-cream);
}

.ranking-table-card .semi-table-thead th {
  background: var(--paper-cream);
  border-bottom: 2px solid var(--ink-lighter);
  font-family: var(--font-handwritten);
  font-weight: 600;
  color: var(--ink-dark);
  white-space: nowrap;
}

.ranking-table-card .semi-table-tbody tr {
  transition: background-color 0.2s ease;
}

.ranking-table-card .semi-table-tbody tr:hover {
  background: rgba(66, 153, 225, 0.05);
}

.ranking-table-card .semi-table-tbody td {
  border-bottom: 1px solid var(--ink-lighter);
  padding: 12px 16px;
}

/* 强制显示表头并修复宽度 */
.ranking-table thead,
.ranking-table .semi-table-thead {
  display: table-header-group !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ranking-table-card .card-body {
  padding: 0;
  overflow: hidden;
}

/* 修复表格容器宽度 */
.ranking-table-card .semi-table-container {
  width: 100% !important;
}

/* 手绘温馨风格排行榜页面样式完成 ✨ */
