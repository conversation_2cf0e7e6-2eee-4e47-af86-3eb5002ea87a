import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Typography } from '@douyinfe/semi-ui';
import { IconArrowLeft } from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;

interface ComingSoonProps {
  title: string;
  description: string;
  icon: string;
}

export const ComingSoon: React.FC<ComingSoonProps> = ({ title, description, icon }) => {
  const navigate = useNavigate();

  return (
    <>
      <Navigation />
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
        paddingTop: '80px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'var(--font-system)'
      }}>
        <div className="sketch-card" style={{
          maxWidth: '500px',
          padding: '40px',
          textAlign: 'center',
          transform: 'rotate(-0.5deg)'
        }}>
          <div style={{ fontSize: '64px', marginBottom: '20px' }}>
            {icon}
          </div>
          <Title level={2} className="handwritten-title large" style={{ marginBottom: '16px' }}>
            {title}
          </Title>
          <Text style={{ 
            fontSize: '16px', 
            color: 'var(--ink-medium)',
            marginBottom: '30px',
            display: 'block'
          }}>
            {description}
          </Text>
          <div style={{ marginBottom: '20px' }}>
            <Text style={{ 
              fontSize: '14px', 
              color: 'var(--ink-light)',
              fontStyle: 'italic'
            }}>
              功能正在紧张开发中，敬请期待... 🚀
            </Text>
          </div>
          <Button 
            type="primary"
            icon={<IconArrowLeft />}
            onClick={() => navigate('/dashboard')}
            className="sketch-button primary"
            style={{
              borderRadius: '25px',
              padding: '12px 24px',
              fontFamily: 'var(--font-handwritten)',
              fontWeight: '600'
            }}
          >
            返回首页
          </Button>
        </div>
      </div>
    </>
  );
};

export default ComingSoon;
