#!/bin/bash

# 清除排行榜Redis缓存脚本
# 使用方法: ./clear_ranking_cache.sh [redis_host] [redis_port] [redis_password]

REDIS_HOST=${1:-localhost}
REDIS_PORT=${2:-6379}
REDIS_PASSWORD=${3:-""}

echo "开始清除排行榜缓存..."
echo "Redis服务器: ${REDIS_HOST}:${REDIS_PORT}"

# 构建redis-cli命令
if [ -n "$REDIS_PASSWORD" ]; then
    REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD"
else
    REDIS_CMD="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
fi

echo "正在查找排行榜相关缓存..."

# 查找并删除排行榜相关的缓存键
echo "清除 ranking:global 缓存..."
$REDIS_CMD --scan --pattern "ranking:global*" | xargs -r $REDIS_CMD del

echo "清除 ranking:user 缓存..."
$REDIS_CMD --scan --pattern "ranking:user*" | xargs -r $REDIS_CMD del

echo "清除 ranking:stats 缓存..."
$REDIS_CMD --scan --pattern "ranking:stats*" | xargs -r $REDIS_CMD del

echo "验证缓存清除结果..."
GLOBAL_COUNT=$($REDIS_CMD --scan --pattern "ranking:global*" | wc -l)
USER_COUNT=$($REDIS_CMD --scan --pattern "ranking:user*" | wc -l)
STATS_COUNT=$($REDIS_CMD --scan --pattern "ranking:stats*" | wc -l)

echo "剩余缓存统计:"
echo "  ranking:global: $GLOBAL_COUNT 个"
echo "  ranking:user: $USER_COUNT 个"
echo "  ranking:stats: $STATS_COUNT 个"

if [ $((GLOBAL_COUNT + USER_COUNT + STATS_COUNT)) -eq 0 ]; then
    echo "✅ 排行榜缓存清除完成！"
else
    echo "⚠️  仍有部分缓存未清除，请检查"
fi

echo "缓存清除脚本执行完成"
