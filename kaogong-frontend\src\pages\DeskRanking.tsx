import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Avatar,
  Table,
  Select,
  Spin,
  Toast,
  Progress,
  Row,
  Col
} from '@douyinfe/semi-ui';
import {
  IconUser,
  IconRankingCardStroked,
  IconArrowLeft,
  IconCalendar,
  IconTarget,
  IconCrown,
  IconStar
} from '@douyinfe/semi-icons';
import { useParams, useNavigate } from 'react-router-dom';
import {
  deskService,
  DeskResponse,
  DeskRankingResponse
} from '../services/deskService';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 小桌排行榜页面
 * 显示小桌成员的学习排行榜，支持不同时间周期
 */
const DeskRanking: React.FC = () => {
  const { deskId } = useParams<{ deskId: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const [desk, setDesk] = useState<DeskResponse | null>(null);
  const [ranking, setRanking] = useState<DeskRankingResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [period, setPeriod] = useState('week');

  // 时间周期选项
  const periodOptions = [
    { value: 'day', label: '今日' },
    { value: 'week', label: '本周' },
    { value: 'month', label: '本月' },
    { value: 'all', label: '全部' }
  ];

  // 加载小桌信息
  const loadDeskInfo = async () => {
    if (!deskId) return;
    
    try {
      const response = await deskService.getDeskById(deskId);
      setDesk(response);
    } catch (error: any) {
      console.error('加载小桌信息失败:', error);
      Toast.error(error.message || '加载小桌信息失败');
    }
  };

  // 加载排行榜
  const loadRanking = async (selectedPeriod = period) => {
    if (!deskId) return;
    
    try {
      setLoading(true);
      const response = await deskService.getDeskRanking(deskId, selectedPeriod);
      setRanking(response);
    } catch (error: any) {
      console.error('加载排行榜失败:', error);
      Toast.error(error.message || '加载排行榜失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换时间周期
  const handlePeriodChange = (newPeriod: string) => {
    setPeriod(newPeriod);
    loadRanking(newPeriod);
  };

  // 获取排名图标
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <IconCrown style={{ color: '#FFD700' }} />;
      case 2:
        return <IconStar style={{ color: '#C0C0C0' }} />;
      case 3:
        return <IconStar style={{ color: '#CD7F32' }} />;
      default:
        return <Text strong>{rank}</Text>;
    }
  };

  // 获取排名颜色
  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#FFD700';
      case 2:
        return '#C0C0C0';
      case 3:
        return '#CD7F32';
      default:
        return '#666';
    }
  };

  // 排行榜表格列定义
  const columns = [
    {
      title: '排名',
      dataIndex: 'rank',
      width: 80,
      render: (rank: number) => (
        <div style={{ textAlign: 'center', color: getRankColor(rank) }}>
          {getRankIcon(rank)}
        </div>
      )
    },
    {
      title: '成员',
      render: (record: DeskRankingResponse) => (
        <Space>
          <Avatar size="small">
            <IconUser />
          </Avatar>
          <div>
            <Text>{record.nickname || record.username}</Text>
            {record.isOwner && <Tag color="blue" size="small" style={{ marginLeft: '8px' }}>桌长</Tag>}
          </div>
        </Space>
      )
    },
    {
      title: '信誉分数',
      dataIndex: 'reputationScore',
      width: 100,
      render: (score: number) => (
        <Tag color="orange">{score}分</Tag>
      )
    },
    {
      title: '学习天数',
      dataIndex: 'studyDays',
      width: 100,
      render: (days: number) => (
        <Text>{days}天</Text>
      )
    },
    {
      title: '完成题目',
      dataIndex: 'totalQuestions',
      width: 100,
      render: (questions: number) => (
        <Text>{questions}道</Text>
      )
    },
    {
      title: '正确率',
      dataIndex: 'accuracyRate',
      width: 120,
      render: (rate: number) => (
        <div>
          <Progress 
            percent={rate} 
            showInfo={false} 
            size="small"
            stroke={rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f'}
          />
          <Text size="small">{rate.toFixed(1)}%</Text>
        </div>
      )
    },
    {
      title: '综合得分',
      dataIndex: 'totalScore',
      width: 120,
      render: (score: number) => (
        <Text strong style={{ color: '#1890ff' }}>
          {score.toFixed(1)}
        </Text>
      )
    }
  ];

  // 初始化加载
  useEffect(() => {
    loadDeskInfo();
    loadRanking();
  }, [deskId]);

  if (!desk) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <Navigation />
      <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto', paddingTop: '104px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<IconArrowLeft />} 
            onClick={() => navigate(`/desks/${deskId}/dashboard`)}
          >
            返回小桌
          </Button>
        </Space>
      </div>

      {/* 小桌信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title heading={3} style={{ margin: 0, marginBottom: '8px' }}>
              <IconRankingCardStroked style={{ marginRight: '8px', color: '#FFD700' }} />
              {desk.name} - 学习排行榜
            </Title>
            <Text type="secondary">{desk.description || '暂无描述'}</Text>
          </div>
          
          <div>
            <Space>
              <Text>统计周期：</Text>
              <Select 
                value={period} 
                onChange={handlePeriodChange}
                style={{ width: '120px' }}
              >
                {periodOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Space>
          </div>
        </div>
      </Card>

      {/* 前三名展示 */}
      {ranking.length >= 3 && (
        <Card style={{ marginBottom: '24px' }}>
          <Title heading={4} style={{ textAlign: 'center', marginBottom: '24px' }}>
            🏆 前三名
          </Title>
          <Row gutter={16}>
            {ranking.slice(0, 3).map((member, index) => (
              <Col span={8} key={member.userId}>
                <Card 
                  style={{ 
                    textAlign: 'center',
                    background: index === 0 ? '#fff7e6' : index === 1 ? '#f6f6f6' : '#fff2e8'
                  }}
                >
                  <div style={{ fontSize: '48px', marginBottom: '8px' }}>
                    {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                  </div>
                  <Title heading={5} style={{ margin: 0, marginBottom: '8px' }}>
                    {member.nickname || member.username}
                  </Title>
                  {member.isOwner && <Tag color="blue" size="small">桌长</Tag>}
                  <div style={{ marginTop: '12px' }}>
                    <Text strong style={{ fontSize: '20px', color: '#1890ff' }}>
                      {member.totalScore.toFixed(1)}分
                    </Text>
                  </div>
                  <div style={{ marginTop: '8px' }}>
                    <Space direction="vertical" spacing="tight">
                      <Text size="small">学习{member.studyDays}天</Text>
                      <Text size="small">完成{member.totalQuestions}道题</Text>
                      <Text size="small">正确率{member.accuracyRate.toFixed(1)}%</Text>
                    </Space>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* 完整排行榜 */}
      <Card title="完整排行榜">
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={ranking}
            rowKey="userId"
            pagination={false}
            size="small"
          />
        </Spin>
      </Card>

      {/* 排行榜说明 */}
      <Card style={{ marginTop: '24px' }}>
        <Title heading={5}>排行榜说明</Title>
        <div style={{ marginTop: '12px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <Space direction="vertical" spacing="tight">
                <Text strong>📊 综合得分计算公式：</Text>
                <Text size="small">题目数量 × 0.6 + 学习天数 × 10 + 正确率 × 0.4</Text>
                <Text size="small" type="secondary">
                  综合考虑学习量、坚持度和准确性
                </Text>
              </Space>
            </Col>
            <Col span={12}>
              <Space direction="vertical" spacing="tight">
                <Text strong>🏅 排名更新：</Text>
                <Text size="small">每天凌晨自动更新排名</Text>
                <Text size="small" type="secondary">
                  数据统计基于选择的时间周期
                </Text>
              </Space>
            </Col>
          </Row>
        </div>
      </Card>
      </div>
    </>
  );
};

export default DeskRanking;
