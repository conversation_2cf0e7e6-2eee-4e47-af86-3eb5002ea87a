# 考公刷题记录系统 - 数据库脚本说明

## 📁 文件结构

```
database/
├── database-design.md          # 数据库设计文档
├── database-schema.sql         # 完整建表脚本
├── database-indexes.sql        # 索引优化脚本
├── flyway-migrations/          # Flyway数据库迁移脚本
│   ├── V1__Create_initial_schema.sql
│   └── V2__Create_desk_and_other_modules.sql
└── README.md                   # 本说明文档
```

## 🚀 快速开始

### 1. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE kaogong_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE kaogong_db;
```

### 2. 执行建表脚本

```bash
# 方式1: 直接执行完整建表脚本
mysql -u root -p kaogong_db < database-schema.sql

# 方式2: 使用Flyway迁移（推荐）
flyway migrate
```

### 3. 执行索引优化（可选）

```bash
# 在数据量较大时执行索引优化
mysql -u root -p kaogong_db < database-indexes.sql
```

## 📊 数据库结构概览

### 表结构统计

- **总表数量**: 15 张
- **总索引数量**: 45 个（包括主键、唯一键、普通索引、全文索引）
- **外键约束**: 22 个

### 模块分布

| 模块        | 表数量 | 表名                                        |
| ----------- | ------ | ------------------------------------------- |
| 用户管理    | 2      | users, invite_codes                         |
| 刷题记录    | 3      | study_records, wrong_questions, study_plans |
| 小桌系统 🌟 | 3      | desks, desk_members, desk_applications      |
| 信誉系统 🌟 | 1      | reputation_logs                             |
| 排行榜      | 1      | rankings                                    |
| 考试公告    | 1      | announcements                               |
| 通知系统    | 1      | notifications                               |

## 🔧 技术特性

### MySQL 8.0 特性利用

- ✅ **JSON 字段**: 存储灵活配置和元数据
- ✅ **全文搜索**: ngram 分词器支持中文搜索
- ✅ **生成列**: 支持计算字段（预留）
- ✅ **窗口函数**: 支持复杂排行榜计算

### 性能优化设计

- ✅ **复合索引**: 针对高频查询场景优化
- ✅ **分区预留**: study_records 表支持按日期分区
- ✅ **外键约束**: 保证数据完整性
- ✅ **字符集**: utf8mb4 支持完整 Unicode

## 🔐 安全性设计

### 数据完整性

- **主键约束**: 所有表都有 UUID 主键
- **外键约束**: 确保引用完整性
- **唯一约束**: 防止重复数据
- **非空约束**: 关键字段不允许为空

### 权限管理

- **角色字段**: users.system_role 支持三级权限
- **软删除**: 重要数据支持逻辑删除
- **审计字段**: created_at, updated_at 跟踪变更

## 📈 扩展性考虑

### 水平扩展

- **UUID 主键**: 支持分布式部署
- **分区设计**: 大表支持分区存储
- **读写分离**: 索引设计支持读写分离

### 垂直扩展

- **JSON 字段**: 灵活存储扩展属性
- **元数据字段**: 预留扩展空间
- **模块化设计**: 支持功能模块独立扩展

## 🛠️ 开发集成

### MyBatis Plus 集成

```yaml
# application.yml
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### Spring Boot 配置

```yaml
# application.yml
spring:
  datasource:
    url: **************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
```

### Flyway 配置

```properties
# flyway.conf
flyway.url=**************************************
flyway.user=root
flyway.password=password
flyway.locations=filesystem:database/flyway-migrations
flyway.baselineOnMigrate=true
```

## 📋 维护建议

### 定期维护任务

#### 每日任务

- 监控数据库连接数和慢查询
- 检查错误日志

#### 每周任务

```sql
-- 分析表统计信息
ANALYZE TABLE users, study_records, desks, rankings;

-- 检查表大小
SELECT
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size_MB',
    TABLE_ROWS
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'kaogong_db'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;
```

#### 每月任务

- 检查索引使用情况和碎片率
- 清理过期数据（如过期邀请码）
- 备份数据库

### 性能监控

```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看索引使用统计
SELECT
    OBJECT_NAME,
    INDEX_NAME,
    COUNT_FETCH,
    COUNT_INSERT,
    COUNT_UPDATE
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE OBJECT_SCHEMA = 'kaogong_db'
ORDER BY COUNT_FETCH DESC;
```

## 🚨 注意事项

### 重要提醒

1. **备份**: 执行脚本前请备份现有数据
2. **权限**: 确保数据库用户有足够权限
3. **字符集**: 必须使用 utf8mb4 字符集
4. **时区**: 建议设置为 Asia/Shanghai
5. **版本**: 需要 MySQL 8.0+版本

### 常见问题

1. **全文搜索不生效**: 检查 ngram_token_size 设置
2. **外键约束失败**: 检查表创建顺序
3. **字符集问题**: 确保连接字符集为 utf8mb4
4. **JSON 字段报错**: 确保 MySQL 版本支持 JSON

---

## 🌟 信誉系统专项说明

### **重要更新**: 信誉系统数据库结构

#### **核心文件**

1. **`database-schema.sql`** - 主架构文件（包含信誉系统表）
2. **`reputation-system-init.sql`** - 信誉系统初始化脚本

#### **执行顺序**

```bash
# 1. 创建完整数据库结构（包含信誉系统表）
mysql -u username -p kaogong_db < database-schema.sql

# 2. 初始化信誉系统数据和功能
mysql -u username -p kaogong_db < reputation-system-init.sql
```

#### **信誉系统表结构**

- `reputation_logs` - 信誉记录表
- `user_reputation_stats` - 用户信誉统计表 ✅ 已修复 protection_end_time 字段问题
- `scheduled_task_logs` - 定时任务记录表

#### **关键修复**

- ✅ 解决了 `protection_end_time` 字段的 MySQL 默认值错误
- ✅ 字段定义: `TIMESTAMP NULL DEFAULT NULL`
- ✅ 支持 3 天新用户保护期机制

#### **验证方法**

```sql
-- 检查信誉系统表
SHOW TABLES LIKE '%reputation%';
SHOW TABLES LIKE '%scheduled%';

-- 验证字段定义
DESCRIBE user_reputation_stats;

-- 查看信誉概览
SELECT * FROM v_user_reputation_overview LIMIT 5;
```

---

## 📞 技术支持

如有问题，请参考：

1. 数据库设计文档: `database-design.md`
2. 信誉系统设计: `doc/reputation-system-design.md`
3. 系统架构文档: `doc/system-architecture.md`
4. 开发规范文档: `doc/development-standards.md`
