/* 错题管理页面样式 */
.wrong-questions-page {
  min-height: 100vh;
  background: var(--paper-bg);
}

.wrong-questions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 80px 24px 40px 24px;
}

/* 页面头部 */
.wrong-questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 0;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title .semi-icon {
  color: var(--accent-blue);
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 20px;
  border: 2px solid var(--ink-dark);
  border-radius: 12px;
  box-shadow: 4px 4px 0px var(--ink-dark);
  background: var(--paper-white);
}

.filter-card .semi-card-body {
  padding: 20px 24px;
}

/* 筛选器网格布局 */
.filter-card .semi-select {
  border: 2px solid var(--ink-dark);
  border-radius: 8px;
  font-family: var(--font-handwritten);
}

.filter-card .semi-select:hover {
  border-color: var(--accent-blue);
  transform: translateY(-1px);
}

.filter-card .semi-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 错题列表卡片 */
.questions-card {
  border: 2px solid var(--ink-dark);
  border-radius: 12px;
  box-shadow: 4px 4px 0px var(--ink-dark);
  background: var(--paper-white);
  min-height: 400px;
}

.questions-card .semi-card-body {
  padding: 0;
}

/* 表格样式 */
.questions-card .semi-table {
  border: none;
}

.questions-card .semi-table-thead > tr > th {
  background: var(--paper-bg);
  border-bottom: 2px solid var(--ink-dark);
  font-family: var(--font-handwritten);
  font-weight: 600;
  color: var(--ink-dark);
}

.questions-card .semi-table-tbody > tr {
  border-bottom: 1px solid var(--ink-light);
}

.questions-card .semi-table-tbody > tr:hover {
  background: var(--paper-bg);
}

.questions-card .semi-table-tbody > tr > td {
  padding: 12px 16px;
  vertical-align: middle;
}

/* 题目内容列样式优化 */
.questions-card .semi-table-tbody > tr > td:nth-child(3) {
  max-width: 250px;
  overflow: hidden;
}

/* 掌握状态列样式优化 */
.questions-card .semi-table-tbody > tr > td:nth-child(4) .semi-select {
  /* width: 100% !important; */
  min-width: 120px;
}

/* 操作列样式优化 */
.questions-card .semi-table-tbody > tr > td:last-child {
  text-align: center;
  white-space: nowrap;
}

.questions-card .semi-table-tbody > tr > td:last-child .semi-button {
  margin: 0 2px;
  min-width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 表格滚动条样式 */
.questions-card .semi-table-container::-webkit-scrollbar {
  height: 8px;
}

.questions-card .semi-table-container::-webkit-scrollbar-track {
  background: var(--paper-bg);
  border-radius: 4px;
}

.questions-card .semi-table-container::-webkit-scrollbar-thumb {
  background: var(--ink-light);
  border-radius: 4px;
  border: 1px solid var(--ink-dark);
}

.questions-card .semi-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
}

/* 分页容器 */
.pagination-container {
  padding: 20px;
  border-top: 1px solid var(--ink-light);
  display: flex;
  justify-content: center;
  background: var(--paper-bg);
}

/* 标签样式优化 */
.questions-card .semi-tag {
  border: 1px solid var(--ink-dark);
  font-family: var(--font-handwritten);
  font-weight: 500;
}

/* 选择器样式 */
.questions-card .semi-select {
  border: 1px solid var(--ink-dark);
  border-radius: 6px;
}

.questions-card .semi-select:hover {
  border-color: var(--accent-blue);
}

/* 按钮样式 */
.questions-card .semi-button {
  border: 1px solid var(--ink-dark);
  border-radius: 6px;
  font-family: var(--font-handwritten);
  font-weight: 500;
}

.questions-card .semi-button:hover {
  transform: translateY(-1px);
  box-shadow: 2px 2px 0px var(--ink-dark);
}

/* 详情模态框样式 */
.question-detail-modal .semi-modal-content {
  border: 2px solid var(--ink-dark);
  border-radius: 12px;
  box-shadow: 6px 6px 0px var(--ink-dark);
}

.question-detail-modal .semi-modal-header {
  border-bottom: 2px solid var(--ink-dark);
  background: var(--paper-bg);
  font-family: var(--font-handwritten);
  font-weight: 600;
}

.question-detail {
  padding: 20px;
}

.detail-header {
  margin-bottom: 16px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section .semi-typography-paragraph {
  margin-top: 8px;
  padding: 12px;
  background: var(--paper-bg);
  border: 1px solid var(--ink-light);
  border-radius: 8px;
  border-left: 4px solid var(--accent-blue);
}

.detail-section .semi-typography-paragraph.semi-typography-danger {
  border-left-color: var(--semi-color-danger);
  background: rgba(var(--semi-red-0), 0.1);
}

.detail-section .semi-typography-paragraph.semi-typography-success {
  border-left-color: var(--semi-color-success);
  background: rgba(var(--semi-green-0), 0.1);
}

/* 空状态样式 */
.questions-card .semi-empty {
  padding: 60px 20px;
}

.questions-card .semi-empty .semi-icon {
  color: var(--ink-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wrong-questions-container {
    padding: 60px 16px 24px 16px;
  }

  .wrong-questions-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-card .semi-card-body {
    padding: 12px 16px;
  }

  .filter-card .semi-space {
    flex-wrap: wrap;
    gap: 8px;
  }

  /* 移动端表格优化 */
  .questions-card .semi-table {
    font-size: 12px;
  }

  .questions-card .semi-table-tbody > tr > td {
    padding: 8px 12px;
  }

  /* 在中等屏幕上隐藏复习次数和创建时间列 */
  .questions-card .semi-table-tbody > tr > td:nth-child(5), /* 复习次数 */
  .questions-card .semi-table-thead > tr > th:nth-child(5) {
    display: none;
  }

  .questions-card .semi-table-tbody > tr > td:nth-child(6), /* 创建时间 */
  .questions-card .semi-table-thead > tr > th:nth-child(6) {
    display: none;
  }

  /* 题目内容列在中等屏幕上进一步缩小 */
  .questions-card .semi-table-tbody > tr > td:nth-child(3) {
    max-width: 180px;
  }

  .pagination-container {
    padding: 16px;
  }

  .pagination-container .semi-pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .wrong-questions-container {
    padding: 50px 12px 20px 12px;
  }

  .questions-card .semi-table {
    font-size: 11px;
  }

  .questions-card .semi-table-tbody > tr > td {
    padding: 6px 8px;
  }

  /* 在小屏幕上进一步隐藏列，只保留核心信息 */
  .questions-card .semi-table-tbody > tr > td:nth-child(1), /* 学习模块 */
  .questions-card .semi-table-thead > tr > th:nth-child(1) {
    display: none;
  }

  .questions-card .semi-table-tbody > tr > td:nth-child(2), /* 难度 */
  .questions-card .semi-table-thead > tr > th:nth-child(2) {
    display: none;
  }

  /* 题目内容列在小屏幕上进一步缩小 */
  .questions-card .semi-table-tbody > tr > td:nth-child(3) {
    max-width: 120px;
  }

  /* 操作按钮在小屏幕上只显示图标 */
  .questions-card .semi-table-tbody > tr > td:last-child .semi-button {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .question-detail-modal .semi-modal-content {
    margin: 10px;
    width: calc(100vw - 20px);
  }

  .detail-section .semi-typography-paragraph {
    padding: 8px;
    font-size: 14px;
  }
}

/* 加载状态优化 */
.questions-card .semi-spin-wrapper {
  min-height: 300px;
}

/* 工具提示样式 */
.semi-tooltip {
  max-width: 400px;
}

/* 选择器下拉样式 */
.semi-select-option {
  font-family: var(--font-handwritten);
}

.semi-select-option:hover {
  background: var(--paper-bg);
}

/* 分割线样式 */
.question-detail .semi-divider {
  border-color: var(--ink-light);
  margin: 16px 0;
}
