import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Tag,
  Avatar,
  Table,
  Tabs,
  Toast,
  Modal,
  Descriptions,
  Empty,
  Spin,
  Popconfirm
} from '@douyinfe/semi-ui';
import {
  IconUser,
  IconUserGroup,
  IconRankingCardStroked,
  IconSetting,
  IconDelete,
  IconArrowLeft,
  IconCrown,
  IconCalendar,
  IconTickCircle,
  IconClose
} from '@douyinfe/semi-icons';
import { useParams, useNavigate } from 'react-router-dom';
import {
  deskService,
  DeskResponse,
  DeskMemberResponse,
  DeskApplicationResponse
} from '../services/deskService';
import Navigation from '../components/Navigation';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 小桌管理面板
 * 提供成员管理、申请审核、排行榜等功能
 */
const DeskDashboard: React.FC = () => {
  const { deskId } = useParams<{ deskId: string }>();
  const navigate = useNavigate();
  
  // 状态管理
  const [desk, setDesk] = useState<DeskResponse | null>(null);
  const [members, setMembers] = useState<DeskMemberResponse[]>([]);
  const [applications, setApplications] = useState<DeskApplicationResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('members');

  // 加载小桌信息
  const loadDeskInfo = async () => {
    if (!deskId) return;
    
    try {
      const response = await deskService.getDeskById(deskId);
      setDesk(response);
    } catch (error: any) {
      console.error('加载小桌信息失败:', error);
      Toast.error(error.message || '加载小桌信息失败');
    }
  };

  // 加载成员列表
  const loadMembers = async () => {
    if (!deskId) return;
    
    try {
      setLoading(true);
      const response = await deskService.getDeskMembers(deskId);
      setMembers(response);
    } catch (error: any) {
      console.error('加载成员列表失败:', error);
      Toast.error(error.message || '加载成员列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载申请列表
  const loadApplications = async () => {
    if (!deskId) return;
    
    try {
      setLoading(true);
      const response = await deskService.getApplicationsByDesk(deskId);
      setApplications(response);
    } catch (error: any) {
      console.error('加载申请列表失败:', error);
      Toast.error(error.message || '加载申请列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 移除成员
  const handleRemoveMember = async (memberId: string, memberName: string) => {
    if (!deskId) return;
    
    try {
      await deskService.removeMember(deskId, memberId);
      Toast.success(`已移除成员 ${memberName}`);
      loadMembers(); // 重新加载成员列表
      loadDeskInfo(); // 重新加载小桌信息
    } catch (error: any) {
      Toast.error(error.message || '移除成员失败');
    }
  };

  // 转让桌长
  const handleTransferOwnership = async (memberId: string, memberName: string) => {
    if (!deskId) return;
    
    Modal.confirm({
      title: '确认转让桌长',
      content: `确定要将桌长权限转让给 ${memberName} 吗？转让后您将成为普通成员。`,
      onOk: async () => {
        try {
          await deskService.updateMemberRole(deskId, memberId, 'owner');
          Toast.success(`桌长权限已转让给 ${memberName}`);
          loadMembers();
          loadDeskInfo();
        } catch (error: any) {
          Toast.error(error.message || '转让桌长失败');
        }
      }
    });
  };

  // 处理申请
  const handleProcessApplication = async (applicationId: string, action: 'approved' | 'rejected', applicantName: string) => {
    try {
      await deskService.processApplication(applicationId, { action });
      Toast.success(`已${action === 'approved' ? '通过' : '拒绝'} ${applicantName} 的申请`);
      loadApplications(); // 重新加载申请列表
      if (action === 'approved') {
        loadMembers(); // 如果通过申请，重新加载成员列表
        loadDeskInfo(); // 重新加载小桌信息
      }
    } catch (error: any) {
      Toast.error(error.message || '处理申请失败');
    }
  };

  // 离开小桌
  const handleLeaveDesk = () => {
    if (!deskId) return;
    
    Modal.confirm({
      title: '确认离开小桌',
      content: '确定要离开这个小桌吗？离开后需要重新申请才能加入。',
      onOk: async () => {
        try {
          await deskService.leaveDesk(deskId);
          Toast.success('已离开小桌');
          navigate('/my-desks');
        } catch (error: any) {
          Toast.error(error.message || '离开小桌失败');
        }
      }
    });
  };

  // 查看排行榜
  const handleViewRanking = () => {
    navigate(`/desks/${deskId}/ranking`);
  };

  // 标签页切换
  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
    if (tabKey === 'members') {
      loadMembers();
    } else if (tabKey === 'applications') {
      loadApplications();
    }
  };

  // 成员表格列定义
  const memberColumns = [
    {
      title: '成员',
      dataIndex: 'nickname',
      render: (text: string, record: DeskMemberResponse) => (
        <Space>
          <Avatar size="small">
            <IconUser />
          </Avatar>
          <div>
            <Text>{text || record.username}</Text>
            {record.isOwner && <Tag color="blue" size="small" style={{ marginLeft: '8px' }}>桌长</Tag>}
          </div>
        </Space>
      )
    },
    {
      title: '信誉等级',
      dataIndex: 'reputationLevel',
      render: (text: string, record: DeskMemberResponse) => (
        <Space>
          <Tag color="orange">{text}</Tag>
          <Text size="small" type="secondary">{record.reputationScore}分</Text>
        </Space>
      )
    },
    {
      title: '加入时间',
      dataIndex: 'joinedAt',
      render: (text: string) => new Date(text).toLocaleDateString()
    },
    {
      title: '最近活跃',
      dataIndex: 'lastActiveAt',
      render: (text: string) => text ? new Date(text).toLocaleDateString() : '从未活跃'
    },
    {
      title: '学习情况',
      render: (record: DeskMemberResponse) => (
        <div>
          <Text size="small">最近{record.recentStudyDays}天学习</Text>
          <br />
          <Text size="small" type="secondary">完成{record.recentQuestions}道题</Text>
        </div>
      )
    }
  ];

  // 如果是桌长，添加操作列
  if (desk?.isOwner) {
    memberColumns.push({
      title: '操作',
      render: (record: DeskMemberResponse) => (
        <Space>
          {!record.isOwner && (
            <>
              <Button 
                size="small" 
                icon={<IconCrown />}
                onClick={() => handleTransferOwnership(record.userId, record.nickname || record.username)}
              >
                转让桌长
              </Button>
              <Popconfirm
                title="确定要移除这个成员吗？"
                onConfirm={() => handleRemoveMember(record.userId, record.nickname || record.username)}
              >
                <Button 
                  size="small" 
                  type="danger"
                  icon={<IconDelete />}
                >
                  移除
                </Button>
              </Popconfirm>
            </>
          )}
        </Space>
      )
    } as any);
  }

  // 申请表格列定义
  const applicationColumns = [
    {
      title: '申请者',
      render: (record: DeskApplicationResponse) => (
        <Space>
          <Avatar size="small">
            <IconUser />
          </Avatar>
          <div>
            <Text>{record.nickname || record.username}</Text>
            <br />
            <Tag color="orange" size="small">{record.reputationLevel}</Tag>
            <Text size="small" type="secondary" style={{ marginLeft: '8px' }}>
              {record.reputationScore}分
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '申请理由',
      dataIndex: 'reason',
      render: (text: string) => (
        <Text style={{ maxWidth: '200px', display: 'block' }} ellipsis={{ showTooltip: true }}>
          {text}
        </Text>
      )
    },
    {
      title: '学习计划',
      dataIndex: 'studyPlan',
      render: (text: string) => (
        <Text style={{ maxWidth: '200px', display: 'block' }} ellipsis={{ showTooltip: true }}>
          {text || '未填写'}
        </Text>
      )
    },
    {
      title: '申请时间',
      dataIndex: 'appliedAt',
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (status: string, record: DeskApplicationResponse) => {
        if (record.isPending) return <Tag color="orange">待审核</Tag>;
        if (record.isApproved) return <Tag color="green">已通过</Tag>;
        if (record.isRejected) return <Tag color="red">已拒绝</Tag>;
        return <Tag>未知</Tag>;
      }
    }
  ];

  // 如果是桌长且申请待处理，添加操作列
  if (desk?.isOwner) {
    applicationColumns.push({
      title: '操作',
      render: (record: DeskApplicationResponse) => (
        record.isPending ? (
          <Space>
            <Button
              size="small"
              type="primary"
              icon={<IconTickCircle />}
              onClick={() => handleProcessApplication(record.id, 'approved', record.nickname || record.username)}
            >
              通过
            </Button>
            <Button
              size="small"
              type="danger"
              icon={<IconClose />}
              onClick={() => handleProcessApplication(record.id, 'rejected', record.nickname || record.username)}
            >
              拒绝
            </Button>
          </Space>
        ) : null
      )
    } as any);
  }

  // 初始化加载
  useEffect(() => {
    loadDeskInfo();
    loadMembers();
  }, [deskId]);

  // 当确认是桌长时，加载申请列表
  useEffect(() => {
    if (desk?.isOwner) {
      loadApplications();
    }
  }, [desk?.isOwner]);

  if (!desk) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <Navigation />
      <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto', paddingTop: '104px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<IconArrowLeft />} 
            onClick={() => navigate('/my-desks')}
          >
            返回我的小桌
          </Button>
        </Space>
      </div>

      {/* 小桌信息卡片 */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div>
            <Title heading={3} style={{ margin: 0, marginBottom: '8px' }}>
              {desk.name}
            </Title>
            <Text type="secondary">{desk.description || '暂无描述'}</Text>
          </div>
          
          <Space>
            <Button
              icon={<IconRankingCardStroked />}
              onClick={handleViewRanking}
            >
              查看排行榜
            </Button>
            
            {!desk.isOwner && (
              <Button 
                type="danger"
                onClick={handleLeaveDesk}
              >
                离开小桌
              </Button>
            )}
          </Space>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <Descriptions row>
            <Descriptions.Item itemKey="members" label="成员">
              {desk.currentMembers}/{desk.maxMembers} 人
            </Descriptions.Item>
            <Descriptions.Item itemKey="status" label="状态">
              <Tag color="green">活跃</Tag>
            </Descriptions.Item>
            <Descriptions.Item itemKey="role" label="我的角色">
              <Tag color={desk.isOwner ? 'blue' : 'green'}>
                {desk.isOwner ? '桌长' : '成员'}
              </Tag>
            </Descriptions.Item>
          </Descriptions>
        </div>
      </Card>

      {/* 管理标签页 */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab={`成员管理 (${members.length})`} itemKey="members">
          <Spin spinning={loading}>
            <Table
              columns={memberColumns}
              dataSource={members}
              rowKey="id"
              pagination={false}
            />
          </Spin>
        </TabPane>
        
        {desk.isOwner && (
          <TabPane tab={`申请审核 (${desk.pendingApplications || 0})`} itemKey="applications">
            <Spin spinning={loading}>
              {applications.length > 0 ? (
                <Table
                  columns={applicationColumns}
                  dataSource={applications}
                  rowKey="id"
                  pagination={false}
                />
              ) : (
                <Empty
                  title="暂无申请"
                  description="还没有用户申请加入这个小桌"
                />
              )}
            </Spin>
          </TabPane>
        )}
      </Tabs>
      </div>
    </>
  );
};

export default DeskDashboard;
