/* 用户排名卡片样式 */
.user-rank-card {
  background: linear-gradient(135deg, var(--paper-cream), var(--paper-warm));
  border: 2px solid var(--border-light);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.user-rank-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  transform: translateY(-4px);
}

/* 装饰性星星 */
.rank-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.star {
  position: absolute;
  color: rgba(255, 215, 0, 0.3);
  animation: twinkle 3s ease-in-out infinite;
}

.star-1 {
  top: 20px;
  right: 30px;
  font-size: 16px;
  animation-delay: 0s;
}

.star-2 {
  top: 60px;
  right: 60px;
  font-size: 12px;
  animation-delay: 1s;
}

.star-3 {
  bottom: 40px;
  right: 20px;
  font-size: 14px;
  animation-delay: 2s;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

/* 排名标题 */
.rank-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

/* 主要排名信息 */
.rank-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.rank-number {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.rank-value {
  font-family: var(--font-handwritten);
  font-size: 48px;
  font-weight: bold;
  color: var(--accent-blue);
  text-shadow: 2px 2px 0px var(--shadow-light);
  transform: rotate(-2deg);
}

.rank-total {
  font-size: 18px;
  color: var(--ink-medium);
  font-weight: 500;
}

.rank-change {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: var(--paper-white);
  border-radius: 20px;
  border: 1px solid var(--border-light);
}

/* 分数信息 */
.score-info {
  margin-bottom: 20px;
}

.score-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

/* 百分位进度条 */
.percentile-progress {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

/* 详细统计 */
.detail-stats {
  border-top: 1px dashed var(--border-medium);
  padding-top: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: var(--paper-white);
  border-radius: 8px;
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--accent-blue-light);
  transform: translateY(-2px);
}

/* 未上榜状态 */
.user-rank-card.no-rank {
  background: var(--paper-warm);
  border: 2px dashed var(--border-medium);
}

.no-rank-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
}

/* 进度条样式覆盖 */
.user-rank-card .semi-progress-line-outer {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  height: 8px;
}

.user-rank-card .semi-progress-line-inner {
  border-radius: 10px;
  transition: all 0.3s ease;
}

/* 标签样式覆盖 */
.user-rank-card .semi-tag {
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rank-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .rank-value {
    font-size: 36px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .score-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
