# 基础设施技术栈选型方案

## 技术栈概览

基于已确定的后端技术栈（Java JDK 1.8 + Spring Boot + MySQL）和前端技术栈（Vue 3 + Nuxt 3），设计配套的基础设施技术方案。

## 1. 缓存方案 - Redis 7.0+

### 1.1 技术选择

**选择：Redis 7.0+ 单机 + 主从复制**

**选择理由：**

- **性能优秀** - 内存存储，毫秒级响应
- **数据结构丰富** - 支持 String、Hash、List、Set、ZSet
- **Spring Boot 集成** - 完美支持 Spring Data Redis
- **排行榜支持** - ZSet 天然支持小桌排行榜功能
- **会话存储** - 支持分布式会话管理

**版本建议：**

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
    <version>2.7.18</version>
</dependency>
<dependency>
    <groupId>redis.clients</groupId>
    <artifactId>jedis</artifactId>
    <version>4.3.1</version>
</dependency>
```

### 1.2 部署架构

**开发环境：** Redis 单机
**生产环境：** Redis 主从复制 + Sentinel 哨兵

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis-master:
    image: redis:7.0-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes

  redis-slave:
    image: redis:7.0-alpine
    ports:
      - '6380:6379'
    command: redis-server --slaveof redis-master 6379 --appendonly yes
    depends_on:
      - redis-master
```

### 1.3 配置要点

**Spring Boot 配置：**

```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    timeout: 2000ms
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 2000ms
```

**缓存策略：**

- **用户信息缓存** - 30 分钟过期
- **排行榜缓存** - 5 分钟过期，定时刷新
- **学习统计缓存** - 1 小时过期
- **配置信息缓存** - 24 小时过期

### 1.4 应用场景

```java
// 排行榜实现
@Service
public class RankingService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 更新小桌排行榜
    public void updateDeskRanking(String deskId, String userId, int score) {
        String key = "desk:ranking:" + deskId;
        redisTemplate.opsForZSet().add(key, userId, score);
        redisTemplate.expire(key, Duration.ofMinutes(5));
    }

    // 获取排行榜
    public List<RankingVO> getDeskRanking(String deskId, int limit) {
        String key = "desk:ranking:" + deskId;
        Set<ZSetOperations.TypedTuple<Object>> ranking =
            redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, limit - 1);
        // 转换为VO对象...
    }
}
```

## 2. 消息队列 - RabbitMQ 3.11+

### 2.1 技术选择

**选择：RabbitMQ 3.11+ 单机部署**

**选择理由：**

- **可靠性高** - 支持消息持久化和确认机制
- **功能丰富** - 支持多种消息模式
- **Spring 集成** - Spring AMQP 完美支持
- **管理界面** - Web 管理界面友好
- **轻量级** - 适合中小型项目

**版本建议：**

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
    <version>2.7.18</version>
</dependency>
```

### 2.2 部署配置

**Docker 部署：**

```yaml
# docker-compose.yml
rabbitmq:
  image: rabbitmq:3.11-management-alpine
  ports:
    - '5672:5672'
    - '15672:15672'
  environment:
    RABBITMQ_DEFAULT_USER: admin
    RABBITMQ_DEFAULT_PASS: admin123
  volumes:
    - rabbitmq-data:/var/lib/rabbitmq
```

**Spring Boot 配置：**

```yaml
# application.yml
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin123
    virtual-host: /
    connection-timeout: 15000
    publisher-confirm-type: correlated
    publisher-returns: true
```

### 2.3 队列设计

**队列规划：**

```java
@Configuration
public class RabbitMQConfig {

    // 通知队列
    @Bean
    public Queue notificationQueue() {
        return QueueBuilder.durable("notification.queue").build();
    }

    // 排行榜更新队列
    @Bean
    public Queue rankingUpdateQueue() {
        return QueueBuilder.durable("ranking.update.queue").build();
    }

    // 信誉分计算队列
    @Bean
    public Queue reputationCalculateQueue() {
        return QueueBuilder.durable("reputation.calculate.queue").build();
    }

    // 学习数据统计队列
    @Bean
    public Queue studyStatisticsQueue() {
        return QueueBuilder.durable("study.statistics.queue").build();
    }
}
```

### 2.4 应用场景

```java
// 消息生产者
@Service
public class MessageProducer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    // 发送通知消息
    public void sendNotification(NotificationMessage message) {
        rabbitTemplate.convertAndSend("notification.queue", message);
    }

    // 发送排行榜更新消息
    public void sendRankingUpdate(RankingUpdateMessage message) {
        rabbitTemplate.convertAndSend("ranking.update.queue", message);
    }
}

// 消息消费者
@Component
public class MessageConsumer {

    @RabbitListener(queues = "notification.queue")
    public void handleNotification(NotificationMessage message) {
        // 处理通知逻辑
        notificationService.sendNotification(message);
    }

    @RabbitListener(queues = "ranking.update.queue")
    public void handleRankingUpdate(RankingUpdateMessage message) {
        // 更新排行榜
        rankingService.updateRanking(message);
    }
}
```

## 3. 搜索方案 - MySQL 全文搜索

### 3.1 技术选择

**选择：MySQL 8.0 全文搜索 + ngram 分词器**

**选择理由：**

- **简化架构** - 无需额外中间件，降低系统复杂度
- **资源节省** - 减少内存和存储占用
- **运维简单** - 减少一个服务的监控和维护
- **满足需求** - 对于公告搜索场景完全够用
- **中文支持** - MySQL 8.0 ngram 分词器支持中文搜索

### 3.2 数据库配置

**MySQL 全文搜索配置：**

```sql
-- 创建支持全文搜索的公告表
CREATE TABLE announcements (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    region VARCHAR(50),
    exam_type VARCHAR(50),
    priority VARCHAR(20) DEFAULT 'normal',
    status VARCHAR(20) DEFAULT 'published',
    created_by VARCHAR(36) NOT NULL,
    published_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 全文搜索索引
    FULLTEXT(title, content) WITH PARSER ngram,

    -- 普通索引
    INDEX idx_region (region),
    INDEX idx_exam_type (exam_type),
    INDEX idx_published_at (published_at),
    INDEX idx_status (status)
);

-- 配置ngram分词器的token大小
SET GLOBAL ngram_token_size = 2;
```

### 3.3 搜索服务实现

```java
@Service
public class AnnouncementSearchService {

    @Autowired
    private AnnouncementMapper announcementMapper;

    // 搜索公告
    public Page<Announcement> searchAnnouncements(
            String keyword, String region, String examType, Page<Announcement> page) {

        LambdaQueryWrapper<Announcement> wrapper = new LambdaQueryWrapper<>();

        // 关键词全文搜索
        if (StringUtils.hasText(keyword)) {
            // 使用MySQL全文搜索
            wrapper.apply("MATCH(title, content) AGAINST({0} IN NATURAL LANGUAGE MODE)", keyword);
        }

        // 地区筛选
        if (StringUtils.hasText(region)) {
            wrapper.eq(Announcement::getRegion, region);
        }

        // 考试类型筛选
        if (StringUtils.hasText(examType)) {
            wrapper.eq(Announcement::getExamType, examType);
        }

        // 只查询已发布的公告
        wrapper.eq(Announcement::getStatus, "published");

        // 按发布时间倒序
        wrapper.orderByDesc(Announcement::getPublishedAt);

        return this.page(page, wrapper);
    }

    // 高级搜索（支持多种搜索模式）
    public Page<Announcement> advancedSearch(
            String keyword, String region, String examType,
            String priority, LocalDateTime startDate, LocalDateTime endDate,
            Page<Announcement> page) {

        LambdaQueryWrapper<Announcement> wrapper = new LambdaQueryWrapper<>();

        // 关键词搜索 - 支持布尔模式
        if (StringUtils.hasText(keyword)) {
            if (keyword.contains("+") || keyword.contains("-") || keyword.contains("\"")) {
                // 布尔搜索模式
                wrapper.apply("MATCH(title, content) AGAINST({0} IN BOOLEAN MODE)", keyword);
            } else {
                // 自然语言模式
                wrapper.apply("MATCH(title, content) AGAINST({0} IN NATURAL LANGUAGE MODE)", keyword);
            }
        }

        // 其他筛选条件
        if (StringUtils.hasText(region)) {
            wrapper.eq(Announcement::getRegion, region);
        }

        if (StringUtils.hasText(examType)) {
            wrapper.eq(Announcement::getExamType, examType);
        }

        if (StringUtils.hasText(priority)) {
            wrapper.eq(Announcement::getPriority, priority);
        }

        // 时间范围筛选
        if (startDate != null) {
            wrapper.ge(Announcement::getPublishedAt, startDate);
        }

        if (endDate != null) {
            wrapper.le(Announcement::getPublishedAt, endDate);
        }

        wrapper.eq(Announcement::getStatus, "published");
        wrapper.orderByDesc(Announcement::getPublishedAt);

        return this.page(page, wrapper);
    }
}
```

### 3.4 搜索优化策略

**性能优化：**

```java
@Service
public class SearchOptimizationService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 热门搜索词缓存
    public List<String> getHotSearchKeywords() {
        String key = "hot:search:keywords";
        List<String> cached = (List<String>) redisTemplate.opsForValue().get(key);

        if (cached == null) {
            // 从数据库统计热门搜索词
            cached = getHotKeywordsFromDB();
            redisTemplate.opsForValue().set(key, cached, Duration.ofHours(1));
        }

        return cached;
    }

    // 搜索结果缓存
    public Page<Announcement> getCachedSearchResults(String cacheKey, Supplier<Page<Announcement>> searchFunction) {
        Page<Announcement> cached = (Page<Announcement>) redisTemplate.opsForValue().get(cacheKey);

        if (cached == null) {
            cached = searchFunction.get();
            // 缓存5分钟
            redisTemplate.opsForValue().set(cacheKey, cached, Duration.ofMinutes(5));
        }

        return cached;
    }
}
```

## 4. 文件存储 - MinIO

### 4.1 技术选择

**选择：MinIO 对象存储**

**选择理由：**

- **S3 兼容** - 兼容 Amazon S3 API
- **轻量级** - 部署简单，资源占用少
- **高性能** - 专为高性能设计
- **开源免费** - 无授权费用
- **易于扩展** - 支持分布式部署

**版本建议：**

```xml
<dependency>
    <groupId>io.minio</groupId>
    <artifactId>minio</artifactId>
    <version>8.5.2</version>
</dependency>
```

### 4.2 部署配置

**Docker 部署：**

```yaml
# docker-compose.yml
minio:
  image: minio/minio:latest
  ports:
    - '9000:9000'
    - '9001:9001'
  environment:
    MINIO_ROOT_USER: admin
    MINIO_ROOT_PASSWORD: admin123456
  volumes:
    - minio-data:/data
  command: server /data --console-address ":9001"
```

**Spring Boot 配置：**

```yaml
# application.yml
minio:
  endpoint: http://localhost:9000
  access-key: admin
  secret-key: admin123456
  bucket-name: kaogong-files
```

### 4.3 文件服务

```java
@Service
public class FileService {
    @Autowired
    private MinioClient minioClient;

    @Value("${minio.bucket-name}")
    private String bucketName;

    // 上传文件
    public String uploadFile(MultipartFile file, String folder) throws Exception {
        String fileName = folder + "/" + UUID.randomUUID() + "_" + file.getOriginalFilename();

        minioClient.putObject(
            PutObjectArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .stream(file.getInputStream(), file.getSize(), -1)
                .contentType(file.getContentType())
                .build()
        );

        return fileName;
    }

    // 获取文件URL
    public String getFileUrl(String fileName) throws Exception {
        return minioClient.getPresignedObjectUrl(
            GetPresignedObjectUrlArgs.builder()
                .method(Method.GET)
                .bucket(bucketName)
                .object(fileName)
                .expiry(7, TimeUnit.DAYS)
                .build()
        );
    }
}
```

### 4.4 应用场景

- **用户头像存储** - 用户上传的头像图片
- **公告附件** - 考试公告的 PDF 附件
- **学习资料** - 用户上传的学习笔记、错题截图
- **系统资源** - 系统图标、背景图片等静态资源

## 5. 监控和日志

### 5.1 应用监控 - Spring Boot Actuator

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
    <version>2.7.18</version>
</dependency>
```

### 5.2 日志收集 - Logback + ELK (可选)

**开发环境：** 文件日志
**生产环境：** ELK Stack (Elasticsearch + Logstash + Kibana)

## 6. 集成架构图

```
┌─────────────────┐    ┌─────────────────┐
│   Vue 3 前端    │────│  Nginx 反向代理  │
└─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │ Spring Boot 后端 │
                       └─────────────────┘
                                │
        ┌───────────────────────┼───────────────────────┐
        │                       │                       │
┌─────────────┐        ┌─────────────┐        ┌─────────────┐
│    MySQL    │        │    Redis    │        │  RabbitMQ   │
│ 主数据库+搜索 │        │    缓存     │        │  消息队列   │
└─────────────┘        └─────────────┘        └─────────────┘
        │                       │                       │
        │                       │               ┌─────────────┐
        │                       │               │    MinIO    │
        │                       │               │  文件存储   │
        │                       │               └─────────────┘
        │
┌─────────────┐
│   Flyway    │
│ 数据库迁移   │
└─────────────┘
```

## 7. 部署建议

### 7.1 开发环境

```bash
# 使用Docker Compose一键启动所有基础设施
docker-compose up -d

# 包含服务：
# - MySQL 8.0
# - Redis 7.0 (单机)
# - RabbitMQ 3.11 (单机)
# - MinIO (单机)
```

### 7.2 生产环境

- **MySQL**: 主从复制 + 读写分离
- **Redis**: 主从复制 + Sentinel 哨兵
- **RabbitMQ**: 集群部署 (3 节点)
- **MinIO**: 分布式部署 (4 节点)

### 7.3 资源配置建议

**最小配置 (开发环境):**

- CPU: 4 核
- 内存: 8GB
- 存储: 100GB SSD

**推荐配置 (生产环境):**

- CPU: 8 核
- 内存: 16GB
- 存储: 500GB SSD

## 8. 性能优化要点

### 8.1 缓存策略

- **多级缓存**: 本地缓存(Caffeine) + 分布式缓存(Redis)
- **缓存预热**: 系统启动时预加载热点数据
- **缓存穿透**: 使用布隆过滤器防止缓存穿透
- **缓存雪崩**: 设置随机过期时间

### 8.2 数据库优化

- **索引优化**: 为查询频繁的字段建立合适索引
- **分页优化**: 使用游标分页替代 OFFSET 分页
- **连接池**: 合理配置 HikariCP 连接池参数
- **慢查询**: 监控和优化慢查询

### 8.3 消息队列优化

- **批量处理**: 批量消费消息提高吞吐量
- **死信队列**: 处理消费失败的消息
- **消息持久化**: 重要消息持久化存储
- **流量控制**: 设置合理的 QoS 参数

## 9. 监控告警

### 9.1 关键指标监控

- **应用指标**: QPS、响应时间、错误率
- **基础设施**: CPU、内存、磁盘、网络
- **中间件**: Redis 命中率、MQ 队列长度、MySQL 查询性能
- **业务指标**: 用户活跃度、学习完成率、小桌参与度

### 9.2 告警策略

- **响应时间** > 2 秒 → 警告
- **错误率** > 1% → 严重
- **Redis 内存使用** > 80% → 警告
- **MQ 队列积压** > 1000 → 警告
- **磁盘使用** > 85% → 严重

## 简化后的技术栈优势

### 架构简化收益

- **减少组件数量**: 从 5 个中间件减少到 3 个核心中间件
- **降低运维复杂度**: 减少 Elasticsearch 的部署、监控和维护工作
- **节省资源成本**: Elasticsearch 通常需要较大内存，去掉后显著节省资源
- **简化开发**: 减少一套搜索 API 的开发和维护工作

### MySQL 全文搜索的优势

- **统一数据源**: 搜索和业务数据在同一数据库，减少数据同步问题
- **事务一致性**: 搜索数据与业务数据保持强一致性
- **简化架构**: 无需维护额外的搜索索引和同步机制
- **满足需求**: 对于公告搜索场景完全够用

### 渐进式扩展策略

当系统发展到一定规模时，如果需要更强的搜索能力，可以：

1. **用户量达到 10 万+** 时考虑引入 Elasticsearch
2. **搜索 QPS 超过 1000** 时考虑搜索服务独立部署
3. **需要复杂分析功能** 时引入专业搜索引擎

这个简化后的基础设施技术栈为考公刷题记录系统提供了完整的支撑，既保证了性能和可靠性，又控制了复杂度和成本。所有组件都与现有的 Java 后端和 Vue 前端技术栈完美集成，为系统的稳定运行提供了坚实基础。
