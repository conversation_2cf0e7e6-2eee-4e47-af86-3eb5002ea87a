# 管理员信誉系统管理文档

## 📋 概述

本文档记录信誉系统的管理员功能和接口，供后续开发管理员后台时参考。

## 🔧 管理员功能清单

### 1. 信誉分数管理

#### 1.1 手动调整用户信誉
- **功能**: 管理员可以手动增加或扣除用户信誉分数
- **使用场景**: 
  - 活动奖励发放
  - 违规行为处罚
  - 系统错误补偿
  - 特殊情况调整

#### 1.2 批量信誉操作
- **功能**: 批量调整多个用户的信誉分数
- **使用场景**:
  - 活动结束后批量发奖
  - 系统升级补偿
  - 节日福利发放

### 2. 保护期管理

#### 2.1 延长用户保护期
- **功能**: 为特定用户延长保护期时间
- **使用场景**:
  - 新用户适应期延长
  - 特殊情况照顾
  - 系统维护期间保护

#### 2.2 提前结束保护期
- **功能**: 手动结束用户的保护期
- **使用场景**:
  - 用户主动申请
  - 违规行为处理

### 3. 信誉记录监控

#### 3.1 信誉变更审计
- **功能**: 查看所有用户的信誉变更记录
- **包含信息**:
  - 变更时间
  - 变更原因
  - 变更分数
  - 操作来源（系统/管理员）

#### 3.2 异常行为检测
- **功能**: 识别异常的信誉变更模式
- **检测内容**:
  - 短时间内大量扣分
  - 异常的连续学习记录
  - 可疑的登录模式

### 4. 定时任务管理

#### 4.1 任务执行监控
- **功能**: 监控定时任务的执行状态
- **监控内容**:
  - 任务执行成功率
  - 执行时间统计
  - 错误日志查看

#### 4.2 手动任务执行
- **功能**: 手动触发定时任务执行
- **使用场景**:
  - 补偿执行失败的任务
  - 测试任务逻辑
  - 紧急情况处理

### 5. 系统配置管理

#### 5.1 扣分限制配置
- **功能**: 调整系统扣分限制参数
- **可配置项**:
  - 每日扣分上限
  - 每周扣分上限
  - 每月扣分上限

#### 5.2 奖励规则配置
- **功能**: 调整各种奖励的分数设置
- **可配置项**:
  - 登录奖励分数
  - 学习奖励分数
  - 连续奖励分数

## 🌐 管理员API接口

### 1. 信誉分数管理接口

#### 1.1 手动调整用户信誉
```http
POST /api/v1/reputation/admin/adjust
Authorization: Bearer {admin_token}
Content-Type: application/x-www-form-urlencoded

userId=user-id&points=50&reason=活动奖励
```

**响应示例**:
```json
{
  "code": 200,
  "message": "成功为用户增加50分",
  "data": null
}
```

#### 1.2 批量调整用户信誉
```http
POST /api/v1/reputation/admin/batch-adjust
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "adjustments": [
    {
      "userId": "user-1",
      "points": 100,
      "reason": "月度活动奖励"
    },
    {
      "userId": "user-2", 
      "points": -20,
      "reason": "违规处罚"
    }
  ]
}
```

#### 1.3 获取用户信誉详情
```http
GET /api/v1/reputation/admin/user/{userId}
Authorization: Bearer {admin_token}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取用户信誉详情成功",
  "data": {
    "userId": "user-id",
    "username": "testuser",
    "currentScore": 150,
    "currentLevel": "bronze",
    "totalEarned": 200,
    "totalDeducted": 50,
    "isInProtection": false,
    "protectionEndTime": null,
    "recentLogs": [...]
  }
}
```

### 2. 保护期管理接口

#### 2.1 延长用户保护期
```http
POST /api/v1/reputation/admin/extend-protection
Authorization: Bearer {admin_token}
Content-Type: application/x-www-form-urlencoded

userId=user-id&hours=24
```

#### 2.2 结束用户保护期
```http
POST /api/v1/reputation/admin/end-protection
Authorization: Bearer {admin_token}
Content-Type: application/x-www-form-urlencoded

userId=user-id&reason=用户申请
```

#### 2.3 获取保护期用户列表
```http
GET /api/v1/reputation/admin/protected-users
Authorization: Bearer {admin_token}
```

### 3. 信誉记录监控接口

#### 3.1 获取信誉变更记录
```http
GET /api/v1/reputation/admin/logs
Authorization: Bearer {admin_token}
Query Parameters:
- userId (可选): 用户ID
- startDate (可选): 开始日期
- endDate (可选): 结束日期
- changeType (可选): 变更类型 (earn/deduct)
- category (可选): 分类
- page: 页码
- size: 每页数量
```

#### 3.2 获取信誉统计报告
```http
GET /api/v1/reputation/admin/statistics
Authorization: Bearer {admin_token}
Query Parameters:
- period: 统计周期 (daily/weekly/monthly)
- startDate: 开始日期
- endDate: 结束日期
```

**响应示例**:
```json
{
  "code": 200,
  "message": "获取信誉统计成功",
  "data": {
    "totalUsers": 1000,
    "activeUsers": 800,
    "protectedUsers": 50,
    "levelDistribution": {
      "newbie": 300,
      "bronze": 250,
      "silver": 200,
      "gold": 150,
      "platinum": 70,
      "diamond": 25,
      "master": 4,
      "grandmaster": 1
    },
    "dailyStats": [
      {
        "date": "2025-07-20",
        "totalEarned": 5000,
        "totalDeducted": 1200,
        "newUsers": 10
      }
    ]
  }
}
```

### 4. 定时任务管理接口

#### 4.1 手动执行定时任务
```http
POST /api/v1/task-test/daily-study-check
Authorization: Bearer {admin_token}
Query Parameters:
- checkDate (可选): 检查日期 (yyyy-MM-dd)
```

#### 4.2 获取任务执行历史
```http
GET /api/v1/task-test/task-history
Authorization: Bearer {admin_token}
Query Parameters:
- taskName (可选): 任务名称
- startDate (可选): 开始日期
- endDate (可选): 结束日期
- limit: 记录数量限制
```

#### 4.3 获取任务执行统计
```http
GET /api/v1/task-test/task-statistics
Authorization: Bearer {admin_token}
Query Parameters:
- days: 统计天数
```

#### 4.4 获取失败任务列表
```http
GET /api/v1/task-test/failed-tasks
Authorization: Bearer {admin_token}
Query Parameters:
- startDate (可选): 开始日期
```

### 5. 系统配置管理接口

#### 5.1 获取系统配置
```http
GET /api/v1/reputation/admin/config
Authorization: Bearer {admin_token}
```

#### 5.2 更新系统配置
```http
PUT /api/v1/reputation/admin/config
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "dailyDeductLimit": 15,
  "weeklyDeductLimit": 50,
  "monthlyDeductLimit": 100,
  "loginRewardPoints": 1,
  "studyBasePoints": {
    "10-19": 1,
    "20-49": 2,
    "50-99": 3,
    "100+": 5
  },
  "qualityBonusPoints": {
    "80-89": 1,
    "90-94": 2,
    "95+": 3
  }
}
```

## 🎯 管理员权限控制

### 权限级别
- **超级管理员**: 所有功能权限
- **信誉管理员**: 信誉相关功能权限
- **任务管理员**: 定时任务管理权限

### 权限验证
- 所有管理员接口需要 `@PreAuthorize("hasRole('ADMIN')")` 注解
- 敏感操作需要额外的权限验证
- 所有管理员操作需要记录操作日志

## 📊 管理员仪表板功能

### 1. 概览面板
- 用户总数和活跃用户数
- 信誉等级分布图表
- 今日信誉变更统计
- 保护期用户数量

### 2. 信誉管理面板
- 用户信誉搜索和筛选
- 批量操作工具
- 信誉变更历史查看
- 异常行为警报

### 3. 任务监控面板
- 定时任务执行状态
- 任务性能图表
- 错误日志查看
- 手动执行工具

### 4. 系统配置面板
- 参数配置界面
- 规则配置工具
- 配置变更历史
- 配置导入导出

## 🔒 安全考虑

### 1. 操作审计
- 所有管理员操作记录到审计日志
- 包含操作时间、操作人、操作内容
- 支持审计日志查询和导出

### 2. 权限控制
- 基于角色的权限控制
- 敏感操作需要二次确认
- 操作频率限制

### 3. 数据保护
- 敏感数据脱敏显示
- 批量操作数量限制
- 重要操作备份机制

---

**注意**: 此文档记录的功能和接口供后续开发管理员后台时参考，当前阶段专注于用户端功能开发。
