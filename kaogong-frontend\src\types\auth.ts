// 认证相关的 TypeScript 类型定义

// 用户信息接口
export interface User {
  id: string;
  username: string;
  email: string;
  nickname: string;
  avatarUrl?: string;
  targetPosition?: string;
  reputationScore: number;
  reputationLevel: string;
  systemRole: string;
  phone?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 登录请求接口
export interface LoginRequest {
  username: string;  // 用户名或邮箱
  password: string;
}

// 登录响应接口
export interface LoginResponse {
  token: string;
  refreshToken: string;
  userInfo: User;
}

// 注册请求接口
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  inviteCode: string;
  nickname?: string;
}

// 注册响应接口
export interface RegisterResponse {
  userId: string;
  username: string;
}

// 邀请码验证请求接口
export interface ValidateInviteCodeRequest {
  code: string;
}

// 邀请码验证响应接口
export interface ValidateInviteCodeResponse {
  valid: boolean;
  expiresAt?: string;
  message?: string;
}

// 刷新Token请求接口
export interface RefreshTokenRequest {
  refreshToken: string;
}

// API 统一响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  success: boolean;
}

// 表单验证错误接口
export interface FormErrors {
  username?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  inviteCode?: string;
  nickname?: string;
}

// 认证状态接口
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}

// 认证操作接口
export interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  validateInviteCode: (code: string) => Promise<ValidateInviteCodeResponse>;
}

// 完整的认证Store接口
export interface AuthStore extends AuthState, AuthActions {}

// 表单状态接口
export interface FormState {
  isSubmitting: boolean;
  errors: FormErrors;
  touched: Record<string, boolean>;
}

// 路由状态接口
export interface RouteState {
  from?: string;
  message?: string;
}
