package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 排行榜统计信息响应DTO
 * 用于返回排行榜的整体统计数据
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
public class RankingStatistics {

    /**
     * 排行榜类型
     */
    private String rankingType;

    /**
     * 统计周期
     */
    private String period;

    /**
     * 总参与人数
     */
    private Integer totalParticipants;

    /**
     * 平均分数
     */
    private Double averageScore;

    /**
     * 中位数分数
     */
    private Double medianScore;

    /**
     * 最高分数
     */
    private Integer maxScore;

    /**
     * 最低分数
     */
    private Integer minScore;

    /**
     * 前10%分数线
     */
    private Integer topPercentileScore;

    /**
     * 分数分布
     */
    private List<ScoreDistribution> scoreDistribution;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 排行榜活跃度（相比上一周期的参与人数变化）
     */
    private Integer participantChange;

    /**
     * 分数分布内部类
     */
    @Data
    public static class ScoreDistribution {
        /**
         * 分数区间描述
         */
        private String range;

        /**
         * 分数区间最小值
         */
        private Integer minScore;

        /**
         * 分数区间最大值
         */
        private Integer maxScore;

        /**
         * 该区间人数
         */
        private Integer count;

        /**
         * 该区间占比（百分比）
         */
        private Double percentage;
    }

    /**
     * 排行榜趋势信息
     */
    private TrendInfo trendInfo;

    /**
     * 趋势信息内部类
     */
    @Data
    public static class TrendInfo {
        /**
         * 平均分数趋势（相比上一周期）
         */
        private String averageScoreTrend; // "up", "down", "stable"

        /**
         * 参与人数趋势
         */
        private String participantTrend; // "up", "down", "stable"

        /**
         * 竞争激烈程度（基于分数分布的标准差）
         */
        private String competitionLevel; // "high", "medium", "low"

        /**
         * 趋势描述
         */
        private String description;
    }
}
