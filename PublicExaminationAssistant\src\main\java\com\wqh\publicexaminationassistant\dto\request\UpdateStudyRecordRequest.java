package com.wqh.publicexaminationassistant.dto.request;

import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 更新刷题记录请求DTO
 * 只允许更新学习笔记和薄弱知识点
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class UpdateStudyRecordRequest {

    /**
     * 学习笔记
     */
    @Size(max = 1000, message = "学习笔记不能超过1000个字符")
    private String notes;

    /**
     * 薄弱知识点 (JSON字符串格式)
     */
    @Size(max = 2000, message = "薄弱知识点内容不能超过2000个字符")
    private String weakPoints;
}
