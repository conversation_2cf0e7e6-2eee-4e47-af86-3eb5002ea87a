-- =====================================================
-- 信誉系统初始化脚本
-- =====================================================
-- 用途: 在主数据库架构创建后，初始化信誉系统数据
-- 前置条件: 必须先执行 database-schema.sql
-- 执行时机: 系统部署时或现有系统升级时
-- =====================================================

-- 设置数据库配置
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 为现有用户初始化信誉统计数据
-- =====================================================
-- 检查是否需要初始化数据
SELECT 
  COUNT(*) AS existing_users,
  (SELECT COUNT(*) FROM user_reputation_stats) AS users_with_reputation
FROM users;

-- 为没有信誉统计记录的用户创建记录
INSERT INTO user_reputation_stats (
  user_id, 
  current_score, 
  current_level, 
  protection_end_time,
  created_at,
  updated_at
)
SELECT 
  u.id,
  COALESCE(u.reputation_score, 100) AS current_score,
  COALESCE(u.reputation_level, 'newbie') AS current_level,
  -- 新用户：注册时间 + 3天保护期
  -- 老用户：如果注册超过3天，则无保护期
  CASE 
    WHEN DATE_ADD(u.created_at, INTERVAL 3 DAY) > NOW() 
    THEN DATE_ADD(u.created_at, INTERVAL 3 DAY)
    ELSE NULL 
  END AS protection_end_time,
  NOW() AS created_at,
  NOW() AS updated_at
FROM users u
WHERE u.id NOT IN (SELECT user_id FROM user_reputation_stats WHERE user_id IS NOT NULL);

-- =====================================================
-- 2. 创建触发器：新用户注册时自动创建信誉统计记录
-- =====================================================
DELIMITER $$

DROP TRIGGER IF EXISTS `tr_users_after_insert_reputation`$$

CREATE TRIGGER `tr_users_after_insert_reputation`
AFTER INSERT ON `users`
FOR EACH ROW
BEGIN
  -- 为新注册用户创建信誉统计记录
  INSERT INTO user_reputation_stats (
    user_id,
    current_score,
    current_level,
    protection_end_time,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    COALESCE(NEW.reputation_score, 100),
    COALESCE(NEW.reputation_level, 'newbie'),
    DATE_ADD(NEW.created_at, INTERVAL 3 DAY), -- 3天保护期
    NOW(),
    NOW()
  );
END$$

DELIMITER ;

-- =====================================================
-- 3. 创建存储过程：更新用户信誉等级
-- =====================================================
DELIMITER $$

DROP PROCEDURE IF EXISTS `UpdateUserReputationLevel`$$

CREATE PROCEDURE `UpdateUserReputationLevel`(IN userId VARCHAR(36))
BEGIN
  DECLARE currentScore INT;
  DECLARE newLevel VARCHAR(20);
  
  -- 获取当前分数
  SELECT current_score INTO currentScore 
  FROM user_reputation_stats 
  WHERE user_id = userId;
  
  -- 根据分数计算等级
  IF currentScore < 100 THEN
    SET newLevel = 'newbie';
  ELSEIF currentScore < 300 THEN
    SET newLevel = 'bronze';
  ELSEIF currentScore < 600 THEN
    SET newLevel = 'silver';
  ELSEIF currentScore < 1000 THEN
    SET newLevel = 'gold';
  ELSEIF currentScore < 2000 THEN
    SET newLevel = 'platinum';
  ELSEIF currentScore < 5000 THEN
    SET newLevel = 'diamond';
  ELSEIF currentScore < 10000 THEN
    SET newLevel = 'master';
  ELSE
    SET newLevel = 'grandmaster';
  END IF;
  
  -- 更新信誉统计表
  UPDATE user_reputation_stats 
  SET current_level = newLevel,
      updated_at = NOW()
  WHERE user_id = userId;
  
  -- 同步更新users表
  UPDATE users 
  SET reputation_score = currentScore,
      reputation_level = newLevel,
      updated_at = NOW()
  WHERE id = userId;
END$$

DELIMITER ;

-- =====================================================
-- 4. 创建存储过程：检查用户保护期状态
-- =====================================================
DELIMITER $$

DROP PROCEDURE IF EXISTS `CheckUserProtectionStatus`$$

CREATE PROCEDURE `CheckUserProtectionStatus`(
  IN userId VARCHAR(36),
  OUT isInProtection BOOLEAN,
  OUT hoursRemaining INT
)
BEGIN
  DECLARE protectionEndTime TIMESTAMP;
  
  -- 获取保护期结束时间
  SELECT protection_end_time INTO protectionEndTime
  FROM user_reputation_stats 
  WHERE user_id = userId;
  
  -- 判断是否在保护期内
  IF protectionEndTime IS NULL OR protectionEndTime <= NOW() THEN
    SET isInProtection = FALSE;
    SET hoursRemaining = 0;
  ELSE
    SET isInProtection = TRUE;
    SET hoursRemaining = TIMESTAMPDIFF(HOUR, NOW(), protectionEndTime);
  END IF;
END$$

DELIMITER ;

-- =====================================================
-- 5. 创建视图：用户信誉概览
-- =====================================================
CREATE OR REPLACE VIEW `v_user_reputation_overview` AS
SELECT 
  u.id,
  u.username,
  u.nickname,
  urs.current_score,
  urs.current_level,
  urs.total_earned,
  urs.total_deducted,
  urs.consecutive_login_days,
  urs.consecutive_study_days,
  urs.consecutive_no_study_days,
  urs.protection_end_time,
  CASE 
    WHEN urs.protection_end_time IS NOT NULL AND urs.protection_end_time > NOW() 
    THEN TRUE 
    ELSE FALSE 
  END AS is_in_protection,
  CASE 
    WHEN urs.protection_end_time IS NOT NULL AND urs.protection_end_time > NOW()
    THEN TIMESTAMPDIFF(HOUR, NOW(), urs.protection_end_time)
    ELSE 0
  END AS protection_hours_remaining,
  urs.last_login_date,
  urs.last_study_date,
  urs.updated_at
FROM users u
LEFT JOIN user_reputation_stats urs ON u.id = urs.user_id;

-- =====================================================
-- 6. 插入初始信誉等级配置数据（可选）
-- =====================================================
-- 如果需要在数据库中存储等级配置，可以创建配置表
-- 这里仅作为示例，实际可以在应用代码中配置

-- CREATE TABLE IF NOT EXISTS reputation_level_config (
--   level_name VARCHAR(20) PRIMARY KEY,
--   min_score INT NOT NULL,
--   max_score INT NOT NULL,
--   display_name VARCHAR(50) NOT NULL,
--   color_code VARCHAR(10) NOT NULL,
--   description TEXT
-- );

-- INSERT INTO reputation_level_config VALUES
-- ('newbie', 0, 99, '新手', '#87d068', '刚开始学习的用户'),
-- ('bronze', 100, 299, '青铜', '#CD7F32', '有一定学习基础的用户'),
-- ('silver', 300, 599, '白银', '#C0C0C0', '学习较为稳定的用户'),
-- ('gold', 600, 999, '黄金', '#FFD700', '学习表现优秀的用户'),
-- ('platinum', 1000, 1999, '铂金', '#E5E4E2', '学习非常优秀的用户'),
-- ('diamond', 2000, 4999, '钻石', '#B9F2FF', '学习表现卓越的用户'),
-- ('master', 5000, 9999, '大师', '#FF6B6B', '学习大师级用户'),
-- ('grandmaster', 10000, 999999, '宗师', '#9B59B6', '学习宗师级用户');

-- =====================================================
-- 7. 数据验证和统计
-- =====================================================
-- 验证初始化结果
SELECT 
  '用户总数' AS metric,
  COUNT(*) AS count
FROM users
UNION ALL
SELECT 
  '有信誉记录的用户数' AS metric,
  COUNT(*) AS count
FROM user_reputation_stats
UNION ALL
SELECT 
  '保护期内用户数' AS metric,
  COUNT(*) AS count
FROM user_reputation_stats 
WHERE protection_end_time IS NOT NULL AND protection_end_time > NOW()
UNION ALL
SELECT 
  '信誉记录总数' AS metric,
  COUNT(*) AS count
FROM reputation_logs;

-- 显示等级分布
SELECT 
  current_level,
  COUNT(*) AS user_count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_reputation_stats), 2) AS percentage
FROM user_reputation_stats 
GROUP BY current_level 
ORDER BY 
  CASE current_level
    WHEN 'newbie' THEN 1
    WHEN 'bronze' THEN 2
    WHEN 'silver' THEN 3
    WHEN 'gold' THEN 4
    WHEN 'platinum' THEN 5
    WHEN 'diamond' THEN 6
    WHEN 'master' THEN 7
    WHEN 'grandmaster' THEN 8
  END;

-- =====================================================
-- 恢复外键检查
-- =====================================================
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 初始化完成
-- =====================================================
SELECT 'Reputation system initialization completed successfully!' AS message;
