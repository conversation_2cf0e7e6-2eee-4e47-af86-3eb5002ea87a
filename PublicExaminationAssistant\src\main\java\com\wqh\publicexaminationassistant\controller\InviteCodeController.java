package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.ValidateInviteCodeRequest;
import com.wqh.publicexaminationassistant.dto.response.ValidateInviteCodeResponse;
import com.wqh.publicexaminationassistant.service.InviteCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 邀请码管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/v1/invite-codes")
@RequiredArgsConstructor
@Validated
@Tag(name = "邀请码管理", description = "邀请码生成、验证等功能")
public class InviteCodeController {

    private final InviteCodeService inviteCodeService;

    @Operation(summary = "验证邀请码", description = "验证邀请码是否有效，用于注册前验证")
    @PostMapping("/validate")
    public ApiResponse<ValidateInviteCodeResponse> validateInviteCode(@Valid @RequestBody ValidateInviteCodeRequest request) {
        log.info("验证邀请码请求: code={}", request.getCode());
        ValidateInviteCodeResponse response = inviteCodeService.validateInviteCode(request);
        return ApiResponse.success(response);
    }

    @Operation(summary = "生成测试邀请码", description = "生成测试用邀请码（仅开发环境使用）")
    @PostMapping("/generate-test")
    public ApiResponse<List<String>> generateTestInviteCodes() {
        log.info("生成测试邀请码请求");
        // 生成5个测试邀请码，有效期30天
        // 使用null表示系统生成的邀请码
        List<String> codes = inviteCodeService.generateInviteCodes(
                null, 5, LocalDateTime.now().plusDays(30)
        );
        return ApiResponse.success(codes, "测试邀请码生成成功");
    }
}
