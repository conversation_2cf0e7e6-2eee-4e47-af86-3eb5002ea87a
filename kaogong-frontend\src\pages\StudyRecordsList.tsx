import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Pagination,
  Select,
  DatePicker,
  Toast,
  Modal,
  Popconfirm,
  Empty,
  Spin
} from '@douyinfe/semi-ui';
import {
  IconRefresh,
  IconPlus,
  IconEdit,
  IconDelete,
  IconEyeOpened,
  IconCalendar,
  IconFilter
} from '@douyinfe/semi-icons';
import { useNavigate } from 'react-router-dom';
import { studyService, StudyRecordResponse } from '../services/studyService';
import Navigation from '../components/Navigation';
import '../styles/theme.css';

const { Title, Text } = Typography;

// 学习模块映射 - 与后端API保持一致
const MODULE_TYPE_MAP: Record<string, string> = {
  'math': '数学运算',
  'logic': '逻辑推理',
  'language': '言语理解',
  'knowledge': '常识判断',
  'essay': '申论写作'
};

// 浮动装饰组件
const FloatingDecorations: React.FC = () => {
  const decorations = ['📚', '✏️', '📝', '🎯', '💡', '⭐'];
  
  return (
    <>
      {decorations.map((emoji, index) => (
        <div
          key={index}
          className="floating-emoji"
          style={{
            position: 'absolute',
            top: `${15 + index * 12}%`,
            left: `${5 + (index % 2) * 90}%`,
            animationDelay: `${index * 0.8}s`,
            zIndex: 1
          }}
        >
          {emoji}
        </div>
      ))}
    </>
  );
};

interface StudyRecordsListProps {}

// 辅助函数：将Date对象转换为本地日期字符串 (YYYY-MM-DD)
const formatDateToLocal = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const StudyRecordsList: React.FC<StudyRecordsListProps> = () => {
  const navigate = useNavigate();
  
  // 状态管理
  const [records, setRecords] = useState<StudyRecordResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    size: 20,
    total: 0
  });
  
  // 筛选条件
  const [filters, setFilters] = useState({
    moduleType: '',
    startDate: '',
    endDate: ''
  });

  // 获取记录列表
  const fetchRecords = async (page = 1, size = 20) => {
    setLoading(true);
    try {
      // 构建参数，过滤掉空值
      const params: any = {
        page,
        size
      };

      // 只添加非空的筛选参数
      if (filters.moduleType && filters.moduleType.trim() !== '') {
        params.moduleType = filters.moduleType;
      }
      if (filters.startDate && filters.startDate.trim() !== '') {
        params.startDate = filters.startDate;
      }
      if (filters.endDate && filters.endDate.trim() !== '') {
        params.endDate = filters.endDate;
      }

      const response = await studyService.getRecords(params);

      // 安全检查响应数据
      setRecords(response.records || []);
      setPagination({
        current: response.current || 1,
        size: response.size || 20,
        total: response.total || 0
      });
    } catch (error: any) {
      console.error('获取记录列表失败:', error);
      Toast.error(error.message || '获取记录列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchRecords();
  }, []);

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    fetchRecords(page, size || pagination.size);
  };

  // 处理筛选
  const handleFilter = () => {
    fetchRecords(1, pagination.size);
  };

  // 重置筛选
  const handleResetFilter = () => {
    setFilters({
      moduleType: '',
      startDate: '',
      endDate: ''
    });
    setTimeout(() => {
      fetchRecords(1, pagination.size);
    }, 100);
  };

  // 删除记录
  const handleDelete = async (recordId: string) => {
    try {
      await studyService.deleteRecord(recordId);
      Toast.success('删除成功');
      fetchRecords(pagination.current, pagination.size);
    } catch (error: any) {
      console.error('删除记录失败:', error);
      Toast.error(error.message || '删除记录失败');
    }
  };

  // 查看详情
  const handleViewDetail = (recordId: string) => {
    navigate(`/study-records/${recordId}`);
  };

  // 编辑记录
  const handleEdit = (recordId: string) => {
    navigate(`/study-records/${recordId}/edit`);
  };

  // 表格列定义
  const columns = [
    {
      title: '学习日期',
      dataIndex: 'studyDate',
      key: 'studyDate',
      width: 120,
      render: (date: string) => (
        <Text style={{ fontFamily: 'var(--font-handwritten)' }}>
          {date}
        </Text>
      )
    },
    {
      title: '学习模块',
      dataIndex: 'moduleType',
      key: 'moduleType',
      width: 120,
      render: (moduleType: string) => (
        <Tag 
          color="blue" 
          style={{ 
            fontFamily: 'var(--font-handwritten)',
            transform: 'rotate(-0.5deg)'
          }}
        >
          {MODULE_TYPE_MAP[moduleType] || moduleType}
        </Tag>
      )
    },
    {
      title: '题目数量',
      dataIndex: 'questionCount',
      key: 'questionCount',
      width: 100,
      align: 'center' as const,
      render: (count: number) => (
        <Text strong style={{ color: 'var(--accent-blue)' }}>
          {count}
        </Text>
      )
    },
    {
      title: '正确率',
      dataIndex: 'accuracyRate',
      key: 'accuracyRate',
      width: 100,
      align: 'center' as const,
      render: (rate: number) => {
        const color = rate >= 80 ? 'var(--accent-green)' : 
                     rate >= 60 ? 'var(--accent-orange)' : 'var(--accent-pink)';
        return (
          <Text strong style={{ color }}>
            {(rate).toFixed(1)}%
          </Text>
        );
      }
    },
    {
      title: '学习时长',
      dataIndex: 'studyDuration',
      key: 'studyDuration',
      width: 100,
      align: 'center' as const,
      render: (duration: number) => (
        <Text style={{ fontFamily: 'var(--font-handwritten)' }}>
          {Math.floor(duration / 60)}h {duration % 60}m
        </Text>
      )
    },
    {
      title: '薄弱知识点',
      dataIndex: 'weakPoints',
      key: 'weakPoints',
      width: 200,
      render: (weakPoints: string[] | string | null) => {
        // 处理不同的数据类型
        let pointsArray: string[] = [];

        if (Array.isArray(weakPoints)) {
          pointsArray = weakPoints;
        } else if (typeof weakPoints === 'string' && weakPoints.trim() !== '') {
          try {
            // 尝试解析JSON字符串
            pointsArray = JSON.parse(weakPoints);
          } catch {
            // 如果不是JSON，按逗号分割
            pointsArray = weakPoints.split(',').map(p => p.trim()).filter(p => p.length > 0);
          }
        }

        return (
          <div>
            {pointsArray && pointsArray.length > 0 ? (
              pointsArray.slice(0, 2).map((point, index) => (
                <Tag
                  key={index}
                  size="small"
                  color="orange"
                  style={{
                    margin: '2px',
                    transform: `rotate(${index % 2 === 0 ? '-0.5' : '0.5'}deg)`
                  }}
                >
                  {point}
                </Tag>
              ))
            ) : (
              <Text type="tertiary">无</Text>
            )}
            {pointsArray && pointsArray.length > 2 && (
              <Text type="tertiary" size="small">
                +{pointsArray.length - 2}
              </Text>
            )}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      align: 'center' as const,
      render: (_: any, record: StudyRecordResponse) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEyeOpened />}
            size="small"
            onClick={() => handleViewDetail(record.id)}
            style={{ transform: 'rotate(-0.5deg)' }}
          />
          <Button
            theme="borderless"
            type="secondary"
            icon={<IconEdit />}
            size="small"
            onClick={() => handleEdit(record.id)}
            style={{ transform: 'rotate(0.5deg)' }}
          />
          <Popconfirm
            title="确认删除"
            content="删除后无法恢复，确定要删除这条记录吗？"
            position="top"
            onConfirm={() => handleDelete(record.id)}
            okText="确认删除"
            cancelText="取消"
            okType="danger"
            style={{
              fontFamily: 'var(--font-handwritten)',
              borderRadius: '12px',
              border: '2px solid var(--ink-dark)',
              boxShadow: '3px 3px 0px var(--shadow-medium)'
            }}
          >
            <Button
              theme="borderless"
              type="danger"
              icon={<IconDelete />}
              size="small"
              style={{ transform: 'rotate(-0.5deg)' }}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <>
      <Navigation />
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
        padding: '24px',
        paddingTop: '104px', // Add top padding for navigation bar
        position: 'relative'
      }}>
        <FloatingDecorations />
      
      <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 10 }}>
        {/* 页面标题 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title 
            heading={2} 
            className="handwritten-title large"
            style={{ 
              color: 'var(--ink-dark)',
              marginBottom: '8px'
            }}
          >
            📚 我的刷题记录
          </Title>
          <Text 
            type="secondary" 
            style={{ 
              fontSize: 'var(--font-size-lg)',
              fontFamily: 'var(--font-handwritten)'
            }}
          >
            记录每一次的学习成长，见证进步的足迹
          </Text>
        </div>

        {/* 筛选区域 */}
        <Card 
          className="sketch-card"
          style={{ marginBottom: '24px' }}
          bodyStyle={{ padding: '20px' }}
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
            alignItems: 'end'
          }}>
            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                学习模块
              </Text>
              <Select
                placeholder="选择学习模块"
                value={filters.moduleType}
                onChange={(value) => setFilters(prev => ({ ...prev, moduleType: value as string }))}
                style={{ width: '100%' }}
                showClear
              >
                {Object.entries(MODULE_TYPE_MAP).map(([key, value]) => (
                  <Select.Option key={key} value={key}>
                    {value}
                  </Select.Option>
                ))}
              </Select>
            </div>
            
            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                开始日期
              </Text>
              <DatePicker
                placeholder="选择开始日期"
                value={filters.startDate ? new Date(filters.startDate) : undefined}
                onChange={(date) => {
                  if (date instanceof Date) {
                    setFilters(prev => ({
                      ...prev,
                      startDate: formatDateToLocal(date)
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      startDate: ''
                    }));
                  }
                }}
                style={{ width: '100%' }}
                format="yyyy-MM-dd"
                type="date"
              />
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: '8px' }}>
                结束日期
              </Text>
              <DatePicker
                placeholder="选择结束日期"
                value={filters.endDate ? new Date(filters.endDate) : undefined}
                onChange={(date) => {
                  if (date instanceof Date) {
                    setFilters(prev => ({
                      ...prev,
                      endDate: formatDateToLocal(date)
                    }));
                  } else {
                    setFilters(prev => ({
                      ...prev,
                      endDate: ''
                    }));
                  }
                }}
                style={{ width: '100%' }}
                format="yyyy-MM-dd"
                type="date"
              />
            </div>
            

            
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                theme="solid"
                type="primary"
                icon={<IconFilter />}
                onClick={handleFilter}
                className="sketch-button primary"
                style={{ transform: 'rotate(-0.5deg)' }}
              >
                筛选
              </Button>
              <Button
                theme="borderless"
                icon={<IconRefresh />}
                onClick={handleResetFilter}
                className="sketch-button"
                style={{ transform: 'rotate(0.5deg)' }}
              >
                重置
              </Button>
            </div>
          </div>
        </Card>

        {/* 操作栏 */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <Text type="secondary" style={{ fontFamily: 'var(--font-handwritten)' }}>
            共找到 {pagination.total} 条记录
          </Text>
          <Button
            theme="solid"
            type="primary"
            icon={<IconPlus />}
            onClick={() => navigate('/study-records/create')}
            className="sketch-button primary"
            style={{ transform: 'rotate(-0.5deg)' }}
          >
            新增记录
          </Button>
        </div>

        {/* 记录表格 */}
        <Card className="sketch-card">
          <Spin spinning={loading}>
            {records.length > 0 ? (
              <>
                <Table
                  columns={columns}
                  dataSource={records}
                  pagination={false}
                  rowKey="id"
                  style={{ 
                    background: 'var(--paper-bg)',
                    borderRadius: '8px'
                  }}
                />
                
                {/* 分页 */}
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'center', 
                  marginTop: '24px' 
                }}>
                  <Pagination
                    currentPage={pagination.current}
                    pageSize={pagination.size}
                    total={pagination.total}
                    onPageChange={handlePageChange}
                    showSizeChanger
                    showQuickJumper
                    showTotal={true}
                  />
                </div>
              </>
            ) : (
              <Empty
                image={<IconCalendar size="extra-large" />}
                title="暂无学习记录"
                description="开始你的第一次刷题记录吧！"
                style={{ padding: '40px 0' }}
              >
                <Button
                  theme="solid"
                  type="primary"
                  icon={<IconPlus />}
                  onClick={() => navigate('/study-records/create')}
                  className="sketch-button primary"
                >
                  创建第一条记录
                </Button>
              </Empty>
            )}
          </Spin>
        </Card>
      </div>
      </div>
    </>
  );
};

export default StudyRecordsList;
