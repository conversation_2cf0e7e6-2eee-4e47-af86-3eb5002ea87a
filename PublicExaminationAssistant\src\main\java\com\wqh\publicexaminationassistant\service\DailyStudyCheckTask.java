package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.entity.ReputationLog;
import com.wqh.publicexaminationassistant.entity.ScheduledTaskLog;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import com.wqh.publicexaminationassistant.mapper.ScheduledTaskLogMapper;
import com.wqh.publicexaminationassistant.mapper.UserReputationStatsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 每日学习检查定时任务
 * 每天00:00执行，检查前一天没有学习记录的用户并进行扣分
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Component
@Slf4j
public class DailyStudyCheckTask {

    @Autowired
    private ReputationService reputationService;
    
    @Autowired
    private StudyRecordService studyRecordService;

    @Autowired
    private UserReputationStatsMapper userReputationStatsMapper;
    
    @Autowired
    private ScheduledTaskLogMapper scheduledTaskLogMapper;
    
    @Autowired
    private UserProtectionService userProtectionService;

    /**
     * 每日00:00执行学习检查任务
     * 检查前一天没有学习记录的用户并扣分
     */
    @Scheduled(cron = "0 0 0 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void checkDailyStudy() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startTime = LocalDateTime.now();
        String taskName = ScheduledTaskLog.TaskName.DAILY_STUDY_CHECK;
        
        // 创建任务执行记录
        ScheduledTaskLog taskLog = new ScheduledTaskLog();
        taskLog.setTaskName(taskName);
        taskLog.setExecutionDate(yesterday);
        taskLog.setStartTime(startTime);
        taskLog.setStatus(ScheduledTaskLog.Status.RUNNING);
        taskLog.setProcessedCount(0);
        scheduledTaskLogMapper.insert(taskLog);
        
        log.info("开始执行每日学习检查任务，检查日期: {}", yesterday);
        
        int processedCount = 0;
        
        try {
            // 检查今日是否已执行成功
            if (scheduledTaskLogMapper.isTaskCompletedToday(taskName, yesterday)) {
                log.info("每日学习检查任务今日已执行成功，跳过执行");
                updateTaskCompleted(taskLog.getId(), startTime, 0);
                return;
            }
            
            // 获取所有活跃用户（排除保护期用户）
            List<UserReputationStats> activeUsers = userReputationStatsMapper.selectActiveUsersExcludeProtected();
            log.info("获取到 {} 个需要检查的活跃用户", activeUsers.size());
            
            // 检查每个用户昨天的学习记录
            for (UserReputationStats userStats : activeUsers) {
                try {
                    checkUserDailyStudy(userStats, yesterday);
                    processedCount++;
                } catch (Exception e) {
                    log.error("检查用户 {} 学习记录失败", userStats.getUserId(), e);
                    // 继续处理其他用户
                }
            }
            
            // 更新任务状态为完成
            updateTaskCompleted(taskLog.getId(), startTime, processedCount);
            
            log.info("每日学习检查任务完成，检查用户数: {}, 处理用户数: {}", activeUsers.size(), processedCount);
            
        } catch (Exception e) {
            log.error("每日学习检查任务执行失败", e);
            updateTaskFailed(taskLog.getId(), startTime, e.getMessage());
            throw e;
        }
    }

    /**
     * 检查单个用户的每日学习情况
     * 
     * @param userStats 用户信誉统计
     * @param checkDate 检查日期
     */
    private void checkUserDailyStudy(UserReputationStats userStats, LocalDate checkDate) {
        String userId = userStats.getUserId();
        
        try {
            // 再次确认用户不在保护期内
            if (userProtectionService.isInProtectionPeriod(userId)) {
                log.debug("用户 {} 在保护期内，跳过学习检查", userId);
                return;
            }
            
            // 检查指定日期是否有学习记录
            boolean hasStudyRecord = studyRecordService.hasStudyRecordOnDate(userId, checkDate);
            
            if (!hasStudyRecord) {
                // 计算连续未学习天数
                int consecutiveDays = studyRecordService.getConsecutiveNoStudyDays(userId, checkDate);
                
                // 更新用户统计中的连续未学习天数
                userReputationStatsMapper.updateConsecutiveNoStudyDays(userId, consecutiveDays);
                
                // 根据连续天数扣分
                int deductPoints = calculateDeductPoints(consecutiveDays);
                
                if (deductPoints > 0) {
                    String reason = String.format("连续%d天无学习记录", consecutiveDays);
                    reputationService.deductPoints(
                        userId, 
                        deductPoints, 
                        reason,
                        ReputationLog.Category.DAILY_STUDY,
                        null,
                        consecutiveDays
                    );
                    
                    log.info("用户 {} 因连续{}天无学习记录被扣除{}分", userId, consecutiveDays, deductPoints);
                } else {
                    log.debug("用户 {} 连续{}天无学习记录，但不满足扣分条件", userId, consecutiveDays);
                }
            } else {
                // 有学习记录，重置连续未学习天数
                if (userStats.getConsecutiveNoStudyDays() != null && userStats.getConsecutiveNoStudyDays() > 0) {
                    userReputationStatsMapper.updateConsecutiveNoStudyDays(userId, 0);
                    log.debug("用户 {} 有学习记录，重置连续未学习天数", userId);
                }
            }
            
        } catch (Exception e) {
            log.error("检查用户 {} 在日期 {} 的学习情况失败", userId, checkDate, e);
            throw e;
        }
    }

    /**
     * 根据连续未学习天数计算扣分
     * 
     * @param consecutiveDays 连续未学习天数
     * @return 扣除的分数
     */
    private int calculateDeductPoints(int consecutiveDays) {
        switch (consecutiveDays) {
            case 1: return 3;   // 1天无学习: -3分
            case 2: return 5;   // 连续2天: -5分
            case 3: return 8;   // 连续3天: -8分
            default: 
                if (consecutiveDays >= 7) {
                    return 15;  // 连续7天及以上: -15分
                }
                return 0;       // 其他情况不扣分
        }
    }

    /**
     * 更新任务状态为完成
     * 
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @param processedCount 处理数量
     */
    private void updateTaskCompleted(String taskId, LocalDateTime startTime, int processedCount) {
        try {
            scheduledTaskLogMapper.updateTaskCompleted(taskId, LocalDateTime.now(), processedCount);
        } catch (Exception e) {
            log.error("更新任务 {} 完成状态失败", taskId, e);
        }
    }

    /**
     * 更新任务状态为失败
     * 
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @param errorMessage 错误信息
     */
    private void updateTaskFailed(String taskId, LocalDateTime startTime, String errorMessage) {
        try {
            scheduledTaskLogMapper.updateTaskFailed(taskId, LocalDateTime.now(), errorMessage);
        } catch (Exception e) {
            log.error("更新任务 {} 失败状态失败", taskId, e);
        }
    }

    /**
     * 手动执行每日学习检查（用于测试或补偿执行）
     * 
     * @param checkDate 检查日期
     */
    public void manualCheckDailyStudy(LocalDate checkDate) {
        log.info("手动执行每日学习检查，检查日期: {}", checkDate);
        
        LocalDateTime startTime = LocalDateTime.now();
        String taskName = ScheduledTaskLog.TaskName.DAILY_STUDY_CHECK;
        
        // 创建任务执行记录
        ScheduledTaskLog taskLog = new ScheduledTaskLog();
        taskLog.setTaskName(taskName + "_manual");
        taskLog.setExecutionDate(checkDate);
        taskLog.setStartTime(startTime);
        taskLog.setStatus(ScheduledTaskLog.Status.RUNNING);
        taskLog.setProcessedCount(0);
        scheduledTaskLogMapper.insert(taskLog);
        
        int processedCount = 0;
        
        try {
            List<UserReputationStats> activeUsers = userReputationStatsMapper.selectActiveUsersExcludeProtected();
            
            for (UserReputationStats userStats : activeUsers) {
                try {
                    checkUserDailyStudy(userStats, checkDate);
                    processedCount++;
                } catch (Exception e) {
                    log.error("手动检查用户 {} 学习记录失败", userStats.getUserId(), e);
                }
            }
            
            updateTaskCompleted(taskLog.getId(), startTime, processedCount);
            log.info("手动每日学习检查完成，处理用户数: {}", processedCount);
            
        } catch (Exception e) {
            log.error("手动每日学习检查失败", e);
            updateTaskFailed(taskLog.getId(), startTime, e.getMessage());
            throw new RuntimeException("手动每日学习检查失败", e);
        }
    }

    /**
     * 获取任务执行统计
     * 
     * @param days 最近天数
     * @return 执行统计
     */
    public List<java.util.Map<String, Object>> getTaskStatistics(int days) {
        LocalDate startDate = LocalDate.now().minusDays(days);
        return scheduledTaskLogMapper.selectTaskStatistics(startDate);
    }
}
