/*
 Navicat Premium Data Transfer

 Source Server         : 云数据库
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : rm-cn-zqb4bu1mo0005xeo.rwlb.rds.aliyuncs.com:3306
 Source Schema         : kaogong_db

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 21/07/2025 11:33:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for announcements
-- ----------------------------
DROP TABLE IF EXISTS `announcements`;
CREATE TABLE `announcements`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `region` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地区',
  `exam_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '考试类型',
  `important_dates` json NULL COMMENT '重要时间节点',
  `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '优先级',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'published' COMMENT '状态',
  `created_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者ID',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_region`(`region` ASC) USING BTREE,
  INDEX `idx_exam_type`(`exam_type` ASC) USING BTREE,
  INDEX `idx_published_at`(`published_at` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `fk_announcements_created_by`(`created_by` ASC) USING BTREE,
  INDEX `idx_region_type_status`(`region` ASC, `exam_type` ASC, `status` ASC) USING BTREE,
  INDEX `idx_status_published`(`status` ASC, `published_at` ASC) USING BTREE,
  FULLTEXT INDEX `ft_title_content`(`title`, `content`) WITH PARSER `ngram`,
  CONSTRAINT `fk_announcements_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '考试公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for desk_applications
-- ----------------------------
DROP TABLE IF EXISTS `desk_applications`;
CREATE TABLE `desk_applications`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请ID',
  `desk_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小桌ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请者ID',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请理由',
  `study_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '学习计划',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '申请状态',
  `applied_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `processed_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '处理人ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_desk_id`(`desk_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_applied_at`(`applied_at` ASC) USING BTREE,
  INDEX `fk_desk_applications_processed_by`(`processed_by` ASC) USING BTREE,
  INDEX `idx_desk_status_applied`(`desk_id` ASC, `status` ASC, `applied_at` ASC) USING BTREE,
  CONSTRAINT `fk_desk_applications_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_desk_applications_processed_by` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_desk_applications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for desk_members
-- ----------------------------
DROP TABLE IF EXISTS `desk_members`;
CREATE TABLE `desk_members`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '成员关系ID',
  `desk_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小桌ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'member' COMMENT '角色(owner/member)',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '成员状态',
  `join_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '加入理由',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `last_active_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_desk_user`(`desk_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_desk_id`(`desk_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_desk_status_active`(`desk_id` ASC, `status` ASC, `last_active_at` ASC) USING BTREE,
  INDEX `idx_user_role`(`user_id` ASC, `role` ASC) USING BTREE,
  CONSTRAINT `fk_desk_members_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_desk_members_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌成员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for desks
-- ----------------------------
DROP TABLE IF EXISTS `desks`;
CREATE TABLE `desks`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小桌ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '小桌名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '小桌描述',
  `owner_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '桌长ID',
  `max_members` int NOT NULL DEFAULT 8 COMMENT '最大成员数',
  `current_members` int NOT NULL DEFAULT 1 COMMENT '当前成员数',
  `auto_approve_rules` json NULL COMMENT '自动审核规则',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '小桌状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_owner_id`(`owner_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `fk_desks_owner_id` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '小桌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for file_upload_logs
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_logs`;
CREATE TABLE `file_upload_logs`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上传用户ID',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `upload_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'success' COMMENT '上传状态：success|failed|deleted',
  `related_table` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联表名',
  `related_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联记录ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上传IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_file_path`(`file_path` ASC) USING BTREE,
  INDEX `idx_upload_status`(`upload_status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_related`(`related_table` ASC, `related_id` ASC) USING BTREE,
  CONSTRAINT `fk_file_upload_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件上传日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for invite_codes
-- ----------------------------
DROP TABLE IF EXISTS `invite_codes`;
CREATE TABLE `invite_codes`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码ID',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `created_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者ID（NULL表示系统生成）',
  `used_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用者ID',
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '过期时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE,
  INDEX `idx_created_by`(`created_by` ASC) USING BTREE,
  INDEX `idx_used_by`(`used_by` ASC) USING BTREE,
  INDEX `idx_expires_at`(`expires_at` ASC) USING BTREE,
  CONSTRAINT `fk_invite_codes_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_invite_codes_used_by` FOREIGN KEY (`used_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '邀请码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知类型',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知内容',
  `metadata` json NULL COMMENT '额外数据',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_is_read`(`is_read` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_user_read_created`(`user_id` ASC, `is_read` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_type_created`(`type` ASC, `created_at` ASC) USING BTREE,
  CONSTRAINT `fk_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rankings
-- ----------------------------
DROP TABLE IF EXISTS `rankings`;
CREATE TABLE `rankings`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '排行记录ID',
  `desk_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '小桌ID(NULL为全站)',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `ranking_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '排行类型',
  `score` int NOT NULL COMMENT '排行分数',
  `rank_position` int NOT NULL COMMENT '排名位置',
  `calculated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_desk_user_type_date`(`desk_id` ASC, `user_id` ASC, `ranking_type` ASC, `calculated_at` ASC) USING BTREE,
  INDEX `idx_desk_id`(`desk_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_ranking_type`(`ranking_type` ASC) USING BTREE,
  INDEX `idx_rank_position`(`rank_position` ASC) USING BTREE,
  INDEX `idx_type_desk_rank`(`ranking_type` ASC, `desk_id` ASC, `rank_position` ASC) USING BTREE,
  INDEX `idx_user_type_calculated`(`user_id` ASC, `ranking_type` ASC, `calculated_at` ASC) USING BTREE,
  CONSTRAINT `fk_rankings_desk_id` FOREIGN KEY (`desk_id`) REFERENCES `desks` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_rankings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '排行榜表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for reputation_logs
-- ----------------------------
DROP TABLE IF EXISTS `reputation_logs`;
CREATE TABLE `reputation_logs`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `change_type` enum('earn','deduct') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变化类型: earn-获得, deduct-扣除',
  `points` int NOT NULL COMMENT '分数变化',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变化原因',
  `category` enum('daily_login','daily_study','study_quality','desk_performance','desk_violation','platform_violation','inactive','recovery') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类',
  `related_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联ID(如小桌ID、学习记录ID等)',
  `consecutive_days` int NULL DEFAULT 0 COMMENT '连续天数(用于连续学习/未学习记录)',
  `processed_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'system' COMMENT '处理方式: system-系统, manual-手动, admin-管理员',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_change_type`(`change_type` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `fk_reputation_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '信誉记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for scheduled_task_logs
-- ----------------------------
DROP TABLE IF EXISTS `scheduled_task_logs`;
CREATE TABLE `scheduled_task_logs`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录ID',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` enum('running','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '执行状态',
  `processed_count` int NULL DEFAULT 0 COMMENT '处理数量',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_date`(`task_name` ASC, `execution_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_execution_date`(`execution_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '定时任务执行记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for study_plans
-- ----------------------------
DROP TABLE IF EXISTS `study_plans`;
CREATE TABLE `study_plans`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '计划ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '计划标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '计划描述',
  `target_exam_date` date NULL DEFAULT NULL COMMENT '目标考试日期',
  `daily_target_questions` int NULL DEFAULT NULL COMMENT '每日目标题量',
  `daily_target_time` int NULL DEFAULT NULL COMMENT '每日目标时长(分钟)',
  `modules` json NOT NULL COMMENT '学习模块配置',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '计划状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_target_exam_date`(`target_exam_date` ASC) USING BTREE,
  CONSTRAINT `fk_study_plans_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学习计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for study_records
-- ----------------------------
DROP TABLE IF EXISTS `study_records`;
CREATE TABLE `study_records`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记录ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `module_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块类型',
  `question_count` int NOT NULL COMMENT '题目数量',
  `correct_count` int NOT NULL COMMENT '正确数量',
  `accuracy_rate` decimal(5, 2) NOT NULL COMMENT '正确率(%)',
  `study_duration` int NOT NULL COMMENT '学习时长(分钟)',
  `weak_points` json NULL COMMENT '薄弱知识点',
  `study_date` date NOT NULL COMMENT '学习日期',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '学习笔记',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_study_date`(`study_date` ASC) USING BTREE,
  INDEX `idx_module_type`(`module_type` ASC) USING BTREE,
  INDEX `idx_user_date`(`user_id` ASC, `study_date` ASC) USING BTREE,
  INDEX `idx_user_module_date`(`user_id` ASC, `module_type` ASC, `study_date` ASC) USING BTREE,
  INDEX `idx_date_accuracy`(`study_date` ASC, `accuracy_rate` ASC) USING BTREE,
  CONSTRAINT `fk_study_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '刷题记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_reputation_stats
-- ----------------------------
DROP TABLE IF EXISTS `user_reputation_stats`;
CREATE TABLE `user_reputation_stats`  (
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `current_score` int NOT NULL DEFAULT 100 COMMENT '当前信誉分数',
  `current_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'newbie' COMMENT '当前信誉等级',
  `total_earned` int NOT NULL DEFAULT 0 COMMENT '累计获得分数',
  `total_deducted` int NOT NULL DEFAULT 0 COMMENT '累计扣除分数',
  `consecutive_login_days` int NOT NULL DEFAULT 0 COMMENT '连续登录天数',
  `consecutive_study_days` int NOT NULL DEFAULT 0 COMMENT '连续学习天数',
  `consecutive_no_study_days` int NOT NULL DEFAULT 0 COMMENT '连续未学习天数',
  `last_login_date` date NULL DEFAULT NULL COMMENT '最后登录日期',
  `last_study_date` date NULL DEFAULT NULL COMMENT '最后学习日期',
  `last_score_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后分数更新时间',
  `protection_end_time` timestamp NULL DEFAULT NULL COMMENT '保护期结束时间',
  `weekly_deduct_points` int NOT NULL DEFAULT 0 COMMENT '本周已扣分数',
  `monthly_deduct_points` int NOT NULL DEFAULT 0 COMMENT '本月已扣分数',
  `last_weekly_reset` date NULL DEFAULT NULL COMMENT '上次周重置日期',
  `last_monthly_reset` date NULL DEFAULT NULL COMMENT '上次月重置日期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `idx_current_score`(`current_score` ASC) USING BTREE,
  INDEX `idx_current_level`(`current_level` ASC) USING BTREE,
  INDEX `idx_protection_end_time`(`protection_end_time` ASC) USING BTREE,
  CONSTRAINT `fk_user_reputation_stats_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户信誉统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户唯一标识(UUID)',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱地址',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希值',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `target_position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标岗位',
  `reputation_score` int NOT NULL DEFAULT 100 COMMENT '信誉分数',
  `reputation_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'newbie' COMMENT '信誉等级',
  `system_role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT '系统角色',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '账户状态',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `uk_email`(`email` ASC) USING BTREE,
  INDEX `idx_reputation_score`(`reputation_score` ASC) USING BTREE,
  INDEX `idx_reputation_level`(`reputation_level` ASC) USING BTREE,
  INDEX `idx_system_role`(`system_role` ASC) USING BTREE,
  INDEX `idx_reputation_role`(`reputation_level` ASC, `system_role` ASC) USING BTREE,
  INDEX `idx_active_created`(`is_active` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wrong_question_images
-- ----------------------------
DROP TABLE IF EXISTS `wrong_question_images`;
CREATE TABLE `wrong_question_images`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片ID',
  `wrong_question_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错题ID',
  `image_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片类型：question|answer|explanation',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件存储路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MIME类型',
  `width` int NULL DEFAULT NULL COMMENT '图片宽度(像素)',
  `height` int NULL DEFAULT NULL COMMENT '图片高度(像素)',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `is_compressed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已压缩',
  `thumbnail_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '缩略图路径',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_wrong_question_id`(`wrong_question_id` ASC) USING BTREE,
  INDEX `idx_image_type`(`image_type` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_wrong_question_type`(`wrong_question_id` ASC, `image_type` ASC) USING BTREE,
  INDEX `idx_wrong_question_sort`(`wrong_question_id` ASC, `image_type` ASC, `sort_order` ASC) USING BTREE,
  CONSTRAINT `fk_wrong_question_images_wrong_question_id` FOREIGN KEY (`wrong_question_id`) REFERENCES `wrong_questions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `chk_file_size` CHECK ((`file_size` > 0) and (`file_size` <= 10485760)),
  CONSTRAINT `chk_image_type` CHECK (`image_type` in (_utf8mb4'question',_utf8mb4'answer',_utf8mb4'explanation')),
  CONSTRAINT `chk_sort_order` CHECK (`sort_order` >= 0)
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错题图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wrong_questions
-- ----------------------------
DROP TABLE IF EXISTS `wrong_questions`;
CREATE TABLE `wrong_questions`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '错题ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户ID',
  `study_record_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联的学习记录',
  `module_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '学习模块类型',
  `question_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题目类型',
  `difficulty_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '难度等级',
  `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '题目内容',
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户答案',
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '正确答案',
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '解析',
  `mastery_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_mastered' COMMENT '掌握状态',
  `review_count` int NOT NULL DEFAULT 0 COMMENT '复习次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '最后复习时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_mastery_status`(`mastery_status` ASC) USING BTREE,
  INDEX `idx_question_type`(`question_type` ASC) USING BTREE,
  INDEX `fk_wrong_questions_study_record_id`(`study_record_id` ASC) USING BTREE,
  INDEX `idx_user_mastery`(`user_id` ASC, `mastery_status` ASC) USING BTREE,
  INDEX `idx_type_difficulty`(`question_type` ASC, `difficulty_level` ASC) USING BTREE,
  CONSTRAINT `fk_wrong_questions_study_record_id` FOREIGN KEY (`study_record_id`) REFERENCES `study_records` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `fk_wrong_questions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '错题本表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for v_user_reputation_overview
-- ----------------------------
DROP VIEW IF EXISTS `v_user_reputation_overview`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_reputation_overview` AS select `u`.`id` AS `id`,`u`.`username` AS `username`,`u`.`nickname` AS `nickname`,`urs`.`current_score` AS `current_score`,`urs`.`current_level` AS `current_level`,`urs`.`total_earned` AS `total_earned`,`urs`.`total_deducted` AS `total_deducted`,`urs`.`consecutive_login_days` AS `consecutive_login_days`,`urs`.`consecutive_study_days` AS `consecutive_study_days`,`urs`.`consecutive_no_study_days` AS `consecutive_no_study_days`,`urs`.`protection_end_time` AS `protection_end_time`,(case when ((`urs`.`protection_end_time` is not null) and (`urs`.`protection_end_time` > now())) then true else false end) AS `is_in_protection`,(case when ((`urs`.`protection_end_time` is not null) and (`urs`.`protection_end_time` > now())) then timestampdiff(HOUR,now(),`urs`.`protection_end_time`) else 0 end) AS `protection_hours_remaining`,`urs`.`last_login_date` AS `last_login_date`,`urs`.`last_study_date` AS `last_study_date`,`urs`.`updated_at` AS `updated_at` from (`users` `u` left join `user_reputation_stats` `urs` on((`u`.`id` = `urs`.`user_id`)));

-- ----------------------------
-- Procedure structure for CheckUserProtectionStatus
-- ----------------------------
DROP PROCEDURE IF EXISTS `CheckUserProtectionStatus`;
delimiter ;;
CREATE PROCEDURE `CheckUserProtectionStatus`(IN userId VARCHAR(36),
  OUT isInProtection BOOLEAN,
  OUT hoursRemaining INT)
BEGIN
  DECLARE protectionEndTime TIMESTAMP;
  
  -- 获取保护期结束时间
  SELECT protection_end_time INTO protectionEndTime
  FROM user_reputation_stats 
  WHERE user_id = userId;
  
  -- 判断是否在保护期内
  IF protectionEndTime IS NULL OR protectionEndTime <= NOW() THEN
    SET isInProtection = FALSE;
    SET hoursRemaining = 0;
  ELSE
    SET isInProtection = TRUE;
    SET hoursRemaining = TIMESTAMPDIFF(HOUR, NOW(), protectionEndTime);
  END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for UpdateUserReputationLevel
-- ----------------------------
DROP PROCEDURE IF EXISTS `UpdateUserReputationLevel`;
delimiter ;;
CREATE PROCEDURE `UpdateUserReputationLevel`(IN userId VARCHAR(36))
BEGIN
  DECLARE currentScore INT;
  DECLARE newLevel VARCHAR(20);
  
  -- 获取当前分数
  SELECT current_score INTO currentScore 
  FROM user_reputation_stats 
  WHERE user_id = userId;
  
  -- 根据分数计算等级
  IF currentScore < 100 THEN
    SET newLevel = 'newbie';
  ELSEIF currentScore < 300 THEN
    SET newLevel = 'bronze';
  ELSEIF currentScore < 600 THEN
    SET newLevel = 'silver';
  ELSEIF currentScore < 1000 THEN
    SET newLevel = 'gold';
  ELSEIF currentScore < 2000 THEN
    SET newLevel = 'platinum';
  ELSEIF currentScore < 5000 THEN
    SET newLevel = 'diamond';
  ELSEIF currentScore < 10000 THEN
    SET newLevel = 'master';
  ELSE
    SET newLevel = 'grandmaster';
  END IF;
  
  -- 更新信誉统计表
  UPDATE user_reputation_stats 
  SET current_level = newLevel,
      updated_at = NOW()
  WHERE user_id = userId;
  
  -- 同步更新users表
  UPDATE users 
  SET reputation_score = currentScore,
      reputation_level = newLevel,
      updated_at = NOW()
  WHERE id = userId;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table users
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_users_after_insert_reputation`;
delimiter ;;
CREATE TRIGGER `tr_users_after_insert_reputation` AFTER INSERT ON `users` FOR EACH ROW BEGIN
  -- 为新注册用户创建信誉统计记录
  INSERT INTO user_reputation_stats (
    user_id,
    current_score,
    current_level,
    protection_end_time,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    COALESCE(NEW.reputation_score, 100),
    COALESCE(NEW.reputation_level, 'newbie'),
    DATE_ADD(NEW.created_at, INTERVAL 3 DAY), -- 3天保护期
    NOW(),
    NOW()
  );
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
