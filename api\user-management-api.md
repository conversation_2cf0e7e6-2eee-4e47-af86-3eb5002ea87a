# 用户管理模块 API 接口文档

## 模块概述
用户管理模块负责用户注册、登录、个人资料管理和邀请码系统，是系统的核心基础模块。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 用户管理 | 用户注册 | `/api/v1/auth/register` | POST | `{"username": "string", "email": "string", "password": "string", "inviteCode": "string", "nickname": "string?"}` | `{"code": 200, "message": "注册成功", "data": {"userId": "uuid", "username": "string"}}` | 无 | 需要有效邀请码 |
| 用户管理 | 用户登录 | `/api/v1/auth/login` | POST | `{"username": "string", "password": "string"}` | `{"code": 200, "data": {"token": "jwt_token", "refreshToken": "string", "userInfo": {...}}}` | 无 | 返回JWT Token |
| 用户管理 | 用户登出 | `/api/v1/auth/logout` | POST | `{}` | `{"code": 200, "message": "登出成功"}` | user | Token加入黑名单 |
| 用户管理 | 刷新Token | `/api/v1/auth/refresh` | POST | `{"refreshToken": "string"}` | `{"code": 200, "data": {"token": "new_jwt_token", "refreshToken": "new_refresh_token"}}` | 无 | 使用refreshToken |
| 用户管理 | 获取个人资料 | `/api/v1/users/profile` | GET | 无 | `{"code": 200, "data": {"id": "uuid", "username": "string", "email": "string", "nickname": "string", "avatarUrl": "string", "targetPosition": "string", "reputationScore": 100, "reputationLevel": "newbie", "createdAt": "datetime"}}` | user | 获取当前用户信息 |
| 用户管理 | 更新个人资料 | `/api/v1/users/profile` | PUT | `{"nickname": "string?", "avatarUrl": "string?", "targetPosition": "string?", "phone": "string?"}` | `{"code": 200, "message": "更新成功", "data": {...}}` | user | 不能修改敏感信息 |
| 用户管理 | 修改密码 | `/api/v1/users/password` | PUT | `{"oldPassword": "string", "newPassword": "string"}` | `{"code": 200, "message": "密码修改成功"}` | user | 需要验证旧密码 |
| 用户管理 | 获取用户列表 | `/api/v1/users` | GET | `page=1&size=20&keyword=string&reputationLevel=string&systemRole=string&isActive=boolean` | `{"code": 200, "data": {"records": [...], "total": 100, "current": 1, "size": 20}}` | admin | 管理员查看用户列表 |
| 用户管理 | 获取用户详情 | `/api/v1/users/{userId}` | GET | 路径参数: `userId` | `{"code": 200, "data": {"id": "uuid", "username": "string", ...}}` | admin | 管理员查看用户详情 |
| 用户管理 | 更新用户状态 | `/api/v1/users/{userId}/status` | PUT | `{"isActive": boolean, "reason": "string?"}` | `{"code": 200, "message": "状态更新成功"}` | admin | 启用/禁用用户账户 |
| 用户管理 | 更新用户角色 | `/api/v1/users/{userId}/role` | PUT | `{"systemRole": "user|admin|super_admin"}` | `{"code": 200, "message": "角色更新成功"}` | super_admin | 只有超级管理员可操作 |
| 邀请码管理 | 生成邀请码 | `/api/v1/invite-codes` | POST | `{"count": 10, "expiresAt": "datetime?", "note": "string?"}` | `{"code": 200, "data": {"codes": ["code1", "code2", ...], "expiresAt": "datetime"}}` | admin | 批量生成邀请码 |
| 邀请码管理 | 验证邀请码 | `/api/v1/invite-codes/validate` | POST | `{"code": "string"}` | `{"code": 200, "data": {"valid": true, "expiresAt": "datetime"}}` | 无 | 注册前验证邀请码 |
| 邀请码管理 | 获取邀请码列表 | `/api/v1/invite-codes` | GET | `page=1&size=20&isActive=boolean&createdBy=string` | `{"code": 200, "data": {"records": [...], "total": 50}}` | admin | 查看邀请码使用情况 |
| 邀请码管理 | 获取邀请统计 | `/api/v1/invite-codes/stats` | GET | `startDate=date&endDate=date` | `{"code": 200, "data": {"totalGenerated": 100, "totalUsed": 80, "usageRate": 80.0}}` | admin | 邀请码使用统计 |

## 数据模型

### 用户信息模型 (UserInfo)
```json
{
  "id": "uuid",
  "username": "string",
  "email": "string", 
  "nickname": "string",
  "avatarUrl": "string",
  "targetPosition": "string",
  "reputationScore": 100,
  "reputationLevel": "newbie|regular|expert|master",
  "systemRole": "user|admin|super_admin",
  "phone": "string",
  "isActive": true,
  "lastLoginAt": "datetime",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

### 邀请码模型 (InviteCode)
```json
{
  "id": "uuid",
  "code": "string",
  "createdBy": "uuid",
  "usedBy": "uuid",
  "expiresAt": "datetime",
  "usedAt": "datetime",
  "isActive": true,
  "createdAt": "datetime"
}
```

## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|---------|------|
| 1001 | 用户名已存在 | 注册时用户名重复 |
| 1002 | 邮箱已存在 | 注册时邮箱重复 |
| 1003 | 邀请码无效 | 邀请码不存在或已过期 |
| 1004 | 邀请码已使用 | 邀请码已被其他用户使用 |
| 1005 | 用户名或密码错误 | 登录失败 |
| 1006 | 账户已被禁用 | 用户账户被管理员禁用 |
| 1007 | Token已过期 | JWT Token过期 |
| 1008 | 权限不足 | 没有访问权限 |
| 1009 | 旧密码错误 | 修改密码时旧密码验证失败 |
| 1010 | 用户不存在 | 查询的用户不存在 |

## 业务规则

### 注册规则
1. 必须使用有效的邀请码
2. 用户名长度3-50字符，只能包含字母、数字、下划线
3. 密码长度8-50字符，必须包含字母和数字
4. 邮箱格式必须正确
5. 注册成功后初始信誉分数为100分，等级为newbie

### 登录规则
1. 支持用户名或邮箱登录
2. 连续登录失败5次锁定账户30分钟
3. JWT Token有效期2小时
4. Refresh Token有效期7天

### 权限规则
1. user: 基础用户权限，可以使用所有普通功能
2. admin: 管理员权限，可以管理用户、公告等
3. super_admin: 超级管理员，拥有所有权限包括用户角色管理

### 邀请码规则
1. 邀请码长度8位，包含字母和数字
2. 默认有效期30天
3. 每个邀请码只能使用一次
4. 管理员可以批量生成邀请码
