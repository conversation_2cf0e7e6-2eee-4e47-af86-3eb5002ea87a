import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  Card, 
  Spin, 
  Toast, 
  Select, 
  Empty, 
  Timeline,
  Button,
  Space
} from '@douyinfe/semi-ui';
import { 
  IconArrowLeft,
  IconFilter,
  IconRefresh,
  IconArrowUp,
  IconArrowDown,
  IconCalendar,
  IconUser,
  IconSetting
} from '@douyinfe/semi-icons';
import Navigation from '../components/Navigation';
import { useReputationStore } from '../stores/useReputationStore';
import { 
  REPUTATION_CATEGORIES, 
  REPUTATION_CATEGORY_NAMES,
  ReputationLog 
} from '../types/reputation';
import '../styles/reputation.css';

const ReputationLogs: React.FC = () => {
  const {
    reputationLogs,
    isLoadingLogs,
    error,
    fetchReputationLogs,
    fetchReputationLogsByCategory,
    clearError
  } = useReputationStore();

  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [limit, setLimit] = useState<number>(20);

  useEffect(() => {
    if (selectedCategory === 'all') {
      fetchReputationLogs(limit);
    } else {
      fetchReputationLogsByCategory(selectedCategory, limit);
    }
  }, [selectedCategory, limit, fetchReputationLogs, fetchReputationLogsByCategory]);

  useEffect(() => {
    if (error) {
      Toast.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
  };

  const handleRefresh = () => {
    if (selectedCategory === 'all') {
      fetchReputationLogs(limit);
    } else {
      fetchReputationLogsByCategory(selectedCategory, limit);
    }
    Toast.success('刷新成功！');
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const getChangeIcon = (changeType: string) => {
    return changeType === 'earn' ? (
      <IconArrowUp style={{ color: 'var(--accent-green)' }} />
    ) : (
      <IconArrowDown style={{ color: 'var(--accent-orange)' }} />
    );
  };

  const getProcessedByIcon = (processedBy: string) => {
    switch (processedBy) {
      case 'system':
        return <IconSetting style={{ color: 'var(--ink-light)' }} />;
      case 'admin':
        return <IconUser style={{ color: 'var(--accent-purple)' }} />;
      default:
        return <IconCalendar style={{ color: 'var(--ink-light)' }} />;
    }
  };

  const renderTimelineItem = (log: ReputationLog) => {
    const isEarn = log.changeType === 'earn';
    const pointsText = `${isEarn ? '+' : '-'}${log.points}分`;
    
    return (
      <Timeline.Item
        key={log.id}
        time={formatDate(log.createdAt)}
        type={isEarn ? 'success' : 'warning'}
        extra={
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            {getChangeIcon(log.changeType)}
            <span 
              style={{ 
                fontFamily: 'var(--font-handwritten)',
                fontWeight: '600',
                color: isEarn ? 'var(--accent-green)' : 'var(--accent-orange)'
              }}
            >
              {pointsText}
            </span>
          </div>
        }
      >
        <div className="timeline-content">
          <div style={{ 
            fontFamily: 'var(--font-handwritten)',
            fontSize: '16px',
            color: 'var(--ink-dark)',
            marginBottom: '5px'
          }}>
            {log.reason}
          </div>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '10px',
            fontSize: '12px',
            color: 'var(--ink-light)'
          }}>
            <span>
              {REPUTATION_CATEGORY_NAMES[log.category as keyof typeof REPUTATION_CATEGORY_NAMES] || log.category}
            </span>
            <span>•</span>
            <div style={{ display: 'flex', alignItems: 'center', gap: '3px' }}>
              {getProcessedByIcon(log.processedBy)}
              <span>
                {log.processedBy === 'system' ? '系统' : 
                 log.processedBy === 'admin' ? '管理员' : '手动'}
              </span>
            </div>
            {log.consecutiveDays && log.consecutiveDays > 0 && (
              <>
                <span>•</span>
                <span>连续{log.consecutiveDays}天</span>
              </>
            )}
          </div>
        </div>
      </Timeline.Item>
    );
  };

  return (
    <div className="reputation-page">
      <Navigation />
      <div className="reputation-container" style={{ paddingTop: '80px' }}>
        {/* 页面标题和导航 */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          marginBottom: '30px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <Link 
              to="/reputation" 
              className="sketch-button"
              style={{ transform: 'rotate(0deg)' }}
            >
              <IconArrowLeft />
              返回
            </Link>
            <h1 className="reputation-title" style={{ margin: 0, fontSize: '28px' }}>
              📋 信誉记录
            </h1>
          </div>
          
          <Button 
            icon={<IconRefresh />}
            onClick={handleRefresh}
            style={{
              background: 'var(--paper-bg)',
              border: '2px solid var(--ink-dark)',
              borderRadius: '20px',
              fontFamily: 'var(--font-handwritten)'
            }}
          >
            刷新
          </Button>
        </div>

        {/* 筛选器 */}
        <Card 
          style={{
            background: 'var(--paper-warm)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            marginBottom: '25px',
            transform: 'rotate(-0.2deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)'
          }}
        >
          <Space wrap>
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <IconFilter style={{ color: 'var(--ink-medium)' }} />
              <span style={{ 
                fontFamily: 'var(--font-handwritten)',
                color: 'var(--ink-dark)'
              }}>
                筛选分类:
              </span>
              <Select
                value={selectedCategory}
                onChange={handleCategoryChange}
                style={{ width: 200 }}
                placeholder="选择分类"
              >
                <Select.Option value="all">全部记录</Select.Option>
                {Object.entries(REPUTATION_CATEGORY_NAMES).map(([key, name]) => (
                  <Select.Option key={key} value={key}>
                    {name}
                  </Select.Option>
                ))}
              </Select>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <span style={{ 
                fontFamily: 'var(--font-handwritten)',
                color: 'var(--ink-dark)'
              }}>
                显示数量:
              </span>
              <Select
                value={limit}
                onChange={setLimit}
                style={{ width: 120 }}
              >
                <Select.Option value={10}>10条</Select.Option>
                <Select.Option value={20}>20条</Select.Option>
                <Select.Option value={50}>50条</Select.Option>
                <Select.Option value={100}>100条</Select.Option>
              </Select>
            </div>
          </Space>
        </Card>

        {/* 记录列表 */}
        <Card 
          style={{
            background: 'var(--paper-bg)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            transform: 'rotate(0.1deg)',
            boxShadow: '3px 3px 0px var(--shadow-light)',
            minHeight: '400px'
          }}
        >
          {isLoadingLogs ? (
            <div style={{ textAlign: 'center', padding: '60px 0' }}>
              <Spin size="large" />
              <p style={{ marginTop: '20px', color: 'var(--ink-medium)' }}>
                正在加载记录...
              </p>
            </div>
          ) : reputationLogs.length === 0 ? (
            <Empty
              image={<div style={{ fontSize: '48px' }}>📝</div>}
              title="暂无记录"
              description={
                <span style={{ color: 'var(--ink-medium)' }}>
                  {selectedCategory === 'all' 
                    ? '还没有任何信誉变更记录' 
                    : `暂无${REPUTATION_CATEGORY_NAMES[selectedCategory as keyof typeof REPUTATION_CATEGORY_NAMES]}相关记录`
                  }
                </span>
              }
              style={{ padding: '60px 0' }}
            />
          ) : (
            <Timeline style={{ padding: '20px 0' }}>
              {reputationLogs.map(renderTimelineItem)}
            </Timeline>
          )}
        </Card>

        {/* 底部提示 */}
        {reputationLogs.length > 0 && (
          <div style={{ 
            textAlign: 'center', 
            marginTop: '20px',
            color: 'var(--ink-light)',
            fontFamily: 'var(--font-handwritten)'
          }}>
            💡 显示最近 {reputationLogs.length} 条记录
            {selectedCategory !== 'all' && (
              <span> • 当前筛选: {REPUTATION_CATEGORY_NAMES[selectedCategory as keyof typeof REPUTATION_CATEGORY_NAMES]}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ReputationLogs;
