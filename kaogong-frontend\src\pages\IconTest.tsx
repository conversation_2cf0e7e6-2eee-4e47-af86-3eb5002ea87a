import React from 'react';
import { Card, Space, Typography, Button } from '@douyinfe/semi-ui';
import {
  IconEyeOpened,
  IconDelete,
  IconUpload,
  IconDownload,
  IconArrowLeft,
  IconArrowRight,
  IconPlus,
  IconRefresh,
  IconFilter
} from '@douyinfe/semi-icons';

const { Title, Text } = Typography;

const IconTest: React.FC = () => {
  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <Title heading={3}>图标测试页面</Title>
        <Text type="secondary">
          验证Semi Design图标是否正确导入和显示
        </Text>
        
        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>基础图标测试</Title>
          <Space wrap>
            <Button icon={<IconEyeOpened />}>查看详情</Button>
            <Button type="danger" icon={<IconDelete />}>删除</Button>
            <Button icon={<IconUpload />}>上传</Button>
            <Button icon={<IconDownload />}>下载</Button>
            <Button icon={<IconPlus />}>添加</Button>
            <Button icon={<IconRefresh />}>刷新</Button>
            <Button icon={<IconFilter />}>筛选</Button>
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>导航图标测试</Title>
          <Space>
            <Button icon={<IconArrowLeft />}>上一页</Button>
            <Button icon={<IconArrowRight />}>下一页</Button>
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>单独图标测试</Title>
          <Space size="large">
            <div style={{ textAlign: 'center' }}>
              <IconEyeOpened size="large" />
              <div><Text size="small">IconEyeOpened</Text></div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <IconDelete size="large" style={{ color: 'var(--semi-color-danger)' }} />
              <div><Text size="small">IconDelete</Text></div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <IconUpload size="large" style={{ color: 'var(--semi-color-primary)' }} />
              <div><Text size="small">IconUpload</Text></div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <IconDownload size="large" style={{ color: 'var(--semi-color-success)' }} />
              <div><Text size="small">IconDownload</Text></div>
            </div>
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>图标尺寸测试</Title>
          <Space align="center">
            <IconEyeOpened size="extra-small" />
            <Text size="small">extra-small</Text>
            <IconEyeOpened size="small" />
            <Text size="small">small</Text>
            <IconEyeOpened size="default" />
            <Text size="small">default</Text>
            <IconEyeOpened size="large" />
            <Text size="small">large</Text>
            <IconEyeOpened size="extra-large" />
            <Text size="small">extra-large</Text>
          </Space>
        </div>

        <div style={{ marginTop: '24px' }}>
          <Title heading={4}>图标动画测试</Title>
          <Space>
            <IconRefresh spin />
            <Text>旋转动画</Text>
            <IconArrowRight rotate={90} />
            <Text>90度旋转</Text>
            <IconArrowRight rotate={180} />
            <Text>180度旋转</Text>
          </Space>
        </div>

        <div style={{ 
          marginTop: '24px', 
          padding: '16px', 
          backgroundColor: 'var(--semi-color-success-light-default)',
          borderRadius: '6px'
        }}>
          <Text strong style={{ color: 'var(--semi-color-success)' }}>
            ✅ 如果您能看到上述所有图标正常显示，说明图标导入配置正确！
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default IconTest;
