package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.ImageUploadRequest;
import com.wqh.publicexaminationassistant.dto.request.WrongQuestionRequest;
import com.wqh.publicexaminationassistant.dto.response.ImageUploadResponse;
import com.wqh.publicexaminationassistant.dto.response.WrongQuestionResponse;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.WrongQuestionImageService;
import com.wqh.publicexaminationassistant.service.WrongQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 错题管理控制器
 * 提供错题的创建、查询、更新、删除等功能
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@RestController
@RequestMapping("/v1/wrong-questions")
@RequiredArgsConstructor
@Validated
@Tag(name = "错题管理", description = "错题本相关接口")
public class WrongQuestionController {

    private final WrongQuestionService wrongQuestionService;
    private final WrongQuestionImageService wrongQuestionImageService;

    @PostMapping
    @Operation(summary = "创建错题", description = "添加新的错题到错题本")
    public ApiResponse<WrongQuestionResponse> createWrongQuestion(
            @Valid @RequestBody WrongQuestionRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户创建错题: userId={}, questionType={}, difficultyLevel={}", 
                userDetails.getId(), request.getQuestionType(), request.getDifficultyLevel());
        
        WrongQuestionResponse response = wrongQuestionService.createWrongQuestion(request, userDetails.getId());
        return ApiResponse.success(response, "错题创建成功");
    }

    @GetMapping("/{wrongQuestionId}")
    @Operation(summary = "获取错题详情", description = "根据ID获取错题的详细信息")
    public ApiResponse<WrongQuestionResponse> getWrongQuestionById(
            @Parameter(description = "错题ID") 
            @PathVariable String wrongQuestionId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查询错题详情: userId={}, wrongQuestionId={}", 
                userDetails.getId(), wrongQuestionId);
        
        WrongQuestionResponse response = wrongQuestionService.getWrongQuestionById(wrongQuestionId, userDetails.getId());
        return ApiResponse.success(response, "获取错题详情成功");
    }

    @PutMapping("/{wrongQuestionId}")
    @Operation(summary = "更新错题", description = "更新错题的内容信息")
    public ApiResponse<WrongQuestionResponse> updateWrongQuestion(
            @Parameter(description = "错题ID") 
            @PathVariable String wrongQuestionId,
            @Valid @RequestBody WrongQuestionRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户更新错题: userId={}, wrongQuestionId={}", 
                userDetails.getId(), wrongQuestionId);
        
        WrongQuestionResponse response = wrongQuestionService.updateWrongQuestion(wrongQuestionId, request, userDetails.getId());
        return ApiResponse.success(response, "错题更新成功");
    }

    @DeleteMapping("/{wrongQuestionId}")
    @Operation(summary = "删除错题", description = "从错题本中删除指定错题")
    public ApiResponse<Void> deleteWrongQuestion(
            @Parameter(description = "错题ID")
            @PathVariable String wrongQuestionId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户删除错题: userId={}, wrongQuestionId={}",
                userDetails.getId(), wrongQuestionId);

        wrongQuestionService.deleteWrongQuestion(wrongQuestionId, userDetails.getId());
        return ApiResponse.success(null, "错题删除成功");
    }

    @PutMapping("/{wrongQuestionId}/review")
    @Operation(summary = "增加错题复习次数", description = "当用户查看错题详情时，自动增加复习次数")
    public ApiResponse<Void> incrementReviewCount(
            @Parameter(description = "错题ID")
            @PathVariable String wrongQuestionId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户复习错题: userId={}, wrongQuestionId={}",
                userDetails.getId(), wrongQuestionId);

        wrongQuestionService.incrementReviewCount(wrongQuestionId, userDetails.getId());
        return ApiResponse.success(null, "复习次数更新成功");
    }

    // ==================== 图片管理相关接口 ====================

    @PostMapping("/{wrongQuestionId}/images")
    @Operation(summary = "上传错题图片", description = "为指定错题上传图片")
    public ApiResponse<ImageUploadResponse> uploadWrongQuestionImage(
            @Parameter(description = "错题ID")
            @PathVariable String wrongQuestionId,
            @Parameter(description = "上传的图片文件")
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "图片类型")
            @RequestParam("imageType") String imageType,
            @Parameter(description = "排序顺序")
            @RequestParam(value = "sortOrder", defaultValue = "0") Integer sortOrder,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户上传错题图片: userId={}, wrongQuestionId={}, imageType={}, fileName={}, fileSize={}",
                userDetails.getId(), wrongQuestionId, imageType,
                file.getOriginalFilename(), file.getSize());

        // 构建请求对象
        ImageUploadRequest request = new ImageUploadRequest();
        request.setImageType(imageType);
        request.setSortOrder(sortOrder);

        ImageUploadResponse response = wrongQuestionImageService.uploadImage(
                wrongQuestionId, file, request, userDetails.getId());
        return ApiResponse.success(response, "图片上传成功");
    }

    @GetMapping("/{wrongQuestionId}/images")
    @Operation(summary = "获取错题图片列表", description = "获取指定错题的所有图片")
    public ApiResponse<List<ImageUploadResponse>> getWrongQuestionImages(
            @Parameter(description = "错题ID")
            @PathVariable String wrongQuestionId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户查询错题图片列表: userId={}, wrongQuestionId={}",
                userDetails.getId(), wrongQuestionId);

        List<ImageUploadResponse> images = wrongQuestionImageService.getImagesByWrongQuestionId(
                wrongQuestionId, userDetails.getId());
        return ApiResponse.success(images, "获取图片列表成功");
    }

    @DeleteMapping("/images/{imageId}")
    @Operation(summary = "删除错题图片", description = "删除指定的错题图片")
    public ApiResponse<Void> deleteWrongQuestionImage(
            @Parameter(description = "图片ID")
            @PathVariable String imageId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {

        log.info("用户删除错题图片: userId={}, imageId={}",
                userDetails.getId(), imageId);

        wrongQuestionImageService.deleteImage(imageId, userDetails.getId());
        return ApiResponse.success(null, "图片删除成功");
    }
}
