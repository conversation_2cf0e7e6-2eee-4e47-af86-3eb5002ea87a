# 排行榜模块 API 接口文档

## 模块概述
排行榜模块提供多维度的用户排名功能，包括全站排行榜、小桌排行榜、学习统计排行等，激励用户学习积极性。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 全站排行 | 获取学习排行榜 | `/api/v1/rankings/study` | GET | `type=questions|accuracy|time&period=daily|weekly|monthly&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "score": 1500, "change": "+2", "period": "weekly"}], "total": 1000, "myRank": 25, "myScore": 850}}` | user | 学习相关排行榜 |
| 全站排行 | 获取信誉排行榜 | `/api/v1/rankings/reputation` | GET | `period=all|monthly&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "reputationScore": 850, "level": "master", "change": "+5"}], "myRank": 156}}` | user | 信誉分数排行榜 |
| 全站排行 | 获取活跃度排行榜 | `/api/v1/rankings/activity` | GET | `period=weekly|monthly&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "activityScore": 95, "studyDays": 28, "change": "0"}], "myRank": 45}}` | user | 用户活跃度排行 |
| 全站排行 | 获取综合排行榜 | `/api/v1/rankings/overall` | GET | `period=monthly&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "overallScore": 920, "studyScore": 800, "reputationScore": 750, "activityScore": 90}], "myRank": 78}}` | user | 综合评分排行榜 |
| 小桌排行 | 获取小桌内排行 | `/api/v1/rankings/desks/{deskId}` | GET | `type=study|activity&period=daily|weekly|monthly&limit=10` | `{"code": 200, "data": {"deskInfo": {...}, "records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "score": 450, "change": "+1"}], "myRank": 3}}` | member | 小桌成员排行 |
| 小桌排行 | 获取小桌对比 | `/api/v1/rankings/desks/comparison` | GET | `deskIds=uuid1,uuid2&type=average_score&period=weekly` | `{"code": 200, "data": [{"deskId": "uuid1", "deskName": "学霸小桌", "averageScore": 750, "rank": 1}, {"deskId": "uuid2", "deskName": "努力小桌", "averageScore": 680, "rank": 2}]}` | user | 小桌间对比排行 |
| 小桌排行 | 获取小桌排行榜 | `/api/v1/rankings/desks` | GET | `type=average_score|total_members|activity&period=monthly&limit=20&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "deskId": "uuid", "deskInfo": {...}, "score": 850, "memberCount": 8}], "total": 100}}` | user | 小桌整体排行 |
| 专项排行 | 获取模块排行榜 | `/api/v1/rankings/modules/{moduleType}` | GET | `type=accuracy|speed&period=weekly|monthly&limit=50&page=1` | `{"code": 200, "data": {"moduleType": "math", "records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "accuracyRate": 95.5, "averageTime": 45}], "myRank": 12}}` | user | 按学习模块排行 |
| 专项排行 | 获取新手排行榜 | `/api/v1/rankings/newcomers` | GET | `period=monthly&limit=20` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "progress": 85, "daysActive": 15, "registeredAt": "datetime"}]}}` | user | 新用户进步排行 |
| 专项排行 | 获取连续学习排行 | `/api/v1/rankings/streak` | GET | `type=current|longest&limit=50&page=1` | `{"code": 200, "data": {"records": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "streakDays": 45, "lastStudyDate": "2024-01-15"}], "myRank": 23, "myStreak": 12}}` | user | 连续学习天数排行 |
| 历史排行 | 获取排行历史 | `/api/v1/rankings/history` | GET | `userId=uuid&type=study&period=weekly&limit=10` | `{"code": 200, "data": [{"period": "2024-W03", "rank": 25, "score": 850, "change": "+5"}, {"period": "2024-W02", "rank": 30, "score": 800, "change": "-2"}]}` | user | 个人排名变化历史 |
| 历史排行 | 获取最佳排名 | `/api/v1/rankings/best-ranks` | GET | `userId=uuid&type=all` | `{"code": 200, "data": {"studyRank": {"best": 15, "period": "2024-W01", "score": 950}, "reputationRank": {"best": 45, "period": "2024-01", "score": 680}}}` | user | 历史最佳排名记录 |
| 历史排行 | 获取排行趋势 | `/api/v1/rankings/trends` | GET | `userId=uuid&type=study&period=3months&granularity=week` | `{"code": 200, "data": [{"period": "2024-W01", "rank": 30, "score": 800}, {"period": "2024-W02", "rank": 25, "score": 850}]}` | user | 排名趋势分析 |
| 统计分析 | 获取排行统计 | `/api/v1/rankings/stats` | GET | `type=study&period=monthly` | `{"code": 200, "data": {"totalParticipants": 1000, "averageScore": 650, "medianScore": 600, "topPercentile": 900, "distribution": [{"range": "0-100", "count": 50}]}}` | user | 排行榜统计信息 |
| 统计分析 | 获取个人统计 | `/api/v1/rankings/personal-stats` | GET | `userId=uuid&period=year` | `{"code": 200, "data": {"bestRank": 15, "averageRank": 45, "rankImprovement": "+20", "consistencyScore": 75, "participationRate": 85}}` | user | 个人排行表现统计 |
| 统计分析 | 获取竞争分析 | `/api/v1/rankings/competition` | GET | `userId=uuid&type=study&period=monthly` | `{"code": 200, "data": {"currentRank": 25, "nearbyUsers": [{"rank": 24, "userId": "uuid", "gap": 15}, {"rank": 26, "userId": "uuid", "gap": -10}], "nextTarget": {"rank": 20, "gap": 85}}}` | user | 竞争对手分析 |
| 管理功能 | 刷新排行榜 | `/api/v1/rankings/refresh` | POST | `{"type": "study|reputation|activity", "period": "daily|weekly|monthly", "force": true}` | `{"code": 200, "message": "排行榜刷新成功", "data": {"updated": 1000, "duration": "2.5s"}}` | admin | 手动刷新排行榜 |
| 管理功能 | 获取排行配置 | `/api/v1/rankings/config` | GET | 无 | `{"code": 200, "data": {"updateFrequency": "hourly", "cacheExpiry": 300, "maxDisplayCount": 100, "enabledTypes": ["study", "reputation"]}}` | admin | 排行榜配置信息 |
| 管理功能 | 更新排行配置 | `/api/v1/rankings/config` | PUT | `{"updateFrequency": "hourly", "cacheExpiry": 300, "maxDisplayCount": 100, "enabledTypes": ["study", "reputation", "activity"]}` | `{"code": 200, "message": "配置更新成功"}` | admin | 更新排行榜配置 |

## 数据模型

### 排行记录模型 (RankingRecord)
```json
{
  "id": "uuid",
  "deskId": "uuid",
  "userId": "uuid",
  "userInfo": {
    "username": "string",
    "nickname": "string",
    "avatarUrl": "string",
    "reputationLevel": "expert"
  },
  "rankingType": "study_questions|study_accuracy|reputation|activity",
  "score": 850,
  "rankPosition": 25,
  "change": "+2",
  "period": "2024-W03",
  "calculatedAt": "datetime"
}
```

### 排行榜统计模型 (RankingStats)
```json
{
  "type": "study",
  "period": "monthly",
  "totalParticipants": 1000,
  "averageScore": 650,
  "medianScore": 600,
  "topPercentile": 900,
  "distribution": [
    {"range": "0-100", "count": 50, "percentage": 5.0},
    {"range": "101-200", "count": 100, "percentage": 10.0}
  ],
  "updatedAt": "datetime"
}
```

### 个人排行统计模型 (PersonalRankingStats)
```json
{
  "userId": "uuid",
  "period": "year",
  "bestRank": 15,
  "averageRank": 45,
  "rankImprovement": "+20",
  "consistencyScore": 75,
  "participationRate": 85,
  "rankHistory": [
    {"period": "2024-01", "rank": 50, "score": 600},
    {"period": "2024-02", "rank": 35, "score": 750}
  ]
}
```

## 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|---------|------|
| 3001 | 排行榜类型不支持 | 请求的排行榜类型不存在 |
| 3002 | 时间周期无效 | 时间周期参数错误 |
| 3003 | 小桌不存在 | 请求的小桌ID无效 |
| 3004 | 非小桌成员 | 没有权限查看小桌排行榜 |
| 3005 | 排行榜数据更新中 | 排行榜正在计算，请稍后重试 |
| 3006 | 参数超出范围 | limit参数超过最大值 |

## 业务规则

### 排行榜更新规则
1. **实时排行**: 学习记录提交后5分钟内更新
2. **每日排行**: 每天凌晨2点统一计算
3. **每周排行**: 每周一凌晨3点计算
4. **每月排行**: 每月1日凌晨4点计算

### 分数计算规则
1. **学习分数**: 题目数量 × 正确率 × 时间系数
2. **活跃度分数**: 学习天数 × 连续性系数 × 参与度系数
3. **综合分数**: 学习分数 × 0.5 + 信誉分数 × 0.3 + 活跃度分数 × 0.2

### 显示规则
1. **匿名显示**: 排名50名以后可选择匿名显示
2. **新用户保护**: 注册7天内不参与全站排行
3. **小桌排行**: 只有小桌成员可以查看小桌内排行
4. **历史数据**: 保留最近12个月的排行历史
