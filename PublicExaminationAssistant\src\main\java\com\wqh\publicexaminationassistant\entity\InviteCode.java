package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 邀请码表实体类
 * 管理邀请码的生成、使用和追踪
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("invite_codes")
public class InviteCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邀请码ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 邀请码
     */
    @TableField("code")
    private String code;

    /**
     * 创建者ID
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 使用者ID
     */
    @TableField("used_by")
    private String usedBy;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 使用时间
     */
    @TableField("used_at")
    private LocalDateTime usedAt;

    /**
     * 是否有效
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
