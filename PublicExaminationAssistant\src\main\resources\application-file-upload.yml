# =====================================================
# 文件上传功能配置
# 用于错题图片上传功能
# =====================================================

# 应用自定义配置
app:
  upload:
    # 文件上传基础配置
    max-size: 10485760          # 文件上传最大大小(字节) - 10MB
    allowed-types: jpg,jpeg,png,gif,webp  # 允许上传的文件类型
    path: uploads               # 文件上传根路径（相对于项目根目录）
    
    # 图片处理配置
    image:
      max-width: 1920           # 图片最大宽度(像素)
      max-height: 1080          # 图片最大高度(像素)
      quality: 85               # 图片压缩质量(1-100)
      
    # 缩略图配置
    thumbnail:
      width: 200                # 缩略图宽度(像素)
      height: 200               # 缩略图高度(像素)
      quality: 80               # 缩略图质量(1-100)
      
    # 存储配置
    storage:
      type: local               # 存储类型: local|oss|s3
      base-url: /api/files      # 文件访问基础URL
      
  # 文件清理配置
  file-cleanup:
    enabled: true               # 是否启用文件清理
    orphan-check-interval: 24h  # 孤儿文件检查间隔
    retention-days: 30          # 删除记录保留天数

# Spring文件上传配置
spring:
  servlet:
    multipart:
      enabled: true             # 启用文件上传
      max-file-size: 10MB       # 单个文件最大大小
      max-request-size: 50MB    # 请求最大大小(支持多文件)
      file-size-threshold: 2KB  # 文件写入磁盘的阈值
      location: ${java.io.tmpdir}  # 临时文件存储位置

# 日志配置
logging:
  level:
    com.wqh.publicexaminationassistant.service.FileUploadService: DEBUG
    com.wqh.publicexaminationassistant.service.ImageProcessingService: DEBUG
