import React from 'react';
import { Card, Select, Space, Typography, Button } from '@douyinfe/semi-ui';
import { RankingType, RankingPeriod } from '../../services/rankingService';

const { Title, Text } = Typography;

interface FilterPanelSimpleProps {
  selectedType: RankingType;
  selectedPeriod: RankingPeriod;
  onTypeChange: (type: RankingType) => void;
  onPeriodChange: (period: RankingPeriod) => void;
  loading?: boolean;
}

const FilterPanelSimple: React.FC<FilterPanelSimpleProps> = ({
  selectedType,
  selectedPeriod,
  onTypeChange,
  onPeriodChange,
  loading = false
}) => {
  console.log('FilterPanelSimple render:', { selectedType, selectedPeriod, loading });

  const handleTypeChange = (value: any) => {
    console.log('FilterPanelSimple - Type change:', value, typeof value);
    onTypeChange(value);
  };

  const handlePeriodChange = (value: any) => {
    console.log('FilterPanelSimple - Period change:', value, typeof value);
    onPeriodChange(value);
  };

  // 测试按钮
  const testTypeChange = () => {
    console.log('Test button clicked - changing to study_accuracy');
    onTypeChange('study_accuracy');
  };

  const testPeriodChange = () => {
    console.log('Test button clicked - changing to monthly');
    onPeriodChange('monthly');
  };

  return (
    <Card style={{ padding: '20px' }}>
      <Title heading={5}>🏆 排行榜筛选 (简化版)</Title>
      
      <Space vertical spacing="large" style={{ width: '100%', marginTop: '16px' }}>
        {/* 当前状态显示 */}
        <div>
          <Text strong>当前状态:</Text>
          <br />
          <Text>类型: {selectedType}</Text>
          <br />
          <Text>周期: {selectedPeriod}</Text>
          <br />
          <Text>加载中: {loading ? '是' : '否'}</Text>
        </div>

        {/* 测试按钮 */}
        <Space>
          <Button onClick={testTypeChange}>测试类型切换</Button>
          <Button onClick={testPeriodChange}>测试周期切换</Button>
        </Space>

        {/* 排行榜类型选择 */}
        <div>
          <Text strong>排行榜类型</Text>
          <Select
            value={selectedType}
            onChange={handleTypeChange}
            style={{ width: '100%', marginTop: '8px' }}
            placeholder="选择排行榜类型"
            disabled={loading}
          >
            <Select.Option value="study_questions">刷题数量</Select.Option>
            <Select.Option value="study_accuracy">正确率</Select.Option>
            <Select.Option value="study_time">学习时长</Select.Option>
            <Select.Option value="reputation_score">信誉分数</Select.Option>
            <Select.Option value="comprehensive">综合排名</Select.Option>
          </Select>
        </div>

        {/* 时间周期选择 */}
        <div>
          <Text strong>时间周期</Text>
          <Select
            value={selectedPeriod}
            onChange={handlePeriodChange}
            style={{ width: '100%', marginTop: '8px' }}
            placeholder="选择时间周期"
            disabled={loading}
          >
            <Select.Option value="daily">日榜</Select.Option>
            <Select.Option value="weekly">周榜</Select.Option>
            <Select.Option value="monthly">月榜</Select.Option>
            <Select.Option value="all_time">总榜</Select.Option>
          </Select>
        </div>
      </Space>
    </Card>
  );
};

export default FilterPanelSimple;
