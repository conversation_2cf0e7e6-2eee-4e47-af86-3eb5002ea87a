package com.wqh.publicexaminationassistant.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.CreateStudyRecordRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateMasteryStatusRequest;
import com.wqh.publicexaminationassistant.dto.request.UpdateStudyRecordRequest;
import com.wqh.publicexaminationassistant.dto.request.WrongQuestionRequest;
import com.wqh.publicexaminationassistant.dto.response.StudyRecordResponse;
import com.wqh.publicexaminationassistant.dto.response.StudyStatisticsResponse;
import com.wqh.publicexaminationassistant.dto.response.WrongQuestionResponse;
import com.wqh.publicexaminationassistant.entity.StudyRecord;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.entity.WrongQuestion;
import com.wqh.publicexaminationassistant.mapper.StudyRecordMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import com.wqh.publicexaminationassistant.mapper.WrongQuestionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 刷题记录服务类
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudyRecordService {

    private final StudyRecordMapper studyRecordMapper;
    private final WrongQuestionMapper wrongQuestionMapper;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;
    private final ReputationService reputationService;

    /**
     * 创建刷题记录
     */
    @Transactional(rollbackFor = Exception.class)
    public StudyRecordResponse createRecord(CreateStudyRecordRequest request, String userId) {
        log.info("创建刷题记录: userId={}, moduleType={}, questionCount={}", 
                userId, request.getModuleType(), request.getQuestionCount());

        // 验证用户存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 创建学习记录
        StudyRecord studyRecord = new StudyRecord();
        studyRecord.setUserId(userId);
        studyRecord.setModuleType(request.getModuleType());
        studyRecord.setQuestionCount(request.getQuestionCount());
        studyRecord.setCorrectCount(request.getCorrectCount());
        studyRecord.setStudyDuration(request.getStudyDuration());

        // 处理JSON字段 - 空字符串转为null以避免MySQL JSON错误
        studyRecord.setWeakPoints(
            request.getWeakPoints() != null && !request.getWeakPoints().trim().isEmpty()
                ? request.getWeakPoints()
                : null
        );

        studyRecord.setStudyDate(request.getStudyDate());

        // 处理notes字段 - 空字符串转为null
        studyRecord.setNotes(
            request.getNotes() != null && !request.getNotes().trim().isEmpty()
                ? request.getNotes()
                : null
        );

        // 自动计算正确率
        if (request.getQuestionCount() > 0) {
            BigDecimal accuracyRate = BigDecimal.valueOf(request.getCorrectCount() * 100.0 / request.getQuestionCount())
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            studyRecord.setAccuracyRate(accuracyRate);
        }

        studyRecordMapper.insert(studyRecord);

        // 创建关联的错题记录
        if (request.getWrongQuestions() != null && !request.getWrongQuestions().isEmpty()) {
            createWrongQuestions(request.getWrongQuestions(), studyRecord.getId(), userId);
        }

        // 处理信誉系统奖励
        try {
            handleStudyReward(userId, studyRecord);
        } catch (Exception e) {
            log.error("处理学习记录 {} 的信誉奖励失败", studyRecord.getId(), e);
            // 不影响学习记录创建流程
        }

        // 转换为响应对象
        StudyRecordResponse response = convertToResponse(studyRecord);
        response.setUser(convertUserToBasicInfo(user));

        log.info("刷题记录创建成功: recordId={}, accuracyRate={}",
                studyRecord.getId(), studyRecord.getAccuracyRate());

        return response;
    }

    /**
     * 获取刷题记录列表
     */
    public Page<StudyRecordResponse> getRecords(String userId, int page, int size, 
                                                String moduleType, LocalDate startDate, LocalDate endDate,
                                                String sortBy, String sortOrder) {
        log.info("获取刷题记录列表: userId={}, page={}, size={}, moduleType={}", 
                userId, page, size, moduleType);

        // 构建查询条件
        LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StudyRecord::getUserId, userId);
        
        if (StringUtils.hasText(moduleType)) {
            queryWrapper.eq(StudyRecord::getModuleType, moduleType);
        }
        
        if (startDate != null && endDate != null) {
            queryWrapper.between(StudyRecord::getStudyDate, startDate, endDate);
        } else if (startDate != null) {
            queryWrapper.ge(StudyRecord::getStudyDate, startDate);
        } else if (endDate != null) {
            queryWrapper.le(StudyRecord::getStudyDate, endDate);
        }

        // 构建排序
        if ("asc".equalsIgnoreCase(sortOrder)) {
            switch (sortBy) {
                case "accuracyRate":
                    queryWrapper.orderByAsc(StudyRecord::getAccuracyRate);
                    break;
                case "questionCount":
                    queryWrapper.orderByAsc(StudyRecord::getQuestionCount);
                    break;
                case "studyDuration":
                    queryWrapper.orderByAsc(StudyRecord::getStudyDuration);
                    break;
                default:
                    queryWrapper.orderByAsc(StudyRecord::getStudyDate).orderByAsc(StudyRecord::getCreatedAt);
            }
        } else {
            switch (sortBy) {
                case "accuracyRate":
                    queryWrapper.orderByDesc(StudyRecord::getAccuracyRate);
                    break;
                case "questionCount":
                    queryWrapper.orderByDesc(StudyRecord::getQuestionCount);
                    break;
                case "studyDuration":
                    queryWrapper.orderByDesc(StudyRecord::getStudyDuration);
                    break;
                default:
                    queryWrapper.orderByDesc(StudyRecord::getStudyDate).orderByDesc(StudyRecord::getCreatedAt);
            }
        }

        // 分页查询
        Page<StudyRecord> recordPage = studyRecordMapper.selectPage(new Page<>(page, size), queryWrapper);

        // 转换为响应对象
        Page<StudyRecordResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(recordPage, responsePage);
        
        List<StudyRecordResponse> responses = recordPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // 批量查询错题数量
        enrichWithWrongQuestionCounts(responses);

        responsePage.setRecords(responses);
        return responsePage;
    }

    /**
     * 获取刷题记录详情
     */
    public StudyRecordResponse getRecordDetail(String recordId, String userId) {
        log.info("获取刷题记录详情: recordId={}, userId={}", recordId, userId);

        StudyRecord studyRecord = studyRecordMapper.selectOne(
                new LambdaQueryWrapper<StudyRecord>()
                        .eq(StudyRecord::getId, recordId)
                        .eq(StudyRecord::getUserId, userId)
        );

        if (studyRecord == null) {
            throw new BusinessException(ResultCode.RECORD_NOT_FOUND);
        }

        StudyRecordResponse response = convertToResponse(studyRecord);

        // 查询关联的错题数量
        Long wrongQuestionCount = wrongQuestionMapper.countByUserId(userId);
        response.setWrongQuestionCount(wrongQuestionCount.intValue());

        return response;
    }

    /**
     * 更新刷题记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateRecord(String recordId, UpdateStudyRecordRequest request, String userId) {
        log.info("更新刷题记录: recordId={}, userId={}", recordId, userId);

        StudyRecord studyRecord = studyRecordMapper.selectOne(
                new LambdaQueryWrapper<StudyRecord>()
                        .eq(StudyRecord::getId, recordId)
                        .eq(StudyRecord::getUserId, userId)
        );

        if (studyRecord == null) {
            throw new BusinessException(ResultCode.RECORD_NOT_FOUND);
        }

        // 只允许更新笔记和薄弱知识点
        LambdaUpdateWrapper<StudyRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StudyRecord::getId, recordId).eq(StudyRecord::getUserId, userId);

        if (request.getNotes() != null) {
            updateWrapper.set(StudyRecord::getNotes, request.getNotes());
        }
        if (request.getWeakPoints() != null) {
            // 处理薄弱知识点：将逗号分隔的字符串转换为JSON数组格式
            String weakPointsJson = convertWeakPointsToJson(request.getWeakPoints());
            updateWrapper.set(StudyRecord::getWeakPoints, weakPointsJson);
        }

        studyRecordMapper.update(null, updateWrapper);
        log.info("刷题记录更新成功: recordId={}", recordId);
    }

    /**
     * 删除刷题记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecord(String recordId, String userId) {
        log.info("删除刷题记录: recordId={}, userId={}", recordId, userId);

        StudyRecord studyRecord = studyRecordMapper.selectOne(
                new LambdaQueryWrapper<StudyRecord>()
                        .eq(StudyRecord::getId, recordId)
                        .eq(StudyRecord::getUserId, userId)
        );

        if (studyRecord == null) {
            throw new BusinessException(ResultCode.RECORD_NOT_FOUND);
        }

        // 删除关联的错题记录
        wrongQuestionMapper.deleteByStudyRecordId(recordId);

        // 删除学习记录
        studyRecordMapper.deleteById(recordId);
        log.info("刷题记录删除成功: recordId={}", recordId);
    }

    /**
     * 获取学习统计概览
     */
    public StudyStatisticsResponse getStatisticsOverview(String userId, String period) {
        log.info("获取学习统计概览: userId={}, period={}", userId, period);

        StudyStatisticsResponse response = new StudyStatisticsResponse();

        // 基础统计数据
        Long totalStudyDays = studyRecordMapper.countDistinctStudyDaysByUserId(userId);
        Long totalQuestions = studyRecordMapper.sumQuestionCountByUserId(userId);
        Long totalCorrect = studyRecordMapper.sumCorrectCountByUserId(userId);
        Long totalStudyTime = studyRecordMapper.sumStudyDurationByUserId(userId);
        LocalDate lastStudyDate = studyRecordMapper.findLastStudyDateByUserId(userId);

        response.setTotalStudyDays(totalStudyDays.intValue());
        response.setTotalQuestions(totalQuestions.intValue());
        response.setTotalCorrect(totalCorrect.intValue());
        response.setTotalWrong(totalQuestions.intValue() - totalCorrect.intValue());
        response.setTotalStudyTime(totalStudyTime.intValue());
        response.setLastStudyDate(lastStudyDate);

        // 计算平均正确率
        if (totalQuestions > 0) {
            BigDecimal averageAccuracy = BigDecimal.valueOf(totalCorrect * 100.0 / totalQuestions)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            response.setAverageAccuracy(averageAccuracy);
        } else {
            response.setAverageAccuracy(BigDecimal.ZERO);
        }

        // 计算连续学习天数
        calculateStreakDays(userId, response);

        return response;
    }

    /**
     * 获取模块统计数据
     */
    public StudyStatisticsResponse getModuleStatistics(String userId, LocalDate startDate, LocalDate endDate) {
        log.info("获取模块统计数据: userId={}, startDate={}, endDate={}", userId, startDate, endDate);

        StudyStatisticsResponse response = new StudyStatisticsResponse();
        List<Map<String, Object>> moduleStats = studyRecordMapper
                .findModuleStatisticsByUserId(userId, startDate, endDate);

        List<StudyStatisticsResponse.ModuleStatistics> moduleStatsList = moduleStats.stream()
                .map(this::convertToModuleStatistics)
                .collect(Collectors.toList());

        response.setModuleStats(moduleStatsList);
        return response;
    }

    /**
     * 获取学习趋势数据
     */
    public StudyStatisticsResponse getStudyTrends(String userId, String period, String granularity) {
        log.info("获取学习趋势: userId={}, period={}, granularity={}", userId, period, granularity);

        StudyStatisticsResponse response = new StudyStatisticsResponse();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = calculateStartDate(endDate, period);

        List<StudyRecord> records = studyRecordMapper
                .findByUserIdAndDateRange(userId, startDate, endDate);

        List<StudyStatisticsResponse.DailyStudyData> dailyData = aggregateByGranularity(records, granularity);
        response.setDailyData(dailyData);

        return response;
    }

    /**
     * 获取学习日历数据
     */
    public StudyStatisticsResponse getStudyCalendar(String userId, int year, int month) {
        log.info("获取学习日历: userId={}, year={}, month={}", userId, year, month);

        StudyStatisticsResponse response = new StudyStatisticsResponse();
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.plusMonths(1).minusDays(1);

        List<StudyRecord> records = studyRecordMapper
                .findByUserIdAndDateRange(userId, startDate, endDate);

        List<StudyStatisticsResponse.DailyStudyData> dailyData = records.stream()
                .collect(Collectors.groupingBy(StudyRecord::getStudyDate))
                .entrySet().stream()
                .map(entry -> {
                    StudyStatisticsResponse.DailyStudyData data = new StudyStatisticsResponse.DailyStudyData();
                    data.setDate(entry.getKey());

                    List<StudyRecord> dayRecords = entry.getValue();
                    int totalQuestions = dayRecords.stream().mapToInt(StudyRecord::getQuestionCount).sum();
                    int totalCorrect = dayRecords.stream().mapToInt(StudyRecord::getCorrectCount).sum();
                    int totalTime = dayRecords.stream().mapToInt(StudyRecord::getStudyDuration).sum();

                    data.setQuestionCount(totalQuestions);
                    data.setCorrectCount(totalCorrect);
                    data.setStudyTime(totalTime);

                    if (totalQuestions > 0) {
                        BigDecimal accuracyRate = BigDecimal.valueOf(totalCorrect * 100.0 / totalQuestions)
                                .setScale(2, BigDecimal.ROUND_HALF_UP);
                        data.setAccuracyRate(accuracyRate);
                    } else {
                        data.setAccuracyRate(BigDecimal.ZERO);
                    }

                    return data;
                })
                .sorted(Comparator.comparing(StudyStatisticsResponse.DailyStudyData::getDate))
                .collect(Collectors.toList());

        response.setDailyData(dailyData);
        return response;
    }

    /**
     * 获取错题列表（向后兼容方法）
     */
    public Page<WrongQuestionResponse> getWrongQuestions(String userId, int page, int size,
                                                         String questionType, String masteryStatus) {
        return getWrongQuestions(userId, page, size, questionType, null, masteryStatus);
    }

    /**
     * 获取错题列表（支持按学习模块筛选）
     */
    public Page<WrongQuestionResponse> getWrongQuestions(String userId, int page, int size,
                                                         String questionType, String moduleType, String masteryStatus) {
        log.info("获取错题列表: userId={}, page={}, size={}, questionType={}, moduleType={}, masteryStatus={}",
                userId, page, size, questionType, moduleType, masteryStatus);

        // 如果需要按学习模块筛选，使用关联查询
        if (StringUtils.hasText(moduleType)) {
            return getWrongQuestionsByModuleType(userId, page, size, questionType, moduleType, masteryStatus);
        }

        // 普通查询（不需要关联学习记录表）
        LambdaQueryWrapper<WrongQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WrongQuestion::getUserId, userId);

        if (StringUtils.hasText(questionType)) {
            queryWrapper.eq(WrongQuestion::getQuestionType, questionType);
        }

        if (StringUtils.hasText(masteryStatus)) {
            queryWrapper.eq(WrongQuestion::getMasteryStatus, masteryStatus);
        }

        queryWrapper.orderByDesc(WrongQuestion::getCreatedAt);

        // 分页查询
        Page<WrongQuestion> wrongQuestionPage = wrongQuestionMapper.selectPage(new Page<>(page, size), queryWrapper);

        // 转换为响应对象
        Page<WrongQuestionResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(wrongQuestionPage, responsePage);

        List<WrongQuestionResponse> responses = wrongQuestionPage.getRecords().stream()
                .map(this::convertToWrongQuestionResponse)
                .collect(Collectors.toList());

        responsePage.setRecords(responses);
        return responsePage;
    }

    /**
     * 按学习模块筛选错题（需要关联查询）
     */
    private Page<WrongQuestionResponse> getWrongQuestionsByModuleType(String userId, int page, int size,
                                                                      String questionType, String moduleType, String masteryStatus) {
        // 使用自定义SQL进行关联查询
        Page<WrongQuestion> wrongQuestionPage = wrongQuestionMapper.selectWrongQuestionsByModuleType(
                new Page<>(page, size), userId, questionType, moduleType, masteryStatus);

        // 转换为响应对象
        Page<WrongQuestionResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(wrongQuestionPage, responsePage);

        List<WrongQuestionResponse> responses = wrongQuestionPage.getRecords().stream()
                .map(this::convertToWrongQuestionResponse)
                .collect(Collectors.toList());

        responsePage.setRecords(responses);
        return responsePage;
    }

    /**
     * 更新错题掌握状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMasteryStatus(String wrongQuestionId, UpdateMasteryStatusRequest request, String userId) {
        log.info("更新错题掌握状态: wrongQuestionId={}, userId={}, masteryStatus={}",
                wrongQuestionId, userId, request.getMasteryStatus());

        WrongQuestion wrongQuestion = wrongQuestionMapper.selectOne(
                new LambdaQueryWrapper<WrongQuestion>()
                        .eq(WrongQuestion::getId, wrongQuestionId)
                        .eq(WrongQuestion::getUserId, userId)
        );

        if (wrongQuestion == null) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        // 更新掌握状态并增加复习次数
        int updated = wrongQuestionMapper.updateMasteryStatusAndIncrementReviewCount(
                wrongQuestionId, userId, request.getMasteryStatus());

        if (updated == 0) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        log.info("错题掌握状态更新成功: wrongQuestionId={}", wrongQuestionId);
    }

    // 私有辅助方法
    private void createWrongQuestions(List<WrongQuestionRequest> wrongQuestionRequests,
                                      String studyRecordId, String userId) {
        for (WrongQuestionRequest request : wrongQuestionRequests) {
            WrongQuestion wrongQuestion = new WrongQuestion();
            BeanUtils.copyProperties(request, wrongQuestion);
            wrongQuestion.setUserId(userId);
            wrongQuestion.setStudyRecordId(studyRecordId);
            wrongQuestion.setReviewCount(0);
            if (!StringUtils.hasText(wrongQuestion.getMasteryStatus())) {
                wrongQuestion.setMasteryStatus("not_mastered");
            }
            wrongQuestionMapper.insert(wrongQuestion);
        }
    }

    private StudyRecordResponse convertToResponse(StudyRecord studyRecord) {
        StudyRecordResponse response = new StudyRecordResponse();
        BeanUtils.copyProperties(studyRecord, response);

        // 计算错题数量
        if (studyRecord.getQuestionCount() != null && studyRecord.getCorrectCount() != null) {
            response.setWrongCount(studyRecord.getQuestionCount() - studyRecord.getCorrectCount());
        }

        // 设置模块名称
        response.setModuleName(getModuleName(studyRecord.getModuleType()));

        return response;
    }

    private StudyRecordResponse.UserBasicInfo convertUserToBasicInfo(User user) {
        StudyRecordResponse.UserBasicInfo userInfo = new StudyRecordResponse.UserBasicInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setNickname(user.getNickname());
        userInfo.setAvatarUrl(user.getAvatarUrl());
        return userInfo;
    }

    private WrongQuestionResponse convertToWrongQuestionResponse(WrongQuestion wrongQuestion) {
        WrongQuestionResponse response = new WrongQuestionResponse();
        BeanUtils.copyProperties(wrongQuestion, response);

        // 设置中文显示名称
        response.setQuestionTypeName(getQuestionTypeName(wrongQuestion.getQuestionType()));
        response.setDifficultyLevelName(getDifficultyLevelName(wrongQuestion.getDifficultyLevel()));
        response.setMasteryStatusName(getMasteryStatusName(wrongQuestion.getMasteryStatus()));

        return response;
    }

    private void enrichWithWrongQuestionCounts(List<StudyRecordResponse> responses) {
        if (responses.isEmpty()) {
            return;
        }

        // 批量查询错题数量
        for (StudyRecordResponse response : responses) {
            List<WrongQuestion> wrongQuestions = wrongQuestionMapper
                    .findByStudyRecordId(response.getId());
            response.setWrongQuestionCount(wrongQuestions.size());
        }
    }

    private void calculateStreakDays(String userId, StudyStatisticsResponse response) {
        List<LocalDate> studyDates = studyRecordMapper
                .findStudyDatesByUserIdOrderByDateDesc(userId);

        if (studyDates.isEmpty()) {
            response.setCurrentStreak(0);
            response.setLongestStreak(0);
            return;
        }

        // 计算当前连续天数
        int currentStreak = 0;
        LocalDate today = LocalDate.now();
        LocalDate checkDate = today;

        for (LocalDate studyDate : studyDates) {
            if (studyDate.equals(checkDate) || studyDate.equals(checkDate.minusDays(1))) {
                currentStreak++;
                checkDate = studyDate.minusDays(1);
            } else {
                break;
            }
        }

        // 计算最长连续天数
        int longestStreak = 0;
        int tempStreak = 1;

        for (int i = 1; i < studyDates.size(); i++) {
            LocalDate current = studyDates.get(i);
            LocalDate previous = studyDates.get(i - 1);

            if (ChronoUnit.DAYS.between(current, previous) == 1) {
                tempStreak++;
            } else {
                longestStreak = Math.max(longestStreak, tempStreak);
                tempStreak = 1;
            }
        }
        longestStreak = Math.max(longestStreak, tempStreak);

        response.setCurrentStreak(currentStreak);
        response.setLongestStreak(longestStreak);
    }

    private StudyStatisticsResponse.ModuleStatistics convertToModuleStatistics(Map<String, Object> data) {
        StudyStatisticsResponse.ModuleStatistics stats = new StudyStatisticsResponse.ModuleStatistics();
        stats.setModuleType((String) data.get("module_type"));
        stats.setModuleName(getModuleName((String) data.get("module_type")));
        stats.setQuestionCount(((Number) data.get("total_questions")).intValue());
        stats.setCorrectCount(((Number) data.get("total_correct")).intValue());
        stats.setStudyTime(((Number) data.get("total_duration")).intValue());
        stats.setStudyDays(((Number) data.get("record_count")).intValue());

        // 计算正确率
        if (stats.getQuestionCount() > 0) {
            BigDecimal accuracyRate = BigDecimal.valueOf(stats.getCorrectCount() * 100.0 / stats.getQuestionCount())
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            stats.setAccuracyRate(accuracyRate);
        } else {
            stats.setAccuracyRate(BigDecimal.ZERO);
        }

        return stats;
    }

    private LocalDate calculateStartDate(LocalDate endDate, String period) {
        switch (period.toLowerCase()) {
            case "week":
                return endDate.minusWeeks(1);
            case "month":
                return endDate.minusMonths(1);
            case "year":
                return endDate.minusYears(1);
            default:
                return endDate.minusWeeks(1);
        }
    }

    private List<StudyStatisticsResponse.DailyStudyData> aggregateByGranularity(
            List<StudyRecord> records, String granularity) {
        Map<LocalDate, StudyStatisticsResponse.DailyStudyData> dataMap = new HashMap<>();

        for (StudyRecord record : records) {
            LocalDate key = "week".equals(granularity) ?
                    record.getStudyDate().with(java.time.DayOfWeek.MONDAY) : record.getStudyDate();

            StudyStatisticsResponse.DailyStudyData data = dataMap.computeIfAbsent(key, k -> {
                StudyStatisticsResponse.DailyStudyData newData = new StudyStatisticsResponse.DailyStudyData();
                newData.setDate(k);
                newData.setQuestionCount(0);
                newData.setCorrectCount(0);
                newData.setStudyTime(0);
                return newData;
            });

            data.setQuestionCount(data.getQuestionCount() + record.getQuestionCount());
            data.setCorrectCount(data.getCorrectCount() + record.getCorrectCount());
            data.setStudyTime(data.getStudyTime() + record.getStudyDuration());
        }

        // 计算正确率
        dataMap.values().forEach(data -> {
            if (data.getQuestionCount() > 0) {
                BigDecimal accuracyRate = BigDecimal.valueOf(data.getCorrectCount() * 100.0 / data.getQuestionCount())
                        .setScale(2, BigDecimal.ROUND_HALF_UP);
                data.setAccuracyRate(accuracyRate);
            } else {
                data.setAccuracyRate(BigDecimal.ZERO);
            }
        });

        return dataMap.values().stream()
                .sorted(Comparator.comparing(StudyStatisticsResponse.DailyStudyData::getDate))
                .collect(Collectors.toList());
    }

    private String getModuleName(String moduleType) {
        if (moduleType == null) return "";
        Map<String, String> moduleNames = new HashMap<>();
        moduleNames.put("math", "数学运算");
        moduleNames.put("logic", "逻辑推理");
        moduleNames.put("language", "言语理解");
        moduleNames.put("knowledge", "常识判断");
        moduleNames.put("essay", "申论");
        return moduleNames.getOrDefault(moduleType, moduleType);
    }

    private String getQuestionTypeName(String questionType) {
        if (questionType == null) return "";
        Map<String, String> typeNames = new HashMap<>();
        typeNames.put("single_choice", "单选题");
        typeNames.put("multiple_choice", "多选题");
        typeNames.put("judgment", "判断题");
        typeNames.put("fill_blank", "填空题");
        typeNames.put("essay", "论述题");
        return typeNames.getOrDefault(questionType, questionType);
    }

    private String getDifficultyLevelName(String difficultyLevel) {
        if (difficultyLevel == null) return "";
        Map<String, String> levelNames = new HashMap<>();
        levelNames.put("easy", "简单");
        levelNames.put("medium", "中等");
        levelNames.put("hard", "困难");
        return levelNames.getOrDefault(difficultyLevel, difficultyLevel);
    }

    private String getMasteryStatusName(String masteryStatus) {
        if (masteryStatus == null) return "";
        Map<String, String> statusNames = new HashMap<>();
        statusNames.put("not_mastered", "未掌握");
        statusNames.put("reviewing", "复习中");
        statusNames.put("mastered", "已掌握");
        return statusNames.getOrDefault(masteryStatus, masteryStatus);
    }

    /**
     * 将逗号分隔的薄弱知识点字符串转换为JSON数组格式
     * @param weakPointsStr 逗号分隔的字符串，如 "函数图像, 时事政治"
     * @return JSON数组字符串，如 ["函数图像", "时事政治"]，如果输入为空则返回null
     */
    private String convertWeakPointsToJson(String weakPointsStr) {
        if (weakPointsStr == null || weakPointsStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 按逗号分割并清理空白
            String[] points = weakPointsStr.split(",");
            List<String> cleanedPoints = new ArrayList<>();
            for (String point : points) {
                String trimmed = point.trim();
                if (!trimmed.isEmpty()) {
                    cleanedPoints.add(trimmed);
                }
            }

            // 如果没有有效的知识点，返回null
            if (cleanedPoints.isEmpty()) {
                return null;
            }

            // 转换为JSON数组字符串
            return objectMapper.writeValueAsString(cleanedPoints);
        } catch (JsonProcessingException e) {
            log.error("转换薄弱知识点为JSON失败: {}", weakPointsStr, e);
            return null;
        }
    }

    // =====================================================
    // 信誉系统相关方法
    // =====================================================

    /**
     * 检查指定日期是否有学习记录
     *
     * @param userId 用户ID
     * @param date 检查日期
     * @return true-有学习记录，false-无学习记录
     */
    public boolean hasStudyRecordOnDate(String userId, LocalDate date) {
        try {
            LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StudyRecord::getUserId, userId)
                       .eq(StudyRecord::getStudyDate, date);

            long count = studyRecordMapper.selectCount(queryWrapper);
            return count > 0;

        } catch (Exception e) {
            log.error("检查用户 {} 在日期 {} 的学习记录失败", userId, date, e);
            return false;
        }
    }

    /**
     * 获取连续无学习记录的天数
     *
     * @param userId 用户ID
     * @param endDate 结束日期（包含）
     * @return 连续无学习记录的天数
     */
    public int getConsecutiveNoStudyDays(String userId, LocalDate endDate) {
        try {
            int consecutiveDays = 0;
            LocalDate checkDate = endDate;

            // 向前查找连续无学习记录的天数，最多查找30天
            while (consecutiveDays < 30) {
                if (hasStudyRecordOnDate(userId, checkDate)) {
                    break;
                }
                consecutiveDays++;
                checkDate = checkDate.minusDays(1);
            }

            return consecutiveDays;

        } catch (Exception e) {
            log.error("获取用户 {} 连续无学习天数失败", userId, e);
            return 0;
        }
    }

    /**
     * 获取连续学习天数
     *
     * @param userId 用户ID
     * @param endDate 结束日期（包含）
     * @return 连续学习天数
     */
    public int getConsecutiveStudyDays(String userId, LocalDate endDate) {
        try {
            int consecutiveDays = 0;
            LocalDate checkDate = endDate;

            // 向前查找连续学习的天数，最多查找365天
            while (consecutiveDays < 365) {
                if (!hasStudyRecordOnDate(userId, checkDate)) {
                    break;
                }
                consecutiveDays++;
                checkDate = checkDate.minusDays(1);
            }

            return consecutiveDays;

        } catch (Exception e) {
            log.error("获取用户 {} 连续学习天数失败", userId, e);
            return 0;
        }
    }

    /**
     * 获取用户指定日期的学习题量
     *
     * @param userId 用户ID
     * @param date 日期
     * @return 学习题量
     */
    public int getStudyCountOnDate(String userId, LocalDate date) {
        try {
            LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StudyRecord::getUserId, userId)
                       .eq(StudyRecord::getStudyDate, date);

            List<StudyRecord> records = studyRecordMapper.selectList(queryWrapper);
            return records.stream()
                         .mapToInt(record -> record.getQuestionCount() != null ? record.getQuestionCount() : 0)
                         .sum();

        } catch (Exception e) {
            log.error("获取用户 {} 在日期 {} 的学习题量失败", userId, date, e);
            return 0;
        }
    }

    /**
     * 获取用户指定日期的学习正确率
     *
     * @param userId 用户ID
     * @param date 日期
     * @return 正确率（0-100）
     */
    public double getAccuracyOnDate(String userId, LocalDate date) {
        try {
            LambdaQueryWrapper<StudyRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StudyRecord::getUserId, userId)
                       .eq(StudyRecord::getStudyDate, date);

            List<StudyRecord> records = studyRecordMapper.selectList(queryWrapper);

            if (records.isEmpty()) {
                return 0.0;
            }

            int totalQuestions = 0;
            int correctQuestions = 0;

            for (StudyRecord record : records) {
                int questionCount = record.getQuestionCount() != null ? record.getQuestionCount() : 0;
                int correctCount = record.getCorrectCount() != null ? record.getCorrectCount() : 0;

                totalQuestions += questionCount;
                correctQuestions += correctCount;
            }

            if (totalQuestions == 0) {
                return 0.0;
            }

            return (double) correctQuestions / totalQuestions * 100;

        } catch (Exception e) {
            log.error("获取用户 {} 在日期 {} 的学习正确率失败", userId, date, e);
            return 0.0;
        }
    }

    /**
     * 处理学习记录的信誉奖励
     *
     * @param userId 用户ID
     * @param studyRecord 学习记录
     */
    private void handleStudyReward(String userId, StudyRecord studyRecord) {
        try {
            // 基础学习奖励
            int basePoints = calculateBaseStudyPoints(studyRecord);
            if (basePoints > 0) {
                reputationService.addPoints(
                    userId,
                    basePoints,
                    "完成学习记录",
                    "daily_study",
                    studyRecord.getId(),
                    0
                );
            }

            // 高质量学习奖励
            int qualityPoints = calculateQualityBonus(studyRecord);
            if (qualityPoints > 0) {
                reputationService.addPoints(
                    userId,
                    qualityPoints,
                    String.format("高质量学习奖励（正确率%.1f%%）", studyRecord.getAccuracyRate()),
                    "study_quality",
                    studyRecord.getId(),
                    0
                );
            }

            // 连续学习奖励
            LocalDate today = studyRecord.getStudyDate();
            int consecutiveStudyDays = getConsecutiveStudyDays(userId, today);
            if (consecutiveStudyDays >= 3) {
                int consecutivePoints = calculateConsecutiveStudyBonus(consecutiveStudyDays);
                if (consecutivePoints > 0) {
                    reputationService.addPoints(
                        userId,
                        consecutivePoints,
                        String.format("连续学习%d天奖励", consecutiveStudyDays),
                        "daily_study",
                        studyRecord.getId(),
                        consecutiveStudyDays
                    );
                }
            }

            log.debug("用户 {} 学习记录 {} 信誉奖励处理完成", userId, studyRecord.getId());

        } catch (Exception e) {
            log.error("处理用户 {} 学习记录 {} 信誉奖励失败", userId, studyRecord.getId(), e);
            throw e;
        }
    }

    /**
     * 计算基础学习分数
     *
     * @param studyRecord 学习记录
     * @return 基础分数
     */
    private int calculateBaseStudyPoints(StudyRecord studyRecord) {
        int questionCount = studyRecord.getQuestionCount() != null ? studyRecord.getQuestionCount() : 0;

        // 根据题量给分
        if (questionCount >= 100) {
            return 5; // 100题以上：5分
        } else if (questionCount >= 50) {
            return 3; // 50-99题：3分
        } else if (questionCount >= 20) {
            return 2; // 20-49题：2分
        } else if (questionCount >= 10) {
            return 1; // 10-19题：1分
        }

        return 0; // 少于10题不给分
    }

    /**
     * 计算质量奖励分数
     *
     * @param studyRecord 学习记录
     * @return 质量奖励分数
     */
    private int calculateQualityBonus(StudyRecord studyRecord) {
        if (studyRecord.getAccuracyRate() == null) {
            return 0;
        }

        double accuracy = studyRecord.getAccuracyRate().doubleValue();
        int questionCount = studyRecord.getQuestionCount() != null ? studyRecord.getQuestionCount() : 0;

        // 只有题量达到一定数量才给质量奖励
        if (questionCount < 20) {
            return 0;
        }

        // 根据正确率给奖励
        if (accuracy >= 95.0) {
            return 3; // 95%以上：3分
        } else if (accuracy >= 90.0) {
            return 2; // 90-94%：2分
        } else if (accuracy >= 80.0) {
            return 1; // 80-89%：1分
        }

        return 0;
    }

    /**
     * 计算连续学习奖励分数
     *
     * @param consecutiveDays 连续学习天数
     * @return 奖励分数
     */
    private int calculateConsecutiveStudyBonus(int consecutiveDays) {
        if (consecutiveDays >= 30) {
            return 10; // 连续30天：10分
        } else if (consecutiveDays >= 14) {
            return 5;  // 连续14天：5分
        } else if (consecutiveDays >= 7) {
            return 3;  // 连续7天：3分
        } else if (consecutiveDays >= 3) {
            return 1;  // 连续3天：1分
        }

        return 0;
    }
}
