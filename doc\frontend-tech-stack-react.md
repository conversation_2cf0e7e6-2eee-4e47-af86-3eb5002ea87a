# 前端技术栈 - React + Semi Design

## 📋 技术栈概览

基于 React 生态系统，使用 Semi Design 作为 UI 组件库，实现手绘温馨风格的考公刷题系统前端。

### 🚀 核心技术栈

- **React 19** - 现代化 React 框架，支持最新特性
- **TypeScript** - 类型安全，提升开发体验
- **Semi Design** - 字节跳动出品的企业级 UI 组件库
- **React Router DOM** - 客户端路由管理
- **Zustand** - 轻量级状态管理
- **Axios** - HTTP 客户端
- **Vite** - 现代化构建工具

### ✅ 当前实现状态

- ✅ **项目搭建完成** - React + TypeScript + Vite
- ✅ **UI 组件库集成** - Semi Design 正确配置
- ✅ **用户认证系统** - 登录/注册/路由守卫
- ✅ **状态管理** - Zustand 认证状态管理
- ✅ **API 服务层** - Axios 配置和认证服务
- ✅ **手绘风格主题** - 温馨的视觉设计
- ✅ **响应式设计** - 桌面和移动端适配

## 📦 依赖清单

### 生产依赖

```json
{
  "@douyinfe/semi-ui": "^2.83.0",
  "@douyinfe/semi-icons": "^2.83.0",
  "react": "^19.1.0",
  "react-dom": "^19.1.0",
  "react-router-dom": "^7.7.0",
  "zustand": "^5.0.6",
  "axios": "^1.10.0"
}
```

### 开发依赖

```json
{
  "@types/react": "^19.1.8",
  "@types/react-dom": "^19.1.6",
  "@vitejs/plugin-react": "^4.6.0",
  "typescript": "~5.8.3",
  "vite": "^7.0.4",
  "eslint": "^9.30.1"
}
```

## 🏗️ 项目结构

```
kaogong-frontend/
├── public/              # 静态资源
├── src/
│   ├── components/      # 可复用组件
│   │   └── ProtectedRoute.tsx  # 路由守卫
│   ├── pages/          # 页面组件
│   │   ├── Login.tsx   # 登录页面
│   │   └── Register.tsx # 注册页面
│   ├── services/       # API服务层
│   │   ├── api.ts      # Axios基础配置
│   │   └── authService.ts # 认证服务
│   ├── stores/         # Zustand状态管理
│   │   └── useAuthStore.ts # 认证状态
│   ├── types/          # TypeScript类型定义
│   │   └── index.ts    # 统一类型导出
│   ├── styles/         # 样式文件
│   │   ├── theme.css   # 主题样式
│   │   └── auth.css    # 认证页面样式
│   ├── App.tsx         # 根组件
│   ├── main.tsx        # 入口文件
│   └── vite-env.d.ts   # Vite类型声明
├── .env.development    # 开发环境配置
├── .env.production     # 生产环境配置
├── index.html          # HTML模板
├── vite.config.ts      # Vite配置
├── tsconfig.json       # TypeScript配置
└── package.json
```

## ⚙️ 核心功能模块

### 🔐 用户认证系统

- **登录页面** (`/login`) - 用户名密码登录
- **注册页面** (`/register`) - 邀请码注册系统
- **路由守卫** - 自动权限控制和重定向
- **Token 管理** - JWT 自动刷新和过期处理
- **状态持久化** - 登录状态本地保存

### 🌐 API 服务层

- **Axios 配置** - 统一 HTTP 客户端和拦截器
- **认证服务** - 登录、注册、登出 API 调用
- **错误处理** - 统一的错误处理和用户提示
- **类型安全** - 完整的 TypeScript 类型定义

### 🎨 UI 设计系统

- **Semi Design** - 企业级 React 组件库
- **手绘风格** - 纸质背景和手写字体
- **温馨色彩** - 蓝色、绿色、橙色等暖色调
- **响应式布局** - 桌面和移动端自适应
- **动画效果** - 浮动装饰和交互动画

## 🚀 开发工作流

### 启动开发服务器

```bash
npm run dev
# 访问 http://localhost:5173/
```

### 构建生产版本

```bash
npm run build
```

### 代码检查

```bash
npm run lint
```

## 🎯 技术栈优势

1. **现代化** - React 19 + TypeScript + Vite 最新技术栈
2. **企业级** - Semi Design 提供稳定可靠的组件库
3. **类型安全** - 完整的 TypeScript 支持，减少运行时错误
4. **开发体验** - Vite 提供极速的开发和构建体验
5. **状态管理** - Zustand 轻量级且易于使用
6. **设计独特** - 手绘温馨风格，符合教育产品定位
7. **响应式** - 完美适配各种设备和屏幕尺寸

## 📱 当前页面

### ✅ 已完成页面

- **登录页面** - 完整的登录表单和验证
- **注册页面** - 包含邀请码验证的注册流程
- **首页** - 认证后的主界面和统计展示

### 🔄 待开发页面

- **刷题记录页面** - 学习进度和题目记录
- **排行榜页面** - 用户排名和竞争系统
- **个人中心页面** - 用户资料和设置管理

## 🎉 项目状态

- 🟢 **开发服务器**: 正常运行在 http://localhost:5173/
- 🟢 **用户认证**: 登录注册功能完整可用
- 🟢 **路由系统**: 权限控制和页面跳转正常
- 🟢 **状态管理**: 认证状态持久化工作正常
- 🟢 **UI 风格**: 手绘温馨风格实现完整
- 🟢 **响应式**: 桌面和移动端适配良好

前端技术栈已经搭建完成，为后续业务功能开发提供了坚实的技术基础！
