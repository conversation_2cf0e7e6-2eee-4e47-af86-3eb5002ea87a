/* 横排筛选面板样式 - 优化为inline布局 */
.filter-panel-horizontal {
  width: 100%;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: flex-end;
  gap: 24px;
  flex-wrap: wrap;
  justify-content: center;
  padding: 16px 20px;
  background:
    /* 轻微纸质背景 */
    linear-gradient(135deg, #fefdf8 0%, #faf9f4 100%);
  border-radius: 12px;
  border: 1px solid rgba(120, 119, 108, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 140px;
}

.filter-label {
  font-family: var(--font-handwritten);
  font-size: 16px;
  color: var(--ink-dark);
  margin-bottom: 4px;
}

.filter-hint {
  margin-top: 4px;
  font-style: italic;
  line-height: 1.4;
}

.filter-option {
  padding: 8px 0;
}

.option-label {
  font-weight: 600;
  color: var(--ink-dark);
  margin-bottom: 2px;
}

.option-description {
  font-size: 12px;
  color: var(--ink-medium);
  line-height: 1.3;
}

.current-selection {
  padding: 8px 16px;
  background: #f7f6f0;
  border-radius: 8px;
  border: 1px dashed rgba(120, 119, 108, 0.2);
  text-align: center;
  align-self: center;
  margin-left: auto;
  white-space: nowrap;
  font-size: 14px;
}

/* 选择器样式覆盖 */
.filter-panel-horizontal .semi-select {
  border-radius: 6px;
  border: 2px solid var(--border-light);
  transition: all 0.3s ease;
  pointer-events: auto !important;
  cursor: pointer;
}

.filter-panel-horizontal .semi-select:hover {
  border-color: var(--accent-blue);
}

.filter-panel-horizontal .semi-select-focused {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgba(var(--accent-blue-rgb), 0.2);
}

.filter-panel-horizontal .semi-select-selection {
  background: var(--paper-white);
  font-family: var(--font-handwritten);
  font-weight: 500;
  pointer-events: auto !important;
  cursor: pointer;
}

/* 下拉选项样式 */
.semi-select-dropdown .semi-select-option {
  border-radius: 6px;
  margin: 2px 4px;
  transition: all 0.2s ease;
}

.semi-select-dropdown .semi-select-option:hover {
  background: var(--accent-blue-light);
  transform: translateX(4px);
}

.semi-select-dropdown .semi-select-option-selected {
  background: var(--accent-blue);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .filter-group {
    width: 100%;
    max-width: 250px;
  }

  .filter-group .semi-select {
    width: 100% !important;
  }

  .filter-label {
    font-size: 14px;
    text-align: center;
  }

  .current-selection {
    padding: 8px;
    margin-top: 12px;
    width: 100%;
    max-width: 300px;
  }
}
