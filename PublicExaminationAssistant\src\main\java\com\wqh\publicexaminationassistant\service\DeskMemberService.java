package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.response.DeskMemberResponse;
import com.wqh.publicexaminationassistant.dto.response.DeskRankingResponse;
import com.wqh.publicexaminationassistant.entity.Desk;
import com.wqh.publicexaminationassistant.entity.DeskMember;
import com.wqh.publicexaminationassistant.mapper.DeskMapper;
import com.wqh.publicexaminationassistant.mapper.DeskMemberMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 小桌成员管理服务
 * 提供成员查询、管理、排行榜等功能
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class DeskMemberService {

    private final DeskMapper deskMapper;
    private final DeskMemberMapper deskMemberMapper;

    /**
     * 获取小桌成员列表
     */
    public List<DeskMemberResponse> getDeskMembers(String deskId, String userId) {
        log.info("获取小桌成员列表: deskId={}, userId={}", deskId, userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证用户是否为成员（只有成员可以查看成员列表）
        if (deskMemberMapper.countByDeskIdAndUserId(deskId, userId) == 0) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有小桌成员可以查看成员列表");
        }

        // 3. 查询成员列表（包含最近活跃度）
        LocalDate startDate = LocalDate.now().minusDays(7); // 最近7天的活跃度
        List<Map<String, Object>> memberMaps = deskMemberMapper.findMembersWithActivity(deskId, startDate);

        // 4. 转换为响应DTO
        return memberMaps.stream()
                .map(this::buildDeskMemberResponse)
                .collect(Collectors.toList());
    }

    /**
     * 移除成员
     */
    public void removeMember(String deskId, String memberId, String operatorId) {
        log.info("移除小桌成员: deskId={}, memberId={}, operatorId={}", deskId, memberId, operatorId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证操作者权限（只有桌长可以移除成员）
        if (!desk.isOwner(operatorId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以移除成员");
        }

        // 3. 验证被移除的成员
        DeskMember member = deskMemberMapper.findByDeskIdAndUserId(deskId, memberId);
        if (member == null) {
            throw new BusinessException(ResultCode.NOT_DESK_MEMBER, "成员不存在");
        }

        // 4. 不能移除桌长自己
        if (member.isOwner()) {
            throw new BusinessException(ResultCode.NOT_DESK_OWNER, "不能移除桌长");
        }

        // 5. 移除成员（设置为非活跃状态）
        member.deactivate();
        int result = deskMemberMapper.updateById(member);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "移除成员失败");
        }

        // 6. 更新小桌成员数量
        int currentMembers = deskMemberMapper.countActiveMembersByDeskId(deskId);
        deskMapper.updateMemberCount(deskId, currentMembers);

        log.info("成员移除成功: deskId={}, memberId={}", deskId, memberId);
    }

    /**
     * 离开小桌
     */
    public void leaveDesk(String deskId, String userId) {
        log.info("离开小桌: deskId={}, userId={}", deskId, userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证用户是否为成员
        DeskMember member = deskMemberMapper.findByDeskIdAndUserId(deskId, userId);
        if (member == null) {
            throw new BusinessException(ResultCode.NOT_DESK_MEMBER, "您不是该小桌的成员");
        }

        // 3. 桌长不能直接离开，需要先转让桌长或解散小桌
        if (member.isOwner()) {
            throw new BusinessException(ResultCode.OWNER_CANNOT_LEAVE, "桌长不能直接离开小桌，请先转让桌长权限或解散小桌");
        }

        // 4. 离开小桌（设置为非活跃状态）
        member.deactivate();
        int result = deskMemberMapper.updateById(member);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "离开小桌失败");
        }

        // 5. 更新小桌成员数量
        int currentMembers = deskMemberMapper.countActiveMembersByDeskId(deskId);
        deskMapper.updateMemberCount(deskId, currentMembers);

        log.info("离开小桌成功: deskId={}, userId={}", deskId, userId);
    }

    /**
     * 更新成员角色
     */
    public void updateMemberRole(String deskId, String memberId, String role, String operatorId) {
        log.info("更新成员角色: deskId={}, memberId={}, role={}, operatorId={}", deskId, memberId, role, operatorId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证操作者权限（只有桌长可以更新角色）
        if (!desk.isOwner(operatorId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有桌长可以更新成员角色");
        }

        // 3. 验证角色参数
        if (!"owner".equals(role) && !"member".equals(role)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "无效的角色类型");
        }

        // 4. 验证目标成员
        DeskMember member = deskMemberMapper.findByDeskIdAndUserId(deskId, memberId);
        if (member == null) {
            throw new BusinessException(ResultCode.NOT_DESK_MEMBER, "成员不存在");
        }

        // 5. 如果是转让桌长权限
        if ("owner".equals(role) && !member.isOwner()) {
            // 将当前桌长降级为普通成员
            DeskMember currentOwner = deskMemberMapper.findByDeskIdAndUserId(deskId, operatorId);
            if (currentOwner != null) {
                currentOwner.setAsMember();
                deskMemberMapper.updateById(currentOwner);
            }

            // 更新小桌的桌长ID
            desk.setOwnerId(memberId);
            deskMapper.updateById(desk);
        }

        // 6. 更新成员角色
        member.setRole(role);
        int result = deskMemberMapper.updateById(member);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新成员角色失败");
        }

        log.info("成员角色更新成功: deskId={}, memberId={}, newRole={}", deskId, memberId, role);
    }

    /**
     * 获取小桌排行榜
     */
    public List<DeskRankingResponse> getDeskRanking(String deskId, String period, String userId) {
        log.info("获取小桌排行榜: deskId={}, period={}, userId={}", deskId, period, userId);

        // 1. 验证小桌是否存在
        Desk desk = deskMapper.selectById(deskId);
        if (desk == null) {
            throw new BusinessException(ResultCode.DESK_NOT_FOUND, "小桌不存在");
        }

        // 2. 验证用户是否为成员（只有成员可以查看排行榜）
        if (deskMemberMapper.countByDeskIdAndUserId(deskId, userId) == 0) {
            throw new BusinessException(ResultCode.FORBIDDEN, "只有小桌成员可以查看排行榜");
        }

        // 3. 根据period确定统计时间范围
        LocalDate startDate = calculateStartDate(period);

        // 4. 查询排行榜数据
        List<Map<String, Object>> rankingMaps = deskMemberMapper.getDeskRanking(deskId, startDate);

        // 5. 计算排名和综合得分
        AtomicInteger rank = new AtomicInteger(1);
        return rankingMaps.stream()
                .map(map -> buildDeskRankingResponse(map, rank.getAndIncrement()))
                .collect(Collectors.toList());
    }

    /**
     * 更新成员活跃时间
     */
    public void updateMemberActiveTime(String deskId, String userId) {
        log.debug("更新成员活跃时间: deskId={}, userId={}", deskId, userId);

        int result = deskMemberMapper.updateLastActiveTime(deskId, userId);
        if (result > 0) {
            log.debug("成员活跃时间更新成功: deskId={}, userId={}", deskId, userId);
        }
    }

    /**
     * 根据period计算开始日期
     */
    private LocalDate calculateStartDate(String period) {
        LocalDate now = LocalDate.now();
        switch (period) {
            case "day":
                return now;
            case "week":
                return now.minusDays(7);
            case "month":
                return now.minusDays(30);
            case "all":
                return LocalDate.of(2020, 1, 1); // 足够早的日期
            default:
                return now.minusDays(7); // 默认一周
        }
    }

    /**
     * 构建小桌成员响应DTO
     */
    private DeskMemberResponse buildDeskMemberResponse(Map<String, Object> memberMap) {
        DeskMemberResponse response = new DeskMemberResponse();
        
        response.setId((String) memberMap.get("id"));
        response.setDeskId((String) memberMap.get("desk_id"));
        response.setUserId((String) memberMap.get("user_id"));
        response.setUsername((String) memberMap.get("username"));
        response.setNickname((String) memberMap.get("nickname"));
        response.setRole((String) memberMap.get("role"));
        response.setStatus((String) memberMap.get("status"));
        response.setReputationScore((Integer) memberMap.get("reputation_score"));
        response.setReputationLevel((String) memberMap.get("reputation_level"));
        response.setJoinReason((String) memberMap.get("join_reason"));
        // 处理时间字段的类型转换
        Object joinedAtObj = memberMap.get("joined_at");
        if (joinedAtObj instanceof java.sql.Timestamp) {
            response.setJoinedAt(((java.sql.Timestamp) joinedAtObj).toLocalDateTime());
        } else if (joinedAtObj instanceof java.time.LocalDateTime) {
            response.setJoinedAt((java.time.LocalDateTime) joinedAtObj);
        }

        Object lastActiveAtObj = memberMap.get("last_active_at");
        if (lastActiveAtObj instanceof java.sql.Timestamp) {
            response.setLastActiveAt(((java.sql.Timestamp) lastActiveAtObj).toLocalDateTime());
        } else if (lastActiveAtObj instanceof java.time.LocalDateTime) {
            response.setLastActiveAt((java.time.LocalDateTime) lastActiveAtObj);
        }
        
        // 活跃度数据
        Object studyDaysObj = memberMap.get("recent_study_days");
        Object questionsObj = memberMap.get("recent_questions");
        response.setRecentStudyDays(studyDaysObj != null ? ((Number) studyDaysObj).intValue() : 0);
        response.setRecentQuestions(questionsObj != null ? ((Number) questionsObj).intValue() : 0);
        
        // 状态标识
        response.setIsOwner("owner".equals(response.getRole()));
        response.setIsActive("active".equals(response.getStatus()));
        
        return response;
    }

    /**
     * 构建小桌排行榜响应DTO
     */
    private DeskRankingResponse buildDeskRankingResponse(Map<String, Object> rankingMap, int rank) {
        DeskRankingResponse response = new DeskRankingResponse();
        
        response.setRank(rank);
        response.setUserId((String) rankingMap.get("user_id"));
        response.setUsername((String) rankingMap.get("username"));
        response.setNickname((String) rankingMap.get("nickname"));
        response.setReputationScore((Integer) rankingMap.get("reputation_score"));
        
        // 学习数据
        Object studyDaysObj = rankingMap.get("study_days");
        Object totalQuestionsObj = rankingMap.get("total_questions");
        Object totalCorrectObj = rankingMap.get("total_correct");
        Object accuracyRateObj = rankingMap.get("accuracy_rate");
        
        response.setStudyDays(studyDaysObj != null ? ((Number) studyDaysObj).intValue() : 0);
        response.setTotalQuestions(totalQuestionsObj != null ? ((Number) totalQuestionsObj).intValue() : 0);
        response.setTotalCorrect(totalCorrectObj != null ? ((Number) totalCorrectObj).intValue() : 0);
        response.setAccuracyRate(accuracyRateObj != null ? ((Number) accuracyRateObj).doubleValue() : 0.0);
        
        // 计算综合得分（题目数量 * 0.6 + 学习天数 * 10 + 正确率 * 0.4）
        double totalScore = response.getTotalQuestions() * 0.6 + 
                           response.getStudyDays() * 10 + 
                           response.getAccuracyRate() * 0.4;
        response.setTotalScore(Math.round(totalScore * 100.0) / 100.0);
        
        response.setIsOwner(false); // 这里需要额外查询，暂时设为false
        
        return response;
    }
}
