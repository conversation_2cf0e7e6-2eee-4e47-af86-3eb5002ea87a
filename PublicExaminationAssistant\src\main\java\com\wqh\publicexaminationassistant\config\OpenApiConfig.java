package com.wqh.publicexaminationassistant.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * OpenAPI (Swagger) 配置
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Configuration
public class OpenApiConfig {

    @Value("${app.name}")
    private String appName;

    @Value("${app.version}")
    private String appVersion;

    @Value("${app.description}")
    private String appDescription;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * 配置OpenAPI
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication", securityScheme()))
                .servers(Arrays.asList(
                        new Server()
                                .url("http://localhost:" + serverPort + "/api")
                                .description("本地开发服务器"),
                        new Server()
                                .url("/api")
                                .description("相对路径服务器")
                ));
    }

    /**
     * 配置API分组 - 用户管理
     */
    @Bean
    public GroupedOpenApi userApi() {
        return GroupedOpenApi.builder()
                .group("用户管理")
                .pathsToMatch("/v1/auth/**", "/v1/users/**", "/v1/invite-codes/**")
                .packagesToScan("com.wqh.publicexaminationassistant.controller")
                .build();
    }

    /**
     * 配置API分组 - 刷题记录
     */
    @Bean
    public GroupedOpenApi studyApi() {
        return GroupedOpenApi.builder()
                .group("刷题记录")
                .pathsToMatch("/v1/study-records/**", "/v1/wrong-questions/**", "/v1/study-plans/**")
                .packagesToScan("com.wqh.publicexaminationassistant.controller")
                .build();
    }

    /**
     * 配置API分组 - 小桌系统
     */
    @Bean
    public GroupedOpenApi deskApi() {
        return GroupedOpenApi.builder()
                .group("小桌系统")
                .pathsToMatch("/v1/desks/**")
                .packagesToScan("com.wqh.publicexaminationassistant.controller")
                .build();
    }

    /**
     * 配置API分组 - 系统测试
     */
    @Bean
    public GroupedOpenApi testApi() {
        return GroupedOpenApi.builder()
                .group("系统测试")
                .pathsToMatch("/v1/test/**")
                .packagesToScan("com.wqh.publicexaminationassistant.controller")
                .build();
    }

    /**
     * 配置API信息
     */
    private Info apiInfo() {
        return new Info()
                .title(appName + " API文档")
                .description(appDescription)
                .version(appVersion)
                .contact(new Contact()
                        .name("wqh")
                        .url("https://github.com/wqh")
                        .email("<EMAIL>"));
    }

    /**
     * 配置安全方案
     */
    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization");
    }
}
