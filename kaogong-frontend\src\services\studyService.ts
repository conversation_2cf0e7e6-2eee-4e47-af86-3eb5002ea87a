import { request } from './api';

// 刷题记录相关接口类型定义

/**
 * 创建刷题记录请求参数
 */
export interface CreateStudyRecordRequest {
  moduleType: string;
  questionCount: number;
  correctCount: number;
  studyDuration: number;
  weakPoints?: string[] | null;
  studyDate: string;
  notes?: string | null;
  wrongQuestions?: WrongQuestionRequest[];
}

/**
 * 更新刷题记录请求参数
 */
export interface UpdateStudyRecordRequest {
  notes?: string | null;
  weakPoints?: string | null;
}

/**
 * 错题请求参数
 */
export interface WrongQuestionRequest {
  studyRecordId?: string;
  moduleType?: string;
  questionType: string;
  difficultyLevel: string;
  questionContent: string;
  userAnswer?: string;
  correctAnswer: string;
  explanation?: string;
  masteryStatus?: string;
}

/**
 * 图片信息接口
 */
export interface ImageInfo {
  id: string;
  imageType: 'question' | 'answer' | 'explanation';
  fileName: string;
  fileUrl: string;
  fileSize: number;
  width?: number;
  height?: number;
  sortOrder: number;
  isCompressed?: boolean;
  thumbnailUrl?: string;
  createdAt: string;
  description?: string;
}

/**
 * 错题响应
 */
export interface WrongQuestionResponse {
  id: string;
  userId: string;
  studyRecordId: string;
  moduleType?: string;
  moduleName?: string;
  questionType: string;
  questionTypeName: string;
  difficultyLevel: string;
  difficultyLevelName: string;
  questionContent: string;
  userAnswer?: string;
  correctAnswer: string;
  explanation?: string;
  masteryStatus: string;
  masteryStatusName: string;
  reviewCount: number;
  createdAt: string;
  reviewedAt?: string;
  studyRecord?: {
    id: string;
    moduleType: string;
    moduleName: string;
    studyDate: string;
  };
  images?: ImageInfo[];
}

/**
 * 更新错题掌握状态请求参数
 */
export interface UpdateMasteryStatusRequest {
  masteryStatus: string;
}

/**
 * 刷题记录响应
 */
export interface StudyRecordResponse {
  id: string;
  userId: string;
  moduleType: string;
  moduleName?: string;
  questionCount: number;
  correctCount: number;
  wrongCount?: number;
  accuracyRate: number;
  studyDuration: number;
  weakPoints?: string[] | string | null; // 可能是数组、JSON字符串或null
  studyDate: string;
  notes?: string;
  createdAt: string;
  wrongQuestionCount?: number;
  user?: {
    id: string;
    username: string;
    nickname: string;
    avatarUrl?: string;
  };
}

/**
 * 学习统计响应 - 根据API文档调整
 */
export interface StudyStatisticsResponse {
  // 概览统计字段
  totalQuestions?: number;
  totalCorrect?: number;
  averageAccuracy?: number;
  totalStudyTime?: number;
  studyDays?: number;
  currentStreak?: number;

  // 趋势数据
  dailyData?: DailyStudyData[];

  // 模块统计
  moduleStats?: ModuleStatistics[];

  // 薄弱知识点
  weakPointStats?: WeakPointStatistics[];
}

export interface DailyStudyData {
  date: string;
  questionCount: number;
  correctCount: number;
  accuracyRate: number;
  studyTime: number;
}

export interface ModuleStatistics {
  moduleType: string;
  moduleName: string;
  questionCount: number;
  correctCount: number;
  accuracyRate: number;
  studyTime: number;
  studyDays: number;
}

export interface WeakPointStatistics {
  point: string;
  frequency: number;
  lastAppeared: string;
  relatedModules: string[];
}

/**
 * 学习概览统计响应
 */
export interface StudyOverviewResponse {
  totalQuestions: number;
  totalCorrect: number;
  averageAccuracy: number;
  totalStudyTime: number;
  studyDays: number;
  currentStreak: number;
}

/**
 * 学习趋势数据点
 */
export interface StudyTrendData {
  date: string;
  questionCount: number;
  correctCount: number;
  accuracyRate: number;
  studyTime: number;
}

/**
 * 学习趋势响应
 */
export interface StudyTrendsResponse {
  totalStudyDays: number | null;
  totalQuestions: number | null;
  totalCorrect: number | null;
  totalWrong: number | null;
  averageAccuracy: number | null;
  totalStudyTime: number | null;
  currentStreak: number | null;
  longestStreak: number | null;
  lastStudyDate: string | null;
  dailyData: StudyTrendData[];
  moduleStats: any | null;
  weakPointStats: any | null;
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

/**
 * 刷题记录服务
 */
export const studyService = {
  /**
   * 创建刷题记录
   */
  createRecord: (data: CreateStudyRecordRequest): Promise<StudyRecordResponse> =>
    request.post('/v1/study-records', data),

  /**
   * 获取刷题记录列表
   */
  getRecords: (params: {
    page?: number;
    size?: number;
    moduleType?: string;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<PageResult<StudyRecordResponse>> =>
    request.get('/v1/study-records', { params }),

  /**
   * 获取刷题记录详情
   */
  getRecordDetail: (recordId: string): Promise<StudyRecordResponse> =>
    request.get(`/v1/study-records/${recordId}`),

  /**
   * 更新刷题记录
   */
  updateRecord: (recordId: string, data: UpdateStudyRecordRequest): Promise<void> =>
    request.put(`/v1/study-records/${recordId}`, data),

  /**
   * 删除刷题记录
   */
  deleteRecord: (recordId: string): Promise<void> =>
    request.delete(`/v1/study-records/${recordId}`),

  /**
   * 获取学习统计概览
   */
  getStudyOverview: (period: string = 'week'): Promise<StudyOverviewResponse> =>
    request.get('/v1/study-records/stats/overview', { params: { period } }),

  /**
   * 获取模块统计
   */
  getModuleStatistics: (params: {
    startDate?: string;
    endDate?: string;
  }): Promise<StudyStatisticsResponse> =>
    request.get('/v1/study-records/stats/modules', { params }),

  /**
   * 获取学习趋势
   */
  getStudyTrends: (period: string = 'week', granularity: string = 'day'): Promise<StudyTrendsResponse> =>
    request.get('/v1/study-records/stats/trends', { params: { period, granularity } }),

  /**
   * 获取学习日历
   */
  getStudyCalendar: (year: number, month: number): Promise<StudyStatisticsResponse> =>
    request.get('/v1/study-records/calendar', { params: { year, month } }),

  /**
   * 获取错题列表
   */
  getWrongQuestions: (params: {
    page?: number;
    size?: number;
    questionType?: string;
    moduleType?: string;
    masteryStatus?: string;
  }): Promise<PageResult<WrongQuestionResponse>> =>
    request.get('/v1/study-records/wrong-questions', { params }),

  /**
   * 获取错题详情（包含图片）
   */
  getWrongQuestionDetail: (wrongQuestionId: string): Promise<WrongQuestionResponse> =>
    request.get(`/v1/wrong-questions/${wrongQuestionId}`),

  /**
   * 增加错题复习次数
   */
  incrementReviewCount: (wrongQuestionId: string): Promise<void> =>
    request.put(`/v1/wrong-questions/${wrongQuestionId}/review`),

  /**
   * 更新错题掌握状态
   */
  updateMasteryStatus: (wrongQuestionId: string, data: UpdateMasteryStatusRequest): Promise<void> =>
    request.put(`/v1/study-records/wrong-questions/${wrongQuestionId}/mastery`, data),

  /**
   * 创建错题
   */
  createWrongQuestion: (data: WrongQuestionRequest): Promise<WrongQuestionResponse> =>
    request.post('/v1/wrong-questions', data),

  /**
   * 删除错题
   */
  deleteWrongQuestion: (wrongQuestionId: string): Promise<void> =>
    request.delete(`/v1/wrong-questions/${wrongQuestionId}`),
};

/**
 * 图片服务API
 */
export const imageService = {
  /**
   * 上传错题图片
   */
  uploadWrongQuestionImage: (
    wrongQuestionId: string,
    file: File,
    imageType: string,
    sortOrder: number = 0
  ): Promise<ImageInfo> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('imageType', imageType);
    formData.append('sortOrder', sortOrder.toString());

    return request.post(`/v1/wrong-questions/${wrongQuestionId}/images`, formData);
  },

  /**
   * 获取错题图片列表
   */
  getWrongQuestionImages: (wrongQuestionId: string): Promise<ImageInfo[]> =>
    request.get(`/v1/wrong-questions/${wrongQuestionId}/images`),

  /**
   * 删除图片
   */
  deleteImage: (imageId: string): Promise<void> =>
    request.delete(`/v1/wrong-questions/images/${imageId}`),

  /**
   * 批量上传图片
   */
  batchUploadImages: async (
    wrongQuestionId: string,
    files: { file: File; imageType: string; sortOrder: number }[]
  ): Promise<ImageInfo[]> => {
    const uploadPromises = files.map(({ file, imageType, sortOrder }) =>
      imageService.uploadWrongQuestionImage(wrongQuestionId, file, imageType, sortOrder)
    );

    return Promise.all(uploadPromises);
  }
};
