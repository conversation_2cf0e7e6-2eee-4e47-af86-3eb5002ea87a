# 错题管理 API 接口文档

## 概述

错题管理 API 提供了完整的错题 CRUD 操作，支持按学习模块和掌握状态筛选错题。

## 接口列表

### 1. 创建错题

**接口地址**: `POST /v1/wrong-questions`

**请求参数**:

```json
{
  "studyRecordId": "uuid", // 可选，关联的学习记录ID
  "questionType": "single_choice", // 必填，题目类型
  "difficultyLevel": "medium", // 必填，难度等级
  "questionContent": "题目内容", // 必填，题目内容
  "userAnswer": "A", // 可选，用户答案
  "correctAnswer": "B", // 必填，正确答案
  "explanation": "解析内容", // 可选，解析
  "masteryStatus": "not_mastered" // 可选，默认为未掌握
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "错题创建成功",
  "data": {
    "id": "uuid",
    "userId": "uuid",
    "studyRecordId": "uuid",
    "questionType": "single_choice",
    "questionTypeName": "单选题",
    "difficultyLevel": "medium",
    "difficultyLevelName": "中等",
    "questionContent": "题目内容",
    "userAnswer": "A",
    "correctAnswer": "B",
    "explanation": "解析内容",
    "masteryStatus": "not_mastered",
    "masteryStatusName": "未掌握",
    "reviewCount": 0,
    "createdAt": "2024-07-19 13:30:00",
    "studyRecord": {
      "id": "uuid",
      "moduleType": "math",
      "moduleName": "数学运算",
      "studyDate": "2024-07-19"
    }
  }
}
```

### 2. 获取错题列表（支持按学习模块筛选）

**接口地址**: `GET /v1/study-records/wrong-questions`

**请求参数**:

- `page`: 页码，默认 1
- `size`: 每页大小，默认 20
- `questionType`: 题目类型筛选（可选）
- `moduleType`: 学习模块筛选（可选）- **新增功能**
- `masteryStatus`: 掌握状态筛选（可选）

**示例请求**:

```
GET /v1/study-records/wrong-questions?page=1&size=20&moduleType=math&masteryStatus=not_mastered
```

**响应示例**:

```json
{
  "code": 200,
  "message": "获取错题列表成功",
  "data": {
    "records": [
      {
        "id": "uuid",
        "questionContent": "题目内容",
        "masteryStatus": "not_mastered",
        "masteryStatusName": "未掌握",
        "studyRecord": {
          "moduleType": "math",
          "moduleName": "数学运算"
        }
      }
    ],
    "total": 50,
    "current": 1,
    "size": 20
  }
}
```

### 3. 获取错题详情

**接口地址**: `GET /v1/wrong-questions/{wrongQuestionId}`

**响应示例**:

```json
{
  "code": 200,
  "message": "获取错题详情成功",
  "data": {
    "id": "uuid",
    "questionContent": "题目内容",
    "userAnswer": "A",
    "correctAnswer": "B",
    "explanation": "解析内容",
    "masteryStatus": "not_mastered",
    "reviewCount": 3,
    "studyRecord": {
      "moduleType": "math",
      "moduleName": "数学运算"
    }
  }
}
```

### 4. 更新错题

**接口地址**: `PUT /v1/wrong-questions/{wrongQuestionId}`

**请求参数**: 与创建错题相同

### 5. 删除错题

**接口地址**: `DELETE /v1/wrong-questions/{wrongQuestionId}`

**响应示例**:

```json
{
  "code": 200,
  "message": "错题删除成功",
  "data": null
}
```

### 6. 增加错题复习次数

**接口地址**: `PUT /v1/wrong-questions/{wrongQuestionId}/review`

**功能说明**: 当用户查看错题详情时，自动调用此接口增加复习次数

**响应示例**:

```json
{
  "code": 200,
  "message": "复习次数更新成功",
  "data": null
}
```

**使用场景**:

- 用户点击"查看详情"按钮时自动调用
- 每次调用会将该错题的 `review_count` 字段 +1
- 同时更新 `reviewed_at` 字段为当前时间

---

## 小桌系统 API 接口

### 1. 创建小桌

**接口地址**: `POST /v1/desks`

**请求参数**:

```json
{
  "name": "数学学习小桌",
  "description": "专注于数学运算和逻辑推理的学习小桌",
  "maxMembers": 6,
  "autoApproveRules": "{\"autoApprove\": true, \"minReputationScore\": 80}"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "小桌创建成功",
  "data": {
    "id": "desk-uuid-123",
    "name": "数学学习小桌",
    "description": "专注于数学运算和逻辑推理的学习小桌",
    "ownerId": "user-uuid-456",
    "ownerUsername": "testuser",
    "ownerNickname": "测试用户",
    "maxMembers": 6,
    "currentMembers": 1,
    "availableSlots": 5,
    "status": "active",
    "isOwner": true,
    "isMember": true,
    "hasApplied": false,
    "canJoin": false,
    "pendingApplications": 0,
    "createdAt": "2024-07-20T10:30:00",
    "updatedAt": "2024-07-20T10:30:00"
  }
}
```

### 2. 获取小桌详情

**接口地址**: `GET /v1/desks/{deskId}`

**响应示例**:

```json
{
  "code": 200,
  "message": "获取小桌详情成功",
  "data": {
    "id": "desk-uuid-123",
    "name": "数学学习小桌",
    "description": "专注于数学运算和逻辑推理的学习小桌",
    "ownerId": "user-uuid-456",
    "ownerUsername": "testuser",
    "ownerNickname": "测试用户",
    "maxMembers": 6,
    "currentMembers": 3,
    "availableSlots": 3,
    "status": "active",
    "isOwner": false,
    "isMember": true,
    "hasApplied": false,
    "canJoin": false,
    "createdAt": "2024-07-20T10:30:00",
    "updatedAt": "2024-07-20T10:30:00"
  }
}
```

### 3. 更新小桌信息

**接口地址**: `PUT /v1/desks/{deskId}`

**权限要求**: 仅桌长可操作

**请求参数**:

```json
{
  "name": "高级数学学习小桌",
  "description": "更新后的描述",
  "maxMembers": 8,
  "autoApproveRules": "{\"autoApprove\": false}"
}
```

### 4. 解散小桌

**接口地址**: `DELETE /v1/desks/{deskId}`

**权限要求**: 仅桌长可操作

**响应示例**:

```json
{
  "code": 200,
  "message": "小桌解散成功",
  "data": null
}
```

### 5. 搜索小桌

**接口地址**: `GET /v1/desks`

**查询参数**:

- `keyword`: 搜索关键词（可选）
- `page`: 页码，默认 1
- `size`: 每页大小，默认 20

**响应示例**:

```json
{
  "code": 200,
  "message": "搜索小桌成功",
  "data": {
    "records": [
      {
        "id": "desk-uuid-123",
        "name": "数学学习小桌",
        "currentMembers": 3,
        "maxMembers": 6,
        "isOwner": false,
        "isMember": false,
        "canJoin": true
      }
    ],
    "total": 10,
    "pages": 1,
    "current": 1,
    "size": 20
  }
}
```

### 6. 获取我的小桌

**接口地址**: `GET /v1/desks/my`

**响应示例**:

```json
{
  "code": 200,
  "message": "获取我的小桌成功",
  "data": [
    {
      "id": "desk-uuid-123",
      "name": "我创建的小桌",
      "isOwner": true,
      "isMember": true,
      "currentMembers": 4,
      "maxMembers": 6
    },
    {
      "id": "desk-uuid-456",
      "name": "我加入的小桌",
      "isOwner": false,
      "isMember": true,
      "currentMembers": 2,
      "maxMembers": 8
    }
  ]
}
```

### 7. 申请加入小桌

**接口地址**: `POST /v1/desk-applications`

**请求参数**:

```json
{
  "deskId": "desk-uuid-123",
  "reason": "我对数学学习很感兴趣，希望能加入这个小桌一起学习进步",
  "studyPlan": "每天刷题50道，重点攻克数学运算和逻辑推理"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "申请提交成功",
  "data": {
    "id": "application-uuid-789",
    "deskId": "desk-uuid-123",
    "userId": "user-uuid-456",
    "reason": "我对数学学习很感兴趣，希望能加入这个小桌一起学习进步",
    "studyPlan": "每天刷题50道，重点攻克数学运算和逻辑推理",
    "status": "pending",
    "appliedAt": "2024-07-20T11:00:00",
    "isPending": true,
    "isApproved": false,
    "isRejected": false
  }
}
```

### 8. 处理申请

**接口地址**: `PUT /v1/desk-applications/{applicationId}/process`

**权限要求**: 仅桌长可操作

**请求参数**:

```json
{
  "action": "approved",
  "remark": "欢迎加入我们的学习小桌"
}
```

### 9. 获取小桌成员列表

**接口地址**: `GET /v1/desks/{deskId}/members`

**权限要求**: 仅小桌成员可查看

**响应示例**:

```json
{
  "code": 200,
  "message": "获取成员列表成功",
  "data": [
    {
      "id": "member-uuid-123",
      "userId": "user-uuid-456",
      "username": "testuser",
      "nickname": "测试用户",
      "role": "owner",
      "reputationScore": 150,
      "reputationLevel": "silver",
      "joinedAt": "2024-07-20T10:30:00",
      "recentStudyDays": 5,
      "recentQuestions": 250,
      "isOwner": true,
      "isActive": true
    }
  ]
}
```

### 10. 获取小桌排行榜

**接口地址**: `GET /v1/desks/{deskId}/ranking`

**查询参数**:

- `period`: 统计周期（day/week/month/all），默认 week

**权限要求**: 仅小桌成员可查看

**响应示例**:

```json
{
  "code": 200,
  "message": "获取排行榜成功",
  "data": [
    {
      "rank": 1,
      "userId": "user-uuid-456",
      "username": "topuser",
      "nickname": "学霸用户",
      "reputationScore": 200,
      "studyDays": 7,
      "totalQuestions": 350,
      "totalCorrect": 315,
      "accuracyRate": 90.0,
      "totalScore": 385.0,
      "isOwner": false
    }
  ]
}
```

## 学习模块类型

支持的学习模块类型：

- `math`: 数学运算
- `logic`: 逻辑推理
- `language`: 言语理解
- `knowledge`: 常识判断
- `essay`: 申论写作

## 题目类型

支持的题目类型：

- `single_choice`: 单选题
- `multiple_choice`: 多选题
- `judgment`: 判断题
- `fill_blank`: 填空题
- `essay`: 论述题

## 难度等级

支持的难度等级：

- `easy`: 简单
- `medium`: 中等
- `hard`: 困难

## 掌握状态

支持的掌握状态：

- `not_mastered`: 未掌握
- `reviewing`: 复习中
- `mastered`: 已掌握

## 权限要求

所有接口都需要用户登录认证，只能操作自己的错题数据。

## 错误码

- `2002`: 错题不存在
- `2001`: 关联的学习记录不存在
- `1003`: 无权限访问
- `1001`: 参数验证失败
