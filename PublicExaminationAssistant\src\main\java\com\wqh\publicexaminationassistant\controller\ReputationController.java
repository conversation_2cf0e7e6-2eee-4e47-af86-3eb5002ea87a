package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.entity.ReputationLog;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import com.wqh.publicexaminationassistant.mapper.ReputationLogMapper;
import com.wqh.publicexaminationassistant.service.ReputationService;
import com.wqh.publicexaminationassistant.service.UserProtectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信誉系统API控制器
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/api/v1/reputation")
@Tag(name = "信誉系统", description = "用户信誉分数管理相关接口")
@Slf4j
public class ReputationController {

    @Autowired
    private ReputationService reputationService;
    
    @Autowired
    private UserProtectionService userProtectionService;
    
    @Autowired
    private ReputationLogMapper reputationLogMapper;

    /**
     * 获取当前用户信誉统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取用户信誉统计", description = "获取当前用户的信誉分数、等级等统计信息")
    public ApiResponse<Map<String, Object>> getUserReputationStats(
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        try {
            // 检查用户认证信息
            if (userDetails == null) {
                log.error("用户认证信息为空");
                return ApiResponse.error("用户认证信息无效");
            }

            String userId = userDetails.getId();
            if (userId == null || userId.trim().isEmpty()) {
                log.error("用户ID为空: userDetails={}", userDetails);
                return ApiResponse.error("用户ID无效");
            }

            log.info("获取用户信誉统计信息: userId={}, username={}", userId, userDetails.getUsername());

            UserReputationStats stats = reputationService.getUserReputationStats(userId);

            if (stats == null) {
                // 如果用户信誉统计记录不存在，自动初始化
                log.warn("用户 {} 信誉统计记录不存在，开始自动初始化", userId);
                try {
                    userProtectionService.initUserProtection(userId);
                    stats = reputationService.getUserReputationStats(userId);

                    if (stats == null) {
                        log.error("用户 {} 信誉统计记录初始化失败", userId);
                        return ApiResponse.error("信誉统计记录初始化失败，请联系管理员");
                    }

                    log.info("用户 {} 信誉统计记录初始化成功", userId);
                } catch (Exception e) {
                    log.error("用户 {} 信誉统计记录初始化异常", userId, e);
                    return ApiResponse.error("信誉统计记录初始化失败：" + e.getMessage());
                }
            }
            
            // 获取保护期信息
            UserProtectionService.ProtectionInfo protectionInfo = 
                userProtectionService.getProtectionInfo(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", stats.getUserId());
            result.put("currentScore", stats.getCurrentScore());
            result.put("currentLevel", stats.getCurrentLevel());
            result.put("totalEarned", stats.getTotalEarned());
            result.put("totalDeducted", stats.getTotalDeducted());
            result.put("consecutiveLoginDays", stats.getConsecutiveLoginDays());
            result.put("consecutiveStudyDays", stats.getConsecutiveStudyDays());
            result.put("consecutiveNoStudyDays", stats.getConsecutiveNoStudyDays());
            result.put("lastLoginDate", stats.getLastLoginDate());
            result.put("lastStudyDate", stats.getLastStudyDate());
            result.put("lastScoreUpdate", stats.getLastScoreUpdate());
            
            // 保护期信息
            result.put("isInProtection", protectionInfo.isInProtection());
            result.put("protectionEndTime", protectionInfo.getEndTime());
            result.put("protectionHoursRemaining", protectionInfo.getRemainingHours());
            
            // 扣分限制信息
            result.put("weeklyDeductPoints", stats.getWeeklyDeductPoints());
            result.put("monthlyDeductPoints", stats.getMonthlyDeductPoints());
            
            return ApiResponse.success(result, "获取用户信誉统计成功");
            
        } catch (Exception e) {
            log.error("获取用户信誉统计失败", e);
            return ApiResponse.error("获取用户信誉统计失败");
        }
    }

    /**
     * 获取用户信誉变更记录
     */
    @GetMapping("/logs")
    @Operation(summary = "获取信誉变更记录", description = "获取用户的信誉分数变更历史记录")
    public ApiResponse<List<ReputationLog>> getReputationLogs(
            @AuthenticationPrincipal JwtUserDetails userDetails,
            @Parameter(description = "记录数量限制") @RequestParam(defaultValue = "20") int limit) {
        try {
            String userId = userDetails.getId();
            List<ReputationLog> logs = reputationLogMapper.getRecentLogs(userId, limit);
            
            return ApiResponse.success(logs, "获取信誉变更记录成功");
            
        } catch (Exception e) {
            log.error("获取信誉变更记录失败", e);
            return ApiResponse.error("获取信誉变更记录失败");
        }
    }

    /**
     * 获取指定分类的信誉记录
     */
    @GetMapping("/logs/{category}")
    @Operation(summary = "获取指定分类的信誉记录", description = "获取用户指定分类的信誉变更记录")
    public ApiResponse<List<ReputationLog>> getReputationLogsByCategory(
            @AuthenticationPrincipal JwtUserDetails userDetails,
            @Parameter(description = "记录分类") @PathVariable String category,
            @Parameter(description = "记录数量限制") @RequestParam(defaultValue = "10") int limit) {
        try {
            String userId = userDetails.getId();
            List<ReputationLog> logs = reputationLogMapper.getLogsByCategory(userId, category, limit);
            
            return ApiResponse.success(logs, "获取分类信誉记录成功");
            
        } catch (Exception e) {
            log.error("获取分类信誉记录失败", e);
            return ApiResponse.error("获取分类信誉记录失败");
        }
    }

    /**
     * 获取保护期状态
     */
    @GetMapping("/protection")
    @Operation(summary = "获取保护期状态", description = "获取用户当前的保护期状态信息")
    public ApiResponse<Map<String, Object>> getProtectionStatus(
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        try {
            String userId = userDetails.getId();
            UserProtectionService.ProtectionInfo protectionInfo = 
                userProtectionService.getProtectionInfo(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("isInProtection", protectionInfo.isInProtection());
            result.put("endTime", protectionInfo.getEndTime());
            result.put("remainingHours", protectionInfo.getRemainingHours());
            result.put("remainingMinutes", protectionInfo.getRemainingMinutes());
            
            return ApiResponse.success(result, "获取保护期状态成功");
            
        } catch (Exception e) {
            log.error("获取保护期状态失败", e);
            return ApiResponse.error("获取保护期状态失败");
        }
    }

    /**
     * 管理员手动调整用户信誉分数
     */
    @PostMapping("/admin/adjust")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "管理员调整信誉分数", description = "管理员手动调整用户的信誉分数")
    public ApiResponse<String> adjustUserReputation(
            @AuthenticationPrincipal JwtUserDetails adminDetails,
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "调整分数（正数增加，负数扣除）") @RequestParam int points,
            @Parameter(description = "调整原因") @RequestParam String reason) {
        try {
            String adminId = adminDetails.getId();
            reputationService.adjustPoints(userId, points, reason, adminId);
            
            String message = points > 0 ? 
                String.format("成功为用户增加%d分", points) : 
                String.format("成功为用户扣除%d分", Math.abs(points));
            
            return ApiResponse.success(null, message);
            
        } catch (Exception e) {
            log.error("管理员调整用户信誉分数失败", e);
            return ApiResponse.error("调整用户信誉分数失败");
        }
    }

    /**
     * 管理员延长用户保护期
     */
    @PostMapping("/admin/extend-protection")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "延长用户保护期", description = "管理员延长用户的保护期时间")
    public ApiResponse<String> extendUserProtection(
            @AuthenticationPrincipal JwtUserDetails adminDetails,
            @Parameter(description = "用户ID") @RequestParam String userId,
            @Parameter(description = "延长小时数") @RequestParam int hours) {
        try {
            userProtectionService.extendUserProtection(userId, hours);
            
            return ApiResponse.success(null, String.format("成功延长用户保护期%d小时", hours));
            
        } catch (Exception e) {
            log.error("延长用户保护期失败", e);
            return ApiResponse.error("延长用户保护期失败");
        }
    }

    /**
     * 获取信誉等级配置信息
     */
    @GetMapping("/levels")
    @Operation(summary = "获取信誉等级配置", description = "获取所有信誉等级的配置信息")
    public ApiResponse<List<Map<String, Object>>> getReputationLevels() {
        try {
            List<Map<String, Object>> levels = new ArrayList<>();
            levels.add(createLevelInfo("newbie", "新手", 0, 99, "#87d068"));
            levels.add(createLevelInfo("bronze", "青铜", 100, 299, "#CD7F32"));
            levels.add(createLevelInfo("silver", "白银", 300, 599, "#C0C0C0"));
            levels.add(createLevelInfo("gold", "黄金", 600, 999, "#FFD700"));
            levels.add(createLevelInfo("platinum", "铂金", 1000, 1999, "#E5E4E2"));
            levels.add(createLevelInfo("diamond", "钻石", 2000, 4999, "#B9F2FF"));
            levels.add(createLevelInfo("master", "大师", 5000, 9999, "#FF6B6B"));
            levels.add(createLevelInfo("grandmaster", "宗师", 10000, 999999, "#9B59B6"));

            return ApiResponse.success(levels, "获取信誉等级配置成功");

        } catch (Exception e) {
            log.error("获取信誉等级配置失败", e);
            return ApiResponse.error("获取信誉等级配置失败");
        }
    }

    /**
     * 创建等级信息
     */
    private Map<String, Object> createLevelInfo(String level, String name, int minScore, int maxScore, String color) {
        Map<String, Object> levelInfo = new HashMap<>();
        levelInfo.put("level", level);
        levelInfo.put("name", name);
        levelInfo.put("minScore", minScore);
        levelInfo.put("maxScore", maxScore);
        levelInfo.put("color", color);
        return levelInfo;
    }
}
