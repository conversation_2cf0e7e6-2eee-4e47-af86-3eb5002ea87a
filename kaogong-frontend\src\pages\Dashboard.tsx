import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Typography, Space, Divider, Toast, Spin, Card, Progress } from '@douyinfe/semi-ui';
import {
  IconUser,
  IconPrizeStroked,
  IconBookStroked,
  IconUserGroup,
  IconCalendar,
  IconStar,
  IconRefresh,
  IconLineChartStroked,
  IconArrowUp,
  IconClock,
  IconTickCircle,
  IconBarChartHStroked
} from '@douyinfe/semi-icons';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { useAuthStore } from '../stores/useAuthStore';
import { studyService, StudyOverviewResponse, StudyTrendsResponse } from '../services/studyService';
import Navigation from '../components/Navigation';
import '../styles/dashboard.css';

const { Title, Text } = Typography;

// 模拟数据 - 后续会从API获取
const mockData = {
  deskRanking: [
    { id: '1', name: '小明', score: 1250, isMe: false, avatar: '👨' },
    { id: '2', name: '我', score: 1180, isMe: true, avatar: '😊' },
    { id: '3', name: '小红', score: 1150, avatar: '👩' },
    { id: '4', name: '小李', score: 1100, avatar: '🧑' },
    { id: '5', name: '小王', score: 1050, avatar: '👦' },
  ],
  studyStats: {
    todayQuestions: 25,
    todayTarget: 50,
    weeklyHours: 12.5,
    weeklyTarget: 20,
    totalQuestions: 1580,
    accuracy: 78
  },
  recentActivities: [
    { id: '1', type: 'study', content: '完成了行测逻辑推理 20题', time: '2小时前', icon: '📚' },
    { id: '2', type: 'achievement', content: '解锁成就：连续学习7天', time: '3小时前', icon: '🏆' },
    { id: '3', type: 'desk', content: '在小桌"冲刺小队"中排名上升到第2位', time: '5小时前', icon: '📈' },
    { id: '4', type: 'social', content: '小红完成了今日学习目标', time: '1天前', icon: '👏' },
  ]
};

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // 状态管理
  const [studyOverview, setStudyOverview] = useState<StudyOverviewResponse | null>(null);
  const [studyTrends, setStudyTrends] = useState<StudyTrendsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [chartType, setChartType] = useState<'bar' | 'line'>('bar');

  // 准备图表数据
  const prepareChartData = () => {
    if (!studyTrends?.dailyData || studyTrends.dailyData.length === 0) {
      return [];
    }

    return studyTrends.dailyData.map(trend => ({
      date: new Date(trend.date).toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      }),
      questionCount: trend.questionCount,
      accuracy: Math.round(trend.accuracyRate)
    }));
  };

  // 渲染图表
  const renderChart = () => {
    const data = prepareChartData();

    if (data.length === 0) {
      return (
        <div style={{
          textAlign: 'center',
          padding: '32px',
          color: 'var(--semi-color-text-2)'
        }}>
          <IconBarChartHStroked size="large" style={{ marginBottom: '8px' }} />
          <div>暂无学习趋势数据</div>
          <div style={{ fontSize: '12px', marginTop: '4px' }}>
            开始刷题后即可查看趋势
          </div>
        </div>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={200}>
        {chartType === 'bar' ? (
          <BarChart data={data} margin={{ top: 10, right: 20, left: 10, bottom: 10 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--semi-color-border)" />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12, fill: 'var(--semi-color-text-2)' }}
              axisLine={{ stroke: 'var(--semi-color-border)' }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: 'var(--semi-color-text-2)' }}
              axisLine={{ stroke: 'var(--semi-color-border)' }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'var(--semi-color-bg-2)',
                border: '1px solid var(--semi-color-border)',
                borderRadius: '6px',
                fontSize: '12px'
              }}
              formatter={(value, name) => [
                name === 'questionCount' ? `${value}题` : `${value}%`,
                name === 'questionCount' ? '题目数量' : '正确率'
              ]}
            />
            <Bar dataKey="questionCount" fill="var(--accent-blue)" radius={[2, 2, 0, 0]} />
            <Bar dataKey="accuracy" fill="var(--accent-green)" radius={[2, 2, 0, 0]} />
          </BarChart>
        ) : (
          <LineChart data={data} margin={{ top: 10, right: 20, left: 10, bottom: 10 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="var(--semi-color-border)" />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12, fill: 'var(--semi-color-text-2)' }}
              axisLine={{ stroke: 'var(--semi-color-border)' }}
            />
            <YAxis
              tick={{ fontSize: 12, fill: 'var(--semi-color-text-2)' }}
              axisLine={{ stroke: 'var(--semi-color-border)' }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'var(--semi-color-bg-2)',
                border: '1px solid var(--semi-color-border)',
                borderRadius: '6px',
                fontSize: '12px'
              }}
              formatter={(value, name) => [
                name === 'questionCount' ? `${value}题` : `${value}%`,
                name === 'questionCount' ? '题目数量' : '正确率'
              ]}
            />
            <Line
              type="monotone"
              dataKey="questionCount"
              stroke="var(--accent-blue)"
              strokeWidth={2}
              dot={{ fill: 'var(--accent-blue)', strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="accuracy"
              stroke="var(--accent-green)"
              strokeWidth={2}
              dot={{ fill: 'var(--accent-green)', strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        )}
      </ResponsiveContainer>
    );
  };

  // 获取学习统计数据
  const fetchStudyData = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }

      // 并行获取概览和趋势数据
      const [overviewResponse, trendsResponse] = await Promise.all([
        studyService.getStudyOverview('week'),
        studyService.getStudyTrends('week', 'day')
      ]);

      setStudyOverview(overviewResponse);
      setStudyTrends(trendsResponse);

      // 如果是手动刷新，显示成功提示
      if (!showLoading) {
        Toast.success('数据已更新');
      }
    } catch (error: any) {
      console.error('获取学习数据失败:', error);
      const errorMessage = error.response?.data?.message || '获取学习数据失败，请稍后重试';
      Toast.error(errorMessage);

      // 如果是首次加载失败，设置默认值避免页面空白
      if (showLoading) {
        setStudyOverview({
          totalQuestions: 0,
          totalCorrect: 0,
          averageAccuracy: 0,
          totalStudyTime: 0,
          studyDays: 0,
          currentStreak: 0
        });
        setStudyTrends({ data: [] });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchStudyData();
  }, []);

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);



  // 获取问候语
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 6) return '夜深了';
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  // 获取学习状态
  const getStudyStatus = () => {
    // 使用真实数据或模拟数据
    const todayQuestions = studyOverview?.totalQuestions ?
      Math.floor(studyOverview.totalQuestions / 7) : // 假设平均每天的题数
      mockData.studyStats.todayQuestions;
    const todayTarget = mockData.studyStats.todayTarget;
    const progress = (todayQuestions / todayTarget) * 100;

    if (progress >= 100) return { text: '今日目标已完成', color: 'var(--accent-green)' };
    if (progress >= 50) return { text: '学习进行中', color: 'var(--accent-blue)' };
    return { text: '开始今日学习', color: 'var(--accent-orange)' };
  };

  const studyStatus = getStudyStatus();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const handleQuickAction = (action: string) => {
    console.log(`执行操作: ${action}`);

    switch (action) {
      case 'start-study':
        navigate('/study-records/create');
        break;
      case 'view-mistakes':
        navigate('/study-records'); // 暂时跳转到记录列表，后续会有专门的错题页面
        break;
      case 'study-plan':
        console.log('学习计划功能待开发');
        break;
      case 'join-desk':
        navigate('/desks');
        break;
      default:
        console.log('未知操作:', action);
    }
  };

  return (
    <>
      <Navigation />
      <div className="dashboard-container" style={{ paddingTop: '80px' }}>
        {/* 浮动装饰元素 */}
        <div className="floating-emoji" style={{
          top: '15%',
          left: '8%',
          animationDelay: '0s',
          fontSize: '28px',
          opacity: 0.4
        }}>
          📚
        </div>
        <div className="floating-emoji" style={{
          top: '25%',
          right: '12%',
          animationDelay: '2s',
          fontSize: '24px',
          opacity: 0.3
        }}>
          ✏️
        </div>
        <div className="floating-emoji" style={{
          bottom: '30%',
          left: '15%',
          animationDelay: '4s',
          fontSize: '26px',
          opacity: 0.35
        }}>
          🎯
        </div>
        <div className="floating-emoji" style={{
          bottom: '20%',
          right: '18%',
          animationDelay: '6s',
          fontSize: '22px',
          opacity: 0.25
        }}>
          🌟
        </div>
        <div className="floating-emoji" style={{
          top: '60%',
          left: '5%',
          animationDelay: '8s',
          fontSize: '20px',
          opacity: 0.3
        }}>
          📖
        </div>
        <div className="floating-emoji" style={{
          top: '70%',
          right: '8%',
          animationDelay: '10s',
          fontSize: '24px',
          opacity: 0.4
        }}>
          🏆
        </div>

        <div className="dashboard-content">
        {/* 个人信息横幅 */}
        <div className="user-banner">
          <div className="user-avatar">
            {user?.nickname?.charAt(0) || user?.username?.charAt(0) || '😊'}
          </div>
          <div className="user-info">
            <h1 className="user-name">
              {getGreeting()}，{user?.nickname || user?.username}！
            </h1>
            <div className="user-status">
              <div className="reputation-badge">
                <IconStar style={{ marginRight: '4px' }} />
                {user?.reputationLevel || '学员'} · {user?.reputationScore || 85}分
              </div>
              <div className="study-status" style={{ backgroundColor: studyStatus.color }}>
                {studyStatus.text}
              </div>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                {currentTime.toLocaleDateString('zh-CN', { 
                  month: 'long', 
                  day: 'numeric',
                  weekday: 'long'
                })}
              </Text>
            </div>
          </div>
        </div>

        {/* 核心功能区域 */}
        <div className="main-features">
          {/* 我的小桌 */}
          <div className="feature-card">
            <div className="feature-card-title">
              <IconPrizeStroked style={{ color: 'var(--accent-orange)' }} />
              我的小桌
            </div>
            <div className="desk-ranking">
              {mockData.deskRanking.map((member, index) => (
                <div key={member.id} className="ranking-item">
                  <div className={`ranking-position ${member.isMe ? 'me' : ''}`}>
                    {index + 1}
                  </div>
                  <div className="member-avatar">
                    {member.avatar}
                  </div>
                  <div className="member-info">
                    <span className="member-name">
                      {member.isMe ? '我' : member.name}
                    </span>
                    <span className="member-score">{member.score}分</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 学习概览 */}
          <div className="feature-card">
            <div className="feature-card-title">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <IconBookStroked style={{ color: 'var(--accent-blue)' }} />
                学习概览
              </div>
              <Button
                icon={<IconRefresh />}
                type="tertiary"
                size="small"
                loading={refreshing}
                onClick={() => fetchStudyData(false)}
                style={{
                  padding: '4px 8px',
                  minWidth: 'auto'
                }}
              />
            </div>
            <div className="study-overview">
              <Spin spinning={loading}>
                <div className="study-stat">
                  <span className="stat-label">今日刷题</span>
                  <span className="stat-value">
                    {studyOverview?.totalQuestions ?
                      Math.floor(studyOverview.totalQuestions / 7) :
                      mockData.studyStats.todayQuestions
                    }/{mockData.studyStats.todayTarget}
                  </span>
                </div>
                <div className="study-stat">
                  <span className="stat-label">本周时长</span>
                  <span className="stat-value">
                    {studyOverview?.totalStudyTime ?
                      Math.floor(studyOverview.totalStudyTime / 60) :
                      mockData.studyStats.weeklyHours
                    }h
                  </span>
                </div>
                <div className="study-stat">
                  <span className="stat-label">总题数</span>
                  <span className="stat-value">
                    {studyOverview?.totalQuestions || mockData.studyStats.totalQuestions}
                  </span>
                </div>
                <div className="study-stat">
                  <span className="stat-label">正确率</span>
                  <span className="stat-value">
                    {studyOverview?.averageAccuracy !== undefined ?
                      Math.round(studyOverview.averageAccuracy > 1 ?
                        studyOverview.averageAccuracy :
                        studyOverview.averageAccuracy * 100) :
                      mockData.studyStats.accuracy
                    }%
                  </span>
                </div>
                <div className="study-stat">
                  <span className="stat-label">连续天数</span>
                  <span className="stat-value">
                    {studyOverview?.currentStreak || 0}天
                  </span>
                </div>
                <div className="study-stat">
                  <span className="stat-label">学习天数</span>
                  <span className="stat-value">
                    {studyOverview?.studyDays || 0}天
                  </span>
                </div>
              </Spin>
            </div>
          </div>

          {/* 学习趋势 */}
          <div className="feature-card">
            <div className="feature-card-title">
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <IconLineChartStroked style={{ color: 'var(--accent-green)' }} />
                  本周学习趋势
                </div>
                <div style={{ display: 'flex', gap: '4px' }}>
                  <Button
                    size="small"
                    type={chartType === 'bar' ? 'primary' : 'tertiary'}
                    icon={<IconBarChartHStroked />}
                    onClick={() => setChartType('bar')}
                    style={{
                      fontSize: '12px',
                      height: '28px',
                      padding: '0 8px'
                    }}
                  >
                    柱状图
                  </Button>
                  <Button
                    size="small"
                    type={chartType === 'line' ? 'primary' : 'tertiary'}
                    icon={<IconLineChartStroked />}
                    onClick={() => setChartType('line')}
                    style={{
                      fontSize: '12px',
                      height: '28px',
                      padding: '0 8px'
                    }}
                  >
                    折线图
                  </Button>
                </div>
              </div>
            </div>
            <div className="study-trends">
              <Spin spinning={loading}>
                {renderChart()}
              </Spin>
            </div>
          </div>

          {/* 快速开始 */}
          <div className="feature-card">
            <div className="feature-card-title">
              ⚡ 快速开始
            </div>
            <div className="quick-actions">
              <button 
                className="action-button primary"
                onClick={() => handleQuickAction('start-study')}
              >
                🚀 开始刷题
              </button>
              <button 
                className="action-button"
                onClick={() => handleQuickAction('view-mistakes')}
              >
                📖 查看错题
              </button>
              <button 
                className="action-button"
                onClick={() => handleQuickAction('study-plan')}
              >
                📅 学习计划
              </button>
              <button 
                className="action-button"
                onClick={() => handleQuickAction('join-desk')}
              >
                👥 加入小桌
              </button>
            </div>
          </div>
        </div>

        {/* 动态信息流区域 */}
        <div className="activity-feed">
          <div className="feature-card" style={{ transform: 'rotate(-0.2deg)' }}>
            <div className="feature-card-title">
              📢 最近动态
            </div>
            <div className="activities-list">
              {mockData.recentActivities.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className="activity-icon">{activity.icon}</div>
                  <div className="activity-content">
                    <div className="activity-text">{activity.content}</div>
                    <div className="activity-time">{activity.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 底部推荐区域 */}
        <div className="recommendations">
          <div className="recommendation-section">
            <div className="feature-card">
              <div className="feature-card-title">
                💡 推荐小桌
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">🔥</div>
                <div className="rec-content">
                  <div className="rec-title">冲刺小队</div>
                  <div className="rec-desc">6/8人 · 平均分1200+ · 活跃度高</div>
                </div>
                <Button size="small" type="primary">加入</Button>
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">⭐</div>
                <div className="rec-content">
                  <div className="rec-title">稳步前进组</div>
                  <div className="rec-desc">4/8人 · 适合新手 · 氛围友好</div>
                </div>
                <Button size="small" type="secondary">查看</Button>
              </div>
            </div>
          </div>

          <div className="recommendation-section">
            <div className="feature-card">
              <div className="feature-card-title">
                📖 热门学习计划
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">📚</div>
                <div className="rec-content">
                  <div className="rec-title">30天行测突破</div>
                  <div className="rec-desc">已有128人参与 · 平均提升15分</div>
                </div>
                <Button size="small" type="primary">开始</Button>
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">🎯</div>
                <div className="rec-content">
                  <div className="rec-title">申论写作训练</div>
                  <div className="rec-desc">21天计划 · 专项提升</div>
                </div>
                <Button size="small" type="secondary">了解</Button>
              </div>
            </div>
          </div>

          <div className="recommendation-section">
            <div className="feature-card">
              <div className="feature-card-title">
                🤝 学习伙伴
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">👨‍💼</div>
                <div className="rec-content">
                  <div className="rec-title">张同学</div>
                  <div className="rec-desc">相似进度 · 同城 · 在线</div>
                </div>
                <Button size="small" type="primary">关注</Button>
              </div>
              <div className="recommendation-item">
                <div className="rec-icon">👩‍💼</div>
                <div className="rec-content">
                  <div className="rec-title">李同学</div>
                  <div className="rec-desc">学霸级别 · 乐于助人</div>
                </div>
                <Button size="small" type="secondary">查看</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default Dashboard;
