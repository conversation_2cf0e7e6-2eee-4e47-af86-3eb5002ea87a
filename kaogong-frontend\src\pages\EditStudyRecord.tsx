import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Button, 
  Space, 
  Typography, 
  TextArea,
  Input,
  Toast,
  Spin,
  Empty,
  Modal
} from '@douyinfe/semi-ui';
import { IconArrowLeft, IconSave, IconRefresh } from '@douyinfe/semi-icons';
import { useNavigate, useParams } from 'react-router-dom';
import { studyService, StudyRecordResponse } from '../services/studyService';
import { useStudyRecordForm } from '../hooks/useStudyRecordForm';
import '../styles/create-study-record.css';

const { Title, Text } = Typography;

// 学习模块映射
const MODULE_TYPE_MAP: Record<string, string> = {
  'math': '数学运算',
  'logic': '逻辑推理',
  'language': '言语理解',
  'knowledge': '常识判断',
  'essay': '申论写作'
};

// 浮动装饰组件
const FloatingDecorations: React.FC = () => {
  const decorations = ['📚', '✏️', '📝', '🎯', '💡', '⭐'];
  
  return (
    <>
      {decorations.map((emoji, index) => (
        <div
          key={index}
          className="floating-emoji"
          style={{
            position: 'absolute',
            top: `${15 + index * 12}%`,
            left: `${5 + (index % 2) * 90}%`,
            animationDelay: `${index * 0.8}s`,
            zIndex: 1
          }}
        >
          {emoji}
        </div>
      ))}
    </>
  );
};

// 格式化学习时长
const formatStudyDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours > 0) {
    return `${hours}小时${mins > 0 ? `${mins}分钟` : ''}`;
  }
  return `${mins}分钟`;
};

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// 安全解析薄弱知识点数据并转换为逗号分隔的字符串
const parseWeakPointsToString = (weakPoints: string | string[] | null | undefined): string => {
  console.log('Parsing weakPoints:', weakPoints, 'Type:', typeof weakPoints);

  // 如果是null或undefined，返回空字符串
  if (!weakPoints) {
    return '';
  }

  // 如果已经是数组，直接join
  if (Array.isArray(weakPoints)) {
    const result = weakPoints.join(', ');
    console.log('Array result:', result);
    return result;
  }

  // 如果是字符串，尝试解析JSON
  if (typeof weakPoints === 'string') {
    try {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(weakPoints);
      if (Array.isArray(parsed)) {
        const result = parsed.join(', ');
        console.log('JSON parsed result:', result);
        return result;
      }
      // 如果解析结果不是数组，直接返回字符串
      return parsed.toString();
    } catch (error) {
      // JSON解析失败，检查是否已经是逗号分隔的字符串
      if (weakPoints.includes(',')) {
        // 清理并重新格式化
        const result = weakPoints.split(',').map(point => point.trim()).filter(point => point.length > 0).join(', ');
        console.log('Comma-separated result:', result);
        return result;
      }
      // 单个值，直接返回
      const result = weakPoints.trim();
      console.log('Single value result:', result);
      return result;
    }
  }

  // 其他情况返回空字符串
  return '';
};

interface EditStudyRecordProps {}

const EditStudyRecord: React.FC<EditStudyRecordProps> = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  // 状态管理
  const [record, setRecord] = useState<StudyRecordResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [notFound, setNotFound] = useState(false);
  const [formApi, setFormApi] = useState<any>(null);
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);

  // 使用表单Hook
  const {
    loading: submitting,
    isDirty,
    errors: formErrors,
    handleSubmit,
    handleReset
  } = useStudyRecordForm({
    mode: 'edit',
    initialData: record || undefined,
    onSuccess: () => {
      Toast.success('记录更新成功！');
      navigate(`/study-records/${id}`);
    },
    onError: (error) => {
      console.error('更新失败:', error);
      Toast.error('更新失败，请重试');
    }
  });

  // 获取记录详情
  const fetchRecordDetail = async (recordId: string) => {
    setLoading(true);
    setNotFound(false);
    try {
      const response = await studyService.getRecordDetail(recordId);
      setRecord(response);
    } catch (error: any) {
      console.error('获取记录详情失败:', error);
      if (error.response?.status === 404) {
        setNotFound(true);
      } else if (error.response?.status === 403) {
        Toast.error('无权限访问此记录');
        navigate('/study-records');
      } else {
        Toast.error(error.message || '获取记录详情失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (id) {
      fetchRecordDetail(id);
    }
  }, [id]);

  // 预填充表单数据
  useEffect(() => {
    if (record && formApi && typeof formApi.setValue === 'function') {
      try {
        // 使用 setValue 方法逐个设置字段值
        formApi.setValue('notes', record.notes || '');
        formApi.setValue('weakPoints', parseWeakPointsToString(record.weakPoints));
      } catch (error) {
        console.error('设置表单值失败:', error);
        Toast.error('表单初始化失败，请刷新页面重试');
      }
    }
  }, [record, formApi]);

  // 处理导航
  const handleNavigation = (path: string) => {
    if (isDirty) {
      setPendingNavigation(path);
      setShowLeaveModal(true);
    } else {
      navigate(path);
    }
  };

  // 确认离开
  const handleConfirmLeave = () => {
    setShowLeaveModal(false);
    if (pendingNavigation) {
      navigate(pendingNavigation);
    }
  };

  // 取消离开
  const handleCancelLeave = () => {
    setShowLeaveModal(false);
    setPendingNavigation(null);
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    if (!formApi) {
      Toast.error('表单未初始化，请刷新页面重试');
      return;
    }

    if (typeof formApi.getValues !== 'function') {
      Toast.error('表单API异常，请刷新页面重试');
      return;
    }

    try {
      const values = formApi.getValues();
      await handleSubmit(values);
    } catch (error) {
      console.error('获取表单值失败:', error);
      Toast.error('提交失败，请重试');
    }
  };

  // 重试加载
  const handleRetry = () => {
    if (id) {
      fetchRecordDetail(id);
    }
  };

  // 404页面
  if (notFound) {
    return (
      <div style={{ 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
        padding: '24px',
        position: 'relative'
      }}>
        <FloatingDecorations />
        
        <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 10 }}>
          <div style={{ textAlign: 'center', marginTop: '100px' }}>
            <Empty
              title="记录不存在"
              description="您要编辑的学习记录不存在或已被删除"
              style={{ padding: '40px 0' }}
            >
              <Button
                theme="solid"
                type="primary"
                icon={<IconArrowLeft />}
                onClick={() => navigate('/study-records')}
                className="sketch-button primary"
              >
                返回记录列表
              </Button>
            </Empty>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 100%)',
      padding: '24px',
      position: 'relative'
    }}>
      <FloatingDecorations />
      
      <div style={{ maxWidth: '800px', margin: '0 auto', position: 'relative', zIndex: 10 }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '32px' }}>
          <Button
            theme="borderless"
            icon={<IconArrowLeft />}
            onClick={() => handleNavigation(`/study-records/${id}`)}
            style={{ 
              marginBottom: '16px',
              transform: 'rotate(-0.5deg)'
            }}
          >
            返回详情
          </Button>
          <Title 
            heading={2} 
            className="handwritten-title large"
            style={{ 
              color: 'var(--ink-dark)',
              marginBottom: '8px'
            }}
          >
            ✏️ 编辑学习记录
          </Title>
          <Text 
            type="secondary" 
            style={{ 
              fontSize: 'var(--font-size-lg)',
              fontFamily: 'var(--font-handwritten)'
            }}
          >
            修改学习笔记和薄弱知识点
          </Text>
        </div>

        <Spin spinning={loading}>
          {record && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
              {/* 只读信息卡片 */}
              <Card 
                className="sketch-card"
                title={
                  <span style={{ fontFamily: 'var(--font-handwritten)' }}>
                    📊 记录信息（只读）
                  </span>
                }
              >
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px'
                }}>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                      学习日期
                    </Text>
                    <Text>{formatDate(record.studyDate)}</Text>
                  </div>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                      学习模块
                    </Text>
                    <Text>{MODULE_TYPE_MAP[record.moduleType] || record.moduleType}</Text>
                  </div>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                      题目统计
                    </Text>
                    <Text>{record.correctCount}/{record.questionCount} 题</Text>
                  </div>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                      正确率
                    </Text>
                    <Text style={{ color: 'var(--accent-green)' }}>
                      {record.accuracyRate.toFixed(1)}%
                    </Text>
                  </div>
                  <div>
                    <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                      学习时长
                    </Text>
                    <Text>{formatStudyDuration(record.studyDuration)}</Text>
                  </div>
                </div>
              </Card>

              {/* 编辑表单 */}
              <Card 
                className="sketch-card"
                title={
                  <span style={{ fontFamily: 'var(--font-handwritten)' }}>
                    ✏️ 可编辑内容
                  </span>
                }
              >
                <Form
                  getFormApi={(api) => setFormApi(api)}
                  onSubmit={handleFormSubmit}
                  labelPosition="top"
                  style={{ width: '100%' }}
                  initValues={{
                    notes: record?.notes || '',
                    weakPoints: parseWeakPointsToString(record?.weakPoints)
                  }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
                    {/* 学习笔记 */}
                    <div>
                      <Form.TextArea
                        field="notes"
                        label="学习笔记"
                        placeholder="记录学习过程中的心得体会、重点难点等..."
                        rows={6}
                        maxCount={1000}
                        showClear
                        className="sketch-input"
                        style={{ width: '100%' }}
                      />
                      {formErrors.notes && (
                        <Text type="danger" size="small" style={{ marginTop: '4px', display: 'block' }}>
                          {formErrors.notes}
                        </Text>
                      )}
                    </div>

                    {/* 薄弱知识点 */}
                    <div>
                      <Form.Input
                        field="weakPoints"
                        label="薄弱知识点"
                        placeholder="用逗号分隔多个知识点，如：函数图像,数列求和,概率计算"
                        showClear
                        className="sketch-input"
                        style={{ width: '100%' }}
                      />
                      {formErrors.weakPoints ? (
                        <Text type="danger" size="small" style={{ marginTop: '4px', display: 'block' }}>
                          {formErrors.weakPoints}
                        </Text>
                      ) : (
                        <Text
                          type="tertiary"
                          size="small"
                          style={{ marginTop: '4px', display: 'block' }}
                        >
                          💡 提示：多个知识点请用逗号分隔
                        </Text>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'flex-end',
                      gap: '12px',
                      paddingTop: '20px',
                      borderTop: '1px dashed var(--border-light)'
                    }}>
                      <Button
                        theme="borderless"
                        icon={<IconRefresh />}
                        onClick={handleReset}
                        disabled={submitting}
                        style={{ transform: 'rotate(-0.5deg)' }}
                      >
                        重置
                      </Button>
                      <Button
                        theme="solid"
                        type="primary"
                        icon={<IconSave />}
                        htmlType="submit"
                        loading={submitting}
                        className="sketch-button primary"
                        style={{ transform: 'rotate(0.5deg)' }}
                      >
                        保存更改
                      </Button>
                    </div>
                  </div>
                </Form>
              </Card>
            </div>
          )}
        </Spin>

        {/* 错误状态重试 */}
        {!loading && !record && !notFound && (
          <div style={{ textAlign: 'center', marginTop: '100px' }}>
            <Empty
              title="加载失败"
              description="获取记录详情时出现错误"
            >
              <Button
                theme="solid"
                type="primary"
                onClick={handleRetry}
                className="sketch-button primary"
              >
                重试
              </Button>
            </Empty>
          </div>
        )}

        {/* 离开确认对话框 */}
        <Modal
          title="确认离开"
          visible={showLeaveModal}
          onOk={handleConfirmLeave}
          onCancel={handleCancelLeave}
          okText="确认离开"
          cancelText="继续编辑"
          okType="warning"
        >
          <p>您有未保存的更改，确定要离开吗？</p>
        </Modal>
      </div>
    </div>
  );
};

export default EditStudyRecord;
