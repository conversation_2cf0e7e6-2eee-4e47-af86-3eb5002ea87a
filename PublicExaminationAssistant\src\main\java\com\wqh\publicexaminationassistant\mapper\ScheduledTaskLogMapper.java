package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.ScheduledTaskLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 定时任务执行记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface ScheduledTaskLogMapper extends BaseMapper<ScheduledTaskLog> {

    /**
     * 查询指定任务在指定日期的执行记录
     */
    @Select("SELECT * FROM scheduled_task_logs " +
            "WHERE task_name = #{taskName} AND execution_date = #{executionDate} " +
            "ORDER BY start_time DESC LIMIT 1")
    ScheduledTaskLog selectByTaskAndDate(@Param("taskName") String taskName, 
                                        @Param("executionDate") LocalDate executionDate);

    /**
     * 检查任务今日是否已执行成功
     */
    @Select("SELECT COUNT(*) > 0 FROM scheduled_task_logs " +
            "WHERE task_name = #{taskName} AND execution_date = #{executionDate} " +
            "AND status = 'completed'")
    boolean isTaskCompletedToday(@Param("taskName") String taskName, 
                                @Param("executionDate") LocalDate executionDate);

    /**
     * 获取正在运行的任务
     */
    @Select("SELECT * FROM scheduled_task_logs " +
            "WHERE status = 'running' " +
            "ORDER BY start_time DESC")
    List<ScheduledTaskLog> selectRunningTasks();

    /**
     * 获取失败的任务记录
     */
    @Select("SELECT * FROM scheduled_task_logs " +
            "WHERE status = 'failed' AND execution_date >= #{startDate} " +
            "ORDER BY start_time DESC")
    List<ScheduledTaskLog> selectFailedTasks(@Param("startDate") LocalDate startDate);

    /**
     * 获取任务执行历史
     */
    @Select("SELECT * FROM scheduled_task_logs " +
            "WHERE task_name = #{taskName} " +
            "AND execution_date BETWEEN #{startDate} AND #{endDate} " +
            "ORDER BY execution_date DESC, start_time DESC " +
            "LIMIT #{limit}")
    List<ScheduledTaskLog> selectTaskHistory(@Param("taskName") String taskName,
                                           @Param("startDate") LocalDate startDate,
                                           @Param("endDate") LocalDate endDate,
                                           @Param("limit") int limit);

    /**
     * 更新任务状态为完成
     */
    @Update("UPDATE scheduled_task_logs " +
            "SET status = 'completed', end_time = #{endTime}, " +
            "processed_count = #{processedCount} " +
            "WHERE id = #{id}")
    int updateTaskCompleted(@Param("id") String id, 
                           @Param("endTime") LocalDateTime endTime,
                           @Param("processedCount") Integer processedCount);

    /**
     * 更新任务状态为失败
     */
    @Update("UPDATE scheduled_task_logs " +
            "SET status = 'failed', end_time = #{endTime}, " +
            "error_message = #{errorMessage} " +
            "WHERE id = #{id}")
    int updateTaskFailed(@Param("id") String id, 
                        @Param("endTime") LocalDateTime endTime,
                        @Param("errorMessage") String errorMessage);

    /**
     * 获取任务执行统计
     */
    @Select("SELECT " +
            "task_name, " +
            "COUNT(*) as total_executions, " +
            "SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_executions, " +
            "SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_executions, " +
            "AVG(CASE WHEN status = 'completed' AND end_time IS NOT NULL " +
            "    THEN TIMESTAMPDIFF(SECOND, start_time, end_time) ELSE NULL END) as avg_duration_seconds " +
            "FROM scheduled_task_logs " +
            "WHERE execution_date >= #{startDate} " +
            "GROUP BY task_name " +
            "ORDER BY task_name")
    List<java.util.Map<String, Object>> selectTaskStatistics(@Param("startDate") LocalDate startDate);

    /**
     * 清理过期的任务日志（保留最近30天）
     */
    @Select("DELETE FROM scheduled_task_logs " +
            "WHERE execution_date < #{cutoffDate}")
    int deleteExpiredLogs(@Param("cutoffDate") LocalDate cutoffDate);

    /**
     * 获取最近的任务执行概览
     */
    @Select("SELECT " +
            "execution_date, " +
            "COUNT(*) as total_tasks, " +
            "SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks, " +
            "SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks, " +
            "SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running_tasks " +
            "FROM scheduled_task_logs " +
            "WHERE execution_date >= #{startDate} " +
            "GROUP BY execution_date " +
            "ORDER BY execution_date DESC")
    List<java.util.Map<String, Object>> selectDailyTaskOverview(@Param("startDate") LocalDate startDate);
}
