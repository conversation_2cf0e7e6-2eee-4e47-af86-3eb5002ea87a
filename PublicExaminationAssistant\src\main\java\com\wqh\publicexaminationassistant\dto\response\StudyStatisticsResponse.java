package com.wqh.publicexaminationassistant.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 学习统计响应DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class StudyStatisticsResponse {

    /**
     * 总学习天数
     */
    private Integer totalStudyDays;

    /**
     * 总题目数
     */
    private Integer totalQuestions;

    /**
     * 总正确数
     */
    private Integer totalCorrect;

    /**
     * 总错题数
     */
    private Integer totalWrong;

    /**
     * 平均正确率(%)
     */
    private BigDecimal averageAccuracy;

    /**
     * 总学习时长(分钟)
     */
    private Integer totalStudyTime;

    /**
     * 当前连续学习天数
     */
    private Integer currentStreak;

    /**
     * 最长连续学习天数
     */
    private Integer longestStreak;

    /**
     * 最后学习日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastStudyDate;

    /**
     * 每日学习数据 (用于趋势图)
     */
    private List<DailyStudyData> dailyData;

    /**
     * 模块统计数据
     */
    private List<ModuleStatistics> moduleStats;

    /**
     * 薄弱知识点统计
     */
    private List<WeakPointStatistics> weakPointStats;

    /**
     * 每日学习数据内部类
     */
    @Data
    public static class DailyStudyData {
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;
        private Integer questionCount;
        private Integer correctCount;
        private BigDecimal accuracyRate;
        private Integer studyTime;
    }

    /**
     * 模块统计内部类
     */
    @Data
    public static class ModuleStatistics {
        private String moduleType;
        private String moduleName;
        private Integer questionCount;
        private Integer correctCount;
        private BigDecimal accuracyRate;
        private Integer studyTime;
        private Integer studyDays;
    }

    /**
     * 薄弱知识点统计内部类
     */
    @Data
    public static class WeakPointStatistics {
        private String point;
        private Integer frequency;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate lastAppeared;
        private List<String> relatedModules;
    }
}
