import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Space, Tooltip, Dropdown } from '@douyinfe/semi-ui';
import {
  IconHome,
  IconBookStroked,
  IconUserGroup,
  IconPrizeStroked,
  IconUser,
  IconSettingStroked,
  IconBell,
  IconExit,
  IconAlertTriangle,
  IconStar
} from '@douyinfe/semi-icons';
import { useAuthStore } from '../stores/useAuthStore';

interface NavigationProps {
  style?: React.CSSProperties;
  className?: string;
}

export const Navigation: React.FC<NavigationProps> = ({ style, className }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuthStore();

  const navigationItems = [
    {
      key: 'dashboard',
      path: '/dashboard',
      icon: <IconHome />,
      label: '首页',
      tooltip: '返回首页'
    },
    {
      key: 'study',
      path: '/study-records',
      icon: <IconBookStroked />,
      label: '学习',
      tooltip: '刷题记录'
    },
    {
      key: 'wrong-questions',
      path: '/wrong-questions',
      icon: <IconAlertTriangle />,
      label: '错题',
      tooltip: '错题本'
    },
    {
      key: 'desks',
      path: '/desks',
      icon: <IconUserGroup />,
      label: '小桌',
      tooltip: '小桌系统'
    },
    {
      key: 'reputation',
      path: '/reputation',
      icon: <IconStar />,
      label: '信誉',
      tooltip: '信誉中心'
    },
    {
      key: 'rankings',
      path: '/rankings',
      icon: <IconPrizeStroked />,
      label: '排行',
      tooltip: '排行榜'
    },
    {
      key: 'profile',
      path: '/profile',
      icon: <IconUser />,
      label: '我的',
      tooltip: '个人中心'
    }
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  // 处理登出功能
  const handleLogout = React.useCallback(() => {
    console.log('执行登出操作');
    logout();
    navigate('/login');
  }, [logout, navigate]);

  // 处理个人中心导航
  const handleProfileNavigation = React.useCallback(() => {
    console.log('点击个人中心');
    handleNavigation('/profile');
  }, [handleNavigation]);

  // 处理设置点击
  const handleSettingsClick = React.useCallback(() => {
    console.log('点击设置');
    // TODO: 实现设置页面功能
    console.log('设置功能待实现');
  }, []);

  // 用户下拉菜单内容
  const userDropdownMenu = React.useMemo(() => (
    <Dropdown.Menu
      style={{
        background: 'var(--paper-bg)',
        border: '2px solid var(--ink-dark)',
        borderRadius: '12px',
        boxShadow: '3px 3px 0px var(--shadow-light)',
        minWidth: '160px',
        transform: 'rotate(-0.5deg)',
        animation: 'dropdownAppear 0.3s ease-out'
      }}
    >
      <Dropdown.Item
        icon={<IconUser />}
        onClick={handleProfileNavigation}
        style={{
          fontFamily: 'var(--font-system)',
          color: 'var(--ink-dark)',
          padding: '8px 16px'
        }}
      >
        个人中心
      </Dropdown.Item>

      <Dropdown.Item
        icon={<IconSettingStroked />}
        onClick={handleSettingsClick}
        style={{
          fontFamily: 'var(--font-system)',
          color: 'var(--ink-dark)',
          padding: '8px 16px'
        }}
      >
        设置
      </Dropdown.Item>

      <Dropdown.Divider style={{ borderColor: 'var(--ink-light)' }} />

      <Dropdown.Item
        icon={<IconExit />}
        onClick={handleLogout}
        type="danger"
        style={{
          fontFamily: 'var(--font-system)',
          color: 'var(--accent-orange)',
          padding: '8px 16px'
        }}
      >
        退出登录
      </Dropdown.Item>
    </Dropdown.Menu>
  ), [handleProfileNavigation, handleSettingsClick, handleLogout]);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <nav 
      className={`navigation-bar ${className || ''}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        background: 'var(--paper-bg)',
        borderBottom: '2px solid var(--ink-dark)',
        padding: '12px 20px',
        boxShadow: '0 2px 8px var(--shadow-light)',
        ...style
      }}
    >
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        {/* Logo区域 */}
        <div 
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            cursor: 'pointer'
          }}
          onClick={() => handleNavigation('/dashboard')}
        >
          <div style={{
            width: '40px',
            height: '40px',
            background: 'linear-gradient(135deg, var(--accent-blue), var(--accent-purple))',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '20px',
            color: 'white',
            border: '2px solid var(--ink-dark)',
            transform: 'rotate(-5deg)'
          }}>
            📚
          </div>
          <div style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '20px',
            fontWeight: '700',
            color: 'var(--ink-dark)'
          }}>
            考公刷题系统
          </div>
        </div>

        {/* 导航菜单 */}
        <Space spacing={8}>
          {navigationItems.map((item) => (
            <Tooltip key={item.key} content={item.tooltip} position="bottom">
              <Button
                type={isActive(item.path) ? 'primary' : 'tertiary'}
                icon={item.icon}
                onClick={() => handleNavigation(item.path)}
                style={{
                  borderRadius: '20px',
                  border: '2px solid var(--ink-dark)',
                  transform: isActive(item.path) ? 'rotate(0deg)' : 'rotate(-1deg)',
                  transition: 'all 0.3s ease',
                  fontFamily: 'var(--font-handwritten)',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'rotate(0deg) translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = isActive(item.path) ? 'rotate(0deg)' : 'rotate(-1deg)';
                }}
              >
                {item.label}
              </Button>
            </Tooltip>
          ))}
        </Space>

        {/* 用户信息区域 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          {/* 通知按钮 */}
          <Tooltip content="通知" position="bottom">
            <Button
              type="tertiary"
              icon={<IconBell />}
              style={{
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                border: '2px solid var(--ink-dark)',
                transform: 'rotate(2deg)'
              }}
            />
          </Tooltip>

          {/* 设置按钮 */}
          <Tooltip content="设置" position="bottom">
            <Button
              type="tertiary"
              icon={<IconSettingStroked />}
              style={{
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                border: '2px solid var(--ink-dark)',
                transform: 'rotate(-2deg)'
              }}
            />
          </Tooltip>

          {/* 用户头像下拉菜单 */}
          <Dropdown
            trigger="click"
            render={userDropdownMenu}
            position="bottomRight"
            spacing={8}
            showTick={false}
            stopPropagation={true}
          >
            <div
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, var(--accent-orange), var(--accent-pink))',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '16px',
                color: 'white',
                fontWeight: 'bold',
                border: '2px solid var(--ink-dark)',
                cursor: 'pointer',
                transform: 'rotate(3deg)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'rotate(0deg) scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'rotate(3deg) scale(1)';
              }}
              title={`${user?.nickname || user?.username} (${user?.reputationLevel || '学员'})`}
            >
              {user?.nickname?.charAt(0) || user?.username?.charAt(0) || '😊'}
            </div>
          </Dropdown>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
