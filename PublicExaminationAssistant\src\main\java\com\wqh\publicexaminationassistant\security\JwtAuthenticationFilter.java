package com.wqh.publicexaminationassistant.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtTokenProvider jwtTokenProvider;

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${jwt.token-prefix}")
    private String tokenPrefix;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 跳过公开端点的JWT验证
        String requestPath = request.getRequestURI();
        if (isPublicPath(requestPath)) {
            log.debug("跳过公开端点的JWT验证: {}", requestPath);
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String token = resolveToken(request);
            if (StringUtils.hasText(token) && jwtTokenProvider.validateToken(token)) {
                Authentication authentication = jwtTokenProvider.getAuthentication(token);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                log.debug("设置用户认证信息到SecurityContext: {}", authentication.getName());
            }
        } catch (Exception e) {
            log.error("无法设置用户认证信息到SecurityContext", e);
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中解析Token
     */
    private String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(tokenHeader);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(tokenPrefix)) {
            return bearerToken.substring(tokenPrefix.length());
        }
        return null;
    }

    /**
     * 判断是否为公开路径
     */
    private boolean isPublicPath(String path) {
        // 公开的API文档路径
        if (path.contains("/api-docs") || path.contains("/swagger-ui")) {
            return true;
        }

        // 公开的测试路径
        if (path.startsWith("/api/v1/test/")) {
            return true;
        }

        // 公开的认证路径
        if (path.startsWith("/api/v1/auth/")) {
            return true;
        }

        // 公开的公告路径
        if (path.startsWith("/api/v1/announcements/")) {
            return true;
        }

        // 排行榜路径需要认证 - 移除公开配置
        // if (path.startsWith("/api/v1/rankings/")) {
        //     return true;
        // }

        // 公开的文件访问路径
        if (path.startsWith("/api/files/")) {
            return true;
        }

        return false;
    }
}
