import React, { useState } from 'react';
import { Card, Select, Button, Space, Typography, Tag } from '@douyinfe/semi-ui';
import { 
  IconListView, 
  IconRefresh, 
  IconTickCircle, 
  IconClock, 
  IconCrossCircleStroked 
} from '@douyinfe/semi-icons';

const { Text } = Typography;
const { Option } = Select;

/**
 * 筛选器功能测试组件
 * 用于验证筛选器的"全部"选项和重置功能
 */
const FilterTest: React.FC = () => {
  // 模拟筛选状态
  const [filters, setFilters] = useState({
    moduleType: '',
    masteryStatus: ''
  });

  // 学习模块映射
  const MODULE_TYPE_MAP = {
    'math': '数学运算',
    'logic': '逻辑推理',
    'language': '言语理解',
    'knowledge': '常识判断',
    'essay': '申论写作'
  };

  // 掌握状态映射
  const MASTERY_STATUS_MAP = {
    not_mastered: { text: '未掌握', color: 'red', icon: <IconCrossCircleStroked /> },
    reviewing: { text: '复习中', color: 'orange', icon: <IconClock /> },
    mastered: { text: '已掌握', color: 'green', icon: <IconTickCircle /> }
  };

  // 处理筛选变化
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 重置所有筛选器
  const handleResetFilters = () => {
    setFilters({
      moduleType: '',
      masteryStatus: ''
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2>筛选器功能测试</h2>
      
      {/* 筛选器 */}
      <Card style={{ marginBottom: '20px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          alignItems: 'end'
        }}>
          {/* 学习模块筛选 */}
          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              学习模块
            </Text>
            <Select
              placeholder="选择学习模块"
              value={filters.moduleType || 'all'}
              onChange={(value) => handleFilterChange('moduleType', value === 'all' ? '' : value)}
              style={{ width: '100%' }}
            >
              <Option key="all" value="all">
                <Space>
                  <IconListView />
                  全部模块
                </Space>
              </Option>
              {Object.entries(MODULE_TYPE_MAP).map(([key, value]) => (
                <Option key={key} value={key}>{value}</Option>
              ))}
            </Select>
          </div>

          {/* 掌握状态筛选 */}
          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              掌握状态
            </Text>
            <Select
              placeholder="选择掌握状态"
              value={filters.masteryStatus || 'all'}
              onChange={(value) => handleFilterChange('masteryStatus', value === 'all' ? '' : value)}
              style={{ width: '100%' }}
            >
              <Option key="all" value="all">
                <Space>
                  <IconListView />
                  全部状态
                </Space>
              </Option>
              {Object.entries(MASTERY_STATUS_MAP).map(([key, value]) => (
                <Option key={key} value={key}>
                  <Space>
                    {value.icon}
                    {value.text}
                  </Space>
                </Option>
              ))}
            </Select>
          </div>

          {/* 快速筛选 */}
          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              快速筛选
            </Text>
            <Space wrap>
              <Button
                size="small"
                type={!filters.masteryStatus ? 'primary' : 'tertiary'}
                icon={<IconListView />}
                onClick={() => handleFilterChange('masteryStatus', '')}
              >
                全部
              </Button>
              <Button
                size="small"
                type={filters.masteryStatus === 'not_mastered' ? 'primary' : 'tertiary'}
                icon={<IconCrossCircleStroked />}
                onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'not_mastered' ? '' : 'not_mastered')}
              >
                未掌握
              </Button>
              <Button
                size="small"
                type={filters.masteryStatus === 'reviewing' ? 'primary' : 'tertiary'}
                icon={<IconClock />}
                onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'reviewing' ? '' : 'reviewing')}
              >
                复习中
              </Button>
              <Button
                size="small"
                type={filters.masteryStatus === 'mastered' ? 'primary' : 'tertiary'}
                icon={<IconTickCircle />}
                onClick={() => handleFilterChange('masteryStatus', filters.masteryStatus === 'mastered' ? '' : 'mastered')}
              >
                已掌握
              </Button>
            </Space>
          </div>

          {/* 重置操作 */}
          <div>
            <Text strong style={{ display: 'block', marginBottom: '8px' }}>
              操作
            </Text>
            <Button
              size="small"
              type="tertiary"
              icon={<IconRefresh />}
              onClick={handleResetFilters}
              style={{ width: '100%' }}
            >
              重置筛选
            </Button>
          </div>
        </div>
      </Card>

      {/* 当前筛选状态显示 */}
      <Card>
        <h3>当前筛选状态</h3>
        <Space wrap>
          <div>
            <Text strong>学习模块: </Text>
            <Tag color="blue">
              {filters.moduleType ? MODULE_TYPE_MAP[filters.moduleType as keyof typeof MODULE_TYPE_MAP] : '全部模块'}
            </Tag>
          </div>
          <div>
            <Text strong>掌握状态: </Text>
            <Tag color="green">
              {filters.masteryStatus ? MASTERY_STATUS_MAP[filters.masteryStatus as keyof typeof MASTERY_STATUS_MAP].text : '全部状态'}
            </Tag>
          </div>
        </Space>

        <div style={{ marginTop: '16px', padding: '12px', background: '#f5f5f5', borderRadius: '8px' }}>
          <h4>测试说明：</h4>
          <ul>
            <li>✅ 每个下拉框都有"全部"选项，可以重新选择查看所有数据</li>
            <li>✅ 快速筛选按钮包含"全部"按钮，可以快速重置状态筛选</li>
            <li>✅ "重置筛选"按钮可以一键清除所有筛选条件</li>
            <li>✅ 筛选器之间相互独立，不会互相影响</li>
            <li>✅ 当前筛选状态实时显示，用户可以清楚看到当前的筛选条件</li>
          </ul>
        </div>

        <div style={{ marginTop: '16px' }}>
          <Text type="secondary">
            <strong>筛选器状态（JSON）：</strong>
            <pre style={{ marginTop: '8px', padding: '8px', background: '#f0f0f0', borderRadius: '4px' }}>
              {JSON.stringify(filters, null, 2)}
            </pre>
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default FilterTest;
