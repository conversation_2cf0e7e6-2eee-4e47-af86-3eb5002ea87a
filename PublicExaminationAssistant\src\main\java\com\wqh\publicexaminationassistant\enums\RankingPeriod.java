package com.wqh.publicexaminationassistant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

/**
 * 排行榜时间周期枚举
 * 定义系统支持的各种排行榜统计周期
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Getter
@AllArgsConstructor
public enum RankingPeriod {

    /**
     * 日榜
     */
    DAILY("daily", "日榜", "当日排行榜"),

    /**
     * 周榜
     */
    WEEKLY("weekly", "周榜", "本周排行榜"),

    /**
     * 月榜
     */
    MONTHLY("monthly", "月榜", "本月排行榜"),

    /**
     * 总榜
     */
    ALL_TIME("all_time", "总榜", "历史总排行榜");

    /**
     * 周期代码
     */
    private final String code;

    /**
     * 周期名称
     */
    private final String name;

    /**
     * 周期描述
     */
    private final String description;

    /**
     * 根据代码获取排行榜周期
     *
     * @param code 周期代码
     * @return 排行榜周期枚举，如果不存在则返回null
     */
    public static RankingPeriod fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (RankingPeriod period : values()) {
            if (period.getCode().equals(code.trim())) {
                return period;
            }
        }
        return null;
    }

    /**
     * 验证排行榜周期代码是否有效
     *
     * @param code 周期代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取周期的开始时间
     *
     * @return 开始时间
     */
    public LocalDateTime getStartTime() {
        LocalDate today = LocalDate.now();
        
        switch (this) {
            case DAILY:
                return today.atStartOfDay();
            case WEEKLY:
                // 周一为一周的开始
                return today.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY)).atStartOfDay();
            case MONTHLY:
                // 本月第一天
                return today.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
            case ALL_TIME:
                // 系统开始时间（2024年1月1日）
                return LocalDate.of(2024, 1, 1).atStartOfDay();
            default:
                return today.atStartOfDay();
        }
    }

    /**
     * 获取周期的结束时间
     *
     * @return 结束时间
     */
    public LocalDateTime getEndTime() {
        LocalDate today = LocalDate.now();
        
        switch (this) {
            case DAILY:
                return today.plusDays(1).atStartOfDay().minusNanos(1);
            case WEEKLY:
                // 周日为一周的结束
                return today.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY))
                           .plusDays(1).atStartOfDay().minusNanos(1);
            case MONTHLY:
                // 本月最后一天
                return today.with(TemporalAdjusters.lastDayOfMonth())
                           .plusDays(1).atStartOfDay().minusNanos(1);
            case ALL_TIME:
                // 当前时间
                return LocalDateTime.now();
            default:
                return LocalDateTime.now();
        }
    }

    /**
     * 获取所有排行榜周期代码
     *
     * @return 周期代码数组
     */
    public static String[] getAllCodes() {
        RankingPeriod[] periods = values();
        String[] codes = new String[periods.length];
        for (int i = 0; i < periods.length; i++) {
            codes[i] = periods[i].getCode();
        }
        return codes;
    }
}
