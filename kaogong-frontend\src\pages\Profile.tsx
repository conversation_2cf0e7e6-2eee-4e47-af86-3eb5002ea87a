import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Form,
  Card,
  Button,
  Avatar,
  Upload,
  Modal,
  Toast,
  Row,
  Col,
  Divider,
  Tag,
  Typography,
  Space,
  Progress
} from '@douyinfe/semi-ui';
import { IconCamera, IconEdit, IconLock, IconUser, IconStar, IconCrown, IconArrowRight, IconSave, IconUpload, IconTickCircle, IconAlertTriangle } from '@douyinfe/semi-icons';
import { useAuthStore } from '../stores/useAuthStore';
import { validateImageFile } from '../utils/fileValidation';
import { authService } from '../services/authService';
import Navigation from '../components/Navigation';
import '../styles/profile.css';

interface ProfileFormData {
  nickname: string;
  avatarUrl: string;
  targetPosition: string;
  phone: string;
}

interface PasswordFormData {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// 信誉等级配置
const REPUTATION_LEVELS = {
  newbie: { name: '新手', color: '#87d068', minScore: 0, maxScore: 99 },
  bronze: { name: '青铜', color: '#CD7F32', minScore: 100, maxScore: 299 },
  silver: { name: '白银', color: '#C0C0C0', minScore: 300, maxScore: 599 },
  gold: { name: '黄金', color: '#FFD700', minScore: 600, maxScore: 999 },
  platinum: { name: '铂金', color: '#E5E4E2', minScore: 1000, maxScore: 1999 },
  diamond: { name: '钻石', color: '#B9F2FF', minScore: 2000, maxScore: 4999 },
  master: { name: '大师', color: '#FF6B6B', minScore: 5000, maxScore: 9999 },
  grandmaster: { name: '宗师', color: '#9B59B6', minScore: 10000, maxScore: Infinity }
};

// 获取信誉等级文本
const getReputationLevelText = (level?: string): string => {
  if (!level || !REPUTATION_LEVELS[level as keyof typeof REPUTATION_LEVELS]) {
    return '新手';
  }
  return REPUTATION_LEVELS[level as keyof typeof REPUTATION_LEVELS].name;
};

// 获取信誉等级颜色
const getReputationColor = (level?: string): string => {
  if (!level || !REPUTATION_LEVELS[level as keyof typeof REPUTATION_LEVELS]) {
    return REPUTATION_LEVELS.newbie.color;
  }
  return REPUTATION_LEVELS[level as keyof typeof REPUTATION_LEVELS].color;
};

// 获取下一等级所需分数
const getNextLevelPoints = (currentScore: number): number => {
  const levels = Object.values(REPUTATION_LEVELS);
  for (const level of levels) {
    if (currentScore < level.maxScore) {
      return level.maxScore - currentScore + 1;
    }
  }
  return 0; // 已达到最高等级
};

// 获取当前等级进度百分比
const getReputationProgress = (currentScore: number): number => {
  const levels = Object.values(REPUTATION_LEVELS);
  for (const level of levels) {
    if (currentScore >= level.minScore && currentScore <= level.maxScore) {
      const progress = ((currentScore - level.minScore) / (level.maxScore - level.minScore)) * 100;
      return Math.min(progress, 100);
    }
  }
  return 100; // 已达到最高等级
};

const Profile: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    nickname: '',
    avatarUrl: '',
    targetPosition: '',
    phone: ''
  });
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState('');

  // 获取个人资料
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setLoading(true);
        setDataLoaded(false);
        const profile = await authService.getUserProfile();

        const formData = {
          nickname: profile.nickname || '',
          avatarUrl: profile.avatarUrl || '',
          targetPosition: profile.targetPosition || '',
          phone: profile.phone || ''
        };
        setProfileForm(formData);
        setAvatarUrl(profile.avatarUrl || '');
        setDataLoaded(true);
      } catch (error: any) {
        Toast.error(error.message || '获取个人资料失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // 更新个人资料
  const handleProfileSubmit = async (values: ProfileFormData) => {
    try {
      setLoading(true);
      const updatedUser = await authService.updateProfile({
        nickname: values.nickname,
        avatarUrl: avatarUrl,
        targetPosition: values.targetPosition,
        phone: values.phone
      });
      
      updateUser(updatedUser);
      Toast.success('个人资料更新成功！');
    } catch (error: any) {
      Toast.error(error.message || '更新个人资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 修改密码
  const handlePasswordSubmit = async (values: PasswordFormData) => {
    if (values.newPassword !== values.confirmPassword) {
      Toast.error('两次输入的密码不一致');
      return;
    }

    try {
      setLoading(true);
      await authService.changePassword({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword
      });
      
      Toast.success('密码修改成功！');
      setPasswordModalVisible(false);
    } catch (error: any) {
      Toast.error(error.message || '修改密码失败');
    } finally {
      setLoading(false);
    }
  };

  // 头像文件选择处理
  const handleAvatarFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('选择的文件:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    // 使用文件验证工具
    const validation = validateImageFile(file, 10 * 1024 * 1024); // 10MB

    if (!validation.isValid) {
      Toast.error(validation.errorMessage || '文件验证失败');
      return;
    }

    try {
      setLoading(true);
      Toast.info('正在上传头像...');

      const updatedUser = await authService.uploadAvatar(file);

      // 更新本地状态
      setAvatarUrl(updatedUser.avatarUrl || '');
      updateUser(updatedUser);

      Toast.success('头像上传成功！');
    } catch (error: any) {
      console.error('头像上传失败:', error);
      Toast.error(error.message || '头像上传失败');
    } finally {
      setLoading(false);
      // 清空文件输入，允许重复选择同一文件
      event.target.value = '';
    }
  };

  // 头像悬浮遮罩
  const avatarHoverMask = (
    <div className="avatar-hover-mask">
      <IconCamera style={{ color: 'white', fontSize: 24 }} />
    </div>
  );

  return (
    <>
      <Navigation />
      <div className="profile-container" style={{ paddingTop: '80px' }}>
        {/* 浮动装饰元素 */}
        <div className="floating-emoji profile-emoji-1">👤</div>
        <div className="floating-emoji profile-emoji-2">✏️</div>
        <div className="floating-emoji profile-emoji-3">🔒</div>
        <div className="floating-emoji profile-emoji-4">⚙️</div>

        <div className="profile-content">
        <Row gutter={24}>
          {/* 左侧：头像和基本信息 */}
          <Col span={8}>
            <Card className="profile-avatar-card sketch-card enhanced-avatar-card">
              {/* 装饰性背景元素 */}
              <div className="card-decoration">
                <div className="decoration-circle circle-1"></div>
                <div className="decoration-circle circle-2"></div>
                <div className="decoration-circle circle-3"></div>
              </div>

              {/* 头像上传区域 */}
              <div className="avatar-upload-container">
                <div
                  className="avatar-section enhanced-avatar"
                  onClick={() => {
                    if (!loading) {
                      document.getElementById('avatar-upload-input')?.click();
                    }
                  }}
                  style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
                >
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarFileChange}
                    style={{ display: 'none' }}
                    id="avatar-upload-input"
                    disabled={loading}
                  />

                  {/* 头像外圈装饰 */}
                  <div className="avatar-ring">
                    <div className="avatar-ring-inner">
                      <Avatar
                        src={avatarUrl || user?.avatarUrl}
                        size="extra-large"
                        style={{
                          opacity: loading ? 0.6 : 1,
                          transition: 'all 0.3s ease',
                          border: '4px solid rgba(255, 255, 255, 0.9)',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                        hoverMask={avatarHoverMask}
                        alt="用户头像"
                      >
                        {user?.nickname?.charAt(0) || user?.username?.charAt(0) || 'U'}
                      </Avatar>
                    </div>
                  </div>

                  {/* 上传提示 */}
                  <div className="upload-hint">
                    <IconUpload size="small" />
                    <span>{loading ? '上传中...' : '点击更换头像'}</span>
                  </div>
                </div>
              </div>

              {/* 用户信息区域 */}
              <div className="user-info-section">
                <div className="user-name-container">
                  <h3 className="user-name enhanced-name">
                    {user?.nickname || user?.username}
                    <div className="name-underline"></div>
                  </h3>
                </div>

                <div className="user-badges">
                  <div className="user-role enhanced-role">
                    <IconUser size="small" />
                    {user?.systemRole === 'admin' ? '管理员' : user?.systemRole === 'super_admin' ? '超级管理员' : '普通用户'}
                  </div>

                  {/* 在线状态指示器 */}
                  <div className="online-status">
                    <div className="status-dot"></div>
                    <span>在线</span>
                  </div>
                </div>
              </div>

              {/* 增强的信誉等级信息 */}
              <div className="reputation-section enhanced-reputation">
                {/* 信誉等级标题 */}
                <div className="reputation-header">
                  <div className="reputation-title">
                    <IconStar className="title-icon" />
                    <Typography.Text strong className="title-text">信誉等级</Typography.Text>
                    <div className="title-sparkle">✨</div>
                  </div>
                </div>

                {/* 信誉等级展示 */}
                <div className="reputation-display">
                  <div className="reputation-badge-container">
                    <div className={`reputation-badge enhanced-badge reputation-${user?.reputationLevel || 'newbie'}`}>
                      <IconCrown className="badge-icon" />
                      <span className="badge-text">{getReputationLevelText(user?.reputationLevel)}</span>
                    </div>
                    <div className="reputation-score-display">
                      <span className="score-number">{user?.reputationScore || 0}</span>
                      <span className="score-unit">分</span>
                    </div>
                  </div>
                </div>

                {/* 进度条区域 */}
                <div className="reputation-progress-area">
                  <div className="progress-info">
                    <Typography.Text className="progress-text">
                      距离下一等级还需 <span className="highlight-number">{getNextLevelPoints(user?.reputationScore || 0)}</span> 分
                    </Typography.Text>
                  </div>
                  <div className="progress-container">
                    <Progress
                      percent={getReputationProgress(user?.reputationScore || 0)}
                      showInfo={false}
                      size="small"
                      stroke={getReputationColor(user?.reputationLevel)}
                      className="enhanced-progress"
                    />
                  </div>
                </div>

                {/* 信誉中心按钮 */}
                <div className="reputation-action">
                  <Link to="/reputation" style={{ textDecoration: 'none' }}>
                    <Button
                      type="tertiary"
                      size="small"
                      icon={<IconStar />}
                      iconPosition="left"
                      className="reputation-center-btn"
                    >
                      <span>信誉中心</span>
                      <IconArrowRight className="btn-arrow" />
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </Col>

          {/* 右侧：增强表单区域 */}
          <Col span={16}>
            {/* 个人资料表单 */}
            <Card
              className="profile-form-card enhanced-form-card sketch-card"
              title={
                <div className="enhanced-card-title">
                  <div className="title-icon-wrapper">
                    <IconUser className="title-icon" />
                  </div>
                  <span className="title-text">个人资料</span>
                  <div className="title-decoration">✨</div>
                </div>
              }
            >
              {dataLoaded ? (
                <div className="form-container">
                  <Form
                    key="profile-form"
                    initValues={profileForm}
                    onSubmit={handleProfileSubmit}
                    labelPosition="top"
                    className="enhanced-form"
                  >
                    {({ formState, values, formApi }) => (
                    <>
                      {/* 昵称字段 */}
                      <div className="form-field-group">
                        <Form.Input
                          field="nickname"
                          label={
                            <div className="enhanced-label">
                              <IconEdit className="label-icon" />
                              <span>昵称</span>
                            </div>
                          }
                          placeholder="请输入您的昵称"
                          className="enhanced-input"
                          showClear
                          prefix={<IconEdit size="small" />}
                          rules={[
                            { required: true, message: '请输入昵称' },
                            { max: 20, message: '昵称不能超过20个字符' }
                          ]}
                        />
                
                      </div>

                      {/* 目标岗位字段 */}
                      <div className="form-field-group">
                        <Form.Input
                          field="targetPosition"
                          label={
                            <div className="enhanced-label">
                              <IconStar className="label-icon" />
                              <span>目标岗位</span>
                            </div>
                          }
                          placeholder="请输入您的目标岗位"
                          className="enhanced-input"
                          showClear
                          prefix={<IconStar size="small" />}
                          rules={[
                            { max: 50, message: '目标岗位不能超过50个字符' }
                          ]}
                        />
            
                      </div>

                      {/* 手机号字段 */}
                      <div className="form-field-group">
                        <Form.Input
                          field="phone"
                          label={
                            <div className="enhanced-label">
                              <IconUser className="label-icon" />
                              <span>手机号</span>
                            </div>
                          }
                          placeholder="请输入您的手机号"
                          className="enhanced-input"
                          showClear
                          prefix={<IconUser size="small" />}
                          rules={[
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
                          ]}
                        />
               
                      </div>

                      {/* 表单操作按钮 */}
                      <div className="enhanced-form-actions">
                        <Button
                          type="primary"
                          htmlType="submit"
                          loading={loading}
                          className="enhanced-save-btn"
                          size="large"
                          disabled={loading}
                        >
                          <div className="btn-content">
                            <IconSave className="btn-icon" />
                            <span className="btn-text">
                              {loading ? '保存中...' : '保存资料'}
                            </span>
                            <div className="btn-shine"></div>
                          </div>
                        </Button>

                        {/* 表单状态指示器 */}
                        <div className="form-status">
                          {values.nickname ? (
                            <div className="status-success">
                              <IconTickCircle className="status-icon" />
                              <span>表单填写完整</span>
                            </div>
                          ) : (
                            <div className="status-warning">
                              <IconAlertTriangle className="status-icon" />
                              <span>请完善必填信息</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                    )}
                  </Form>
                </div>
              ) : (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <div className="loading-text">正在加载个人资料...</div>
                </div>
              )}
            </Card>

            <Divider className="enhanced-divider" style={{ margin: '20px 0' }} />

            {/* 账户安全设置 */}
            <Card
              className="profile-security-card enhanced-security-card sketch-card"
              title={
                <div className="enhanced-card-title">
                  <div className="title-icon-wrapper security-icon">
                    <IconLock className="title-icon" />
                  </div>
                  <span className="title-text">账户安全</span>
                  <div className="security-badge">🔒</div>
                </div>
              }
            >
              <div className="enhanced-security-section">
                {/* 安全状态概览 */}
                <div className="security-overview">
                  <div className="security-item">
                    <div className="security-item-icon">
                      <IconLock />
                    </div>
                    <div className="security-item-content">
                      <h4 className="security-item-title">密码安全</h4>
                      <p className="security-item-desc">定期修改密码，保护账户安全</p>
                    </div>
                    <div className="security-item-action">
                      <Button
                        icon={<IconEdit />}
                        onClick={() => setPasswordModalVisible(true)}
                        className="enhanced-security-btn"
                        theme="light"
                        size="large"
                      >
                        <span>修改密码</span>
                        <IconArrowRight className="btn-arrow" />
                      </Button>
                    </div>
                  </div>

                  {/* 安全提示 */}
                  <div className="security-tips-card">
                    <div className="tips-header">
                      <IconAlertTriangle className="tips-icon" />
                      <span className="tips-title">安全建议</span>
                    </div>
                    <ul className="tips-list">
                      <li className="tip-item">
                        <IconTickCircle className="tip-check" />
                        <span>使用强密码，包含大小写字母、数字和特殊字符</span>
                      </li>
                      <li className="tip-item">
                        <IconTickCircle className="tip-check" />
                        <span>定期更换密码，建议每3-6个月更换一次</span>
                      </li>
                      <li className="tip-item">
                        <IconTickCircle className="tip-check" />
                        <span>不要在多个网站使用相同密码</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
        </div>
      </div>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        visible={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form onSubmit={handlePasswordSubmit}>
          {({ formState, values, formApi }) => (
            <>
              <Form.Input
                field="oldPassword"
                label="当前密码"
                type="password"
                placeholder="请输入当前密码"
                style={{ width: '100%' }}
                rules={[
                  { required: true, message: '请输入当前密码' }
                ]}
              />
              
              <Form.Input
                field="newPassword"
                label="新密码"
                type="password"
                placeholder="请输入新密码"
                style={{ width: '100%' }}
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码至少6位' },
                  { max: 20, message: '密码不能超过20位' }
                ]}
              />
              
              <Form.Input
                field="confirmPassword"
                label="确认密码"
                type="password"
                placeholder="请再次输入新密码"
                style={{ width: '100%' }}
                rules={[
                  { required: true, message: '请确认新密码' },
                  { 
                    validator: (rule, value) => {
                      if (value && value !== values.newPassword) {
                        return Promise.reject('两次输入的密码不一致');
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              />

              <div className="modal-buttons">
                <Button 
                  onClick={() => setPasswordModalVisible(false)}
                  style={{ marginRight: 12 }}
                >
                  取消
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={loading}
                >
                  确认修改
                </Button>
              </div>
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};

export default Profile;
