import { create } from 'zustand';
import { 
  UserReputationStats, 
  ReputationLog, 
  ProtectionInfo, 
  ReputationLevel 
} from '../types/reputation';
import reputationService from '../services/reputationService';

interface ReputationState {
  // 状态数据
  userStats: UserReputationStats | null;
  reputationLogs: ReputationLog[];
  protectionInfo: ProtectionInfo | null;
  reputationLevels: ReputationLevel[];
  
  // 加载状态
  isLoading: boolean;
  isLoadingLogs: boolean;
  error: string | null;
  
  // 操作方法
  fetchUserStats: () => Promise<void>;
  fetchReputationLogs: (limit?: number) => Promise<void>;
  fetchReputationLogsByCategory: (category: string, limit?: number) => Promise<void>;
  fetchProtectionStatus: () => Promise<void>;
  fetchReputationLevels: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useReputationStore = create<ReputationState>((set, get) => ({
  // 初始状态
  userStats: null,
  reputationLogs: [],
  protectionInfo: null,
  reputationLevels: [],
  isLoading: false,
  isLoadingLogs: false,
  error: null,

  // 获取用户信誉统计
  fetchUserStats: async () => {
    set({ isLoading: true, error: null });
    try {
      const userStats = await reputationService.getUserReputationStats();
      set({ userStats, isLoading: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '获取用户信誉统计失败',
        isLoading: false 
      });
    }
  },

  // 获取信誉记录
  fetchReputationLogs: async (limit = 20) => {
    set({ isLoadingLogs: true, error: null });
    try {
      const reputationLogs = await reputationService.getReputationLogs(limit);
      set({ reputationLogs, isLoadingLogs: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '获取信誉记录失败',
        isLoadingLogs: false 
      });
    }
  },

  // 获取指定分类的信誉记录
  fetchReputationLogsByCategory: async (category: string, limit = 10) => {
    set({ isLoadingLogs: true, error: null });
    try {
      const reputationLogs = await reputationService.getReputationLogsByCategory(category, limit);
      set({ reputationLogs, isLoadingLogs: false });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '获取分类信誉记录失败',
        isLoadingLogs: false 
      });
    }
  },

  // 获取保护期状态
  fetchProtectionStatus: async () => {
    try {
      const protectionInfo = await reputationService.getProtectionStatus();
      set({ protectionInfo });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '获取保护期状态失败'
      });
    }
  },

  // 获取信誉等级配置
  fetchReputationLevels: async () => {
    try {
      const reputationLevels = await reputationService.getReputationLevels();
      set({ reputationLevels });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '获取信誉等级配置失败'
      });
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 重置状态
  reset: () => {
    set({
      userStats: null,
      reputationLogs: [],
      protectionInfo: null,
      reputationLevels: [],
      isLoading: false,
      isLoadingLogs: false,
      error: null
    });
  }
}));
