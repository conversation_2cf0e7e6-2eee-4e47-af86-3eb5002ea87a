/* 图片上传组件样式 */
.image-upload-container {
  width: 100%;
}

.upload-area {
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: var(--semi-color-primary) !important;
  background-color: var(--semi-color-fill-0);
}

.upload-area.drag-over {
  border-color: var(--semi-color-primary) !important;
  background-color: var(--semi-color-primary-light-default);
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-area.disabled:hover {
  border-color: var(--semi-color-border) !important;
  background-color: transparent !important;
}

/* 图片预览样式 */
.image-preview-container {
  width: 100%;
}

.image-preview-empty {
  text-align: center;
  padding: 20px;
  color: var(--semi-color-text-2);
}

/* 图片卡片悬浮效果 */
.image-preview-container .semi-card {
  transition: all 0.3s ease;
}

.image-preview-container .semi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片覆盖层动画 */
.image-overlay {
  transition: opacity 0.3s ease !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-upload-container .upload-area {
    padding: 16px;
  }
  
  .image-preview-container {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
    gap: 8px !important;
  }
  
  .image-preview-container .semi-card {
    padding: 4px;
  }
}

@media (max-width: 480px) {
  .image-upload-container .upload-area {
    padding: 12px;
  }
  
  .image-preview-container {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* 图片加载动画 */
.image-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--semi-color-fill-1);
  border-radius: 4px;
}

/* 图片错误状态 */
.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--semi-color-danger-light-default);
  color: var(--semi-color-danger);
  border-radius: 4px;
  font-size: 12px;
}

/* 上传进度条样式 */
.upload-progress {
  margin-top: 4px;
}

/* 图片预览模态框样式 */
.image-preview-modal .semi-modal-content {
  max-height: 90vh;
  overflow-y: auto;
}

.image-preview-modal .semi-modal-body {
  padding: 20px;
}

/* 图片导航按钮 */
.image-nav-button {
  min-width: 100px;
}

/* 大图显示容器 */
.large-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 70vh;
  overflow: hidden;
  border-radius: 8px;
  background-color: var(--semi-color-fill-0);
}

.large-image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

/* 图片信息显示 */
.image-info {
  text-align: center;
  margin-top: 16px;
  padding: 12px;
  background-color: var(--semi-color-fill-0);
  border-radius: 6px;
}

/* 图片类型标签 */
.image-type-label {
  display: inline-block;
  padding: 2px 8px;
  background-color: var(--semi-color-primary-light-default);
  color: var(--semi-color-primary);
  border-radius: 12px;
  font-size: 12px;
  margin-bottom: 8px;
}

/* 拖拽排序样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 删除按钮悬浮效果 */
.delete-button {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview-container .semi-card:hover .delete-button {
  opacity: 1;
}

/* 图片数量提示 */
.image-count-tip {
  font-size: 12px;
  color: var(--semi-color-text-2);
  margin-top: 4px;
}

/* 上传区域图标动画 */
.upload-icon {
  transition: transform 0.3s ease;
}

.upload-area:hover .upload-icon {
  transform: translateY(-2px);
}

/* 图片缩略图样式 */
.image-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  transition: transform 0.3s ease;
}

.image-thumbnail:hover {
  transform: scale(1.05);
}

/* 批量操作按钮 */
.batch-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding: 8px;
  background-color: var(--semi-color-fill-0);
  border-radius: 6px;
}

/* 图片上传状态指示器 */
.upload-status-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
}

.upload-status-indicator.success {
  background-color: var(--semi-color-success);
}

.upload-status-indicator.error {
  background-color: var(--semi-color-danger);
}

.upload-status-indicator.uploading {
  background-color: var(--semi-color-warning);
}

/* 图片预览网格布局优化 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.image-grid-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--semi-color-fill-1);
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .upload-area {
    border-color: var(--semi-color-border);
  }
  
  .image-info {
    background-color: var(--semi-color-fill-1);
  }
  
  .large-image-container {
    background-color: var(--semi-color-fill-1);
  }
}
