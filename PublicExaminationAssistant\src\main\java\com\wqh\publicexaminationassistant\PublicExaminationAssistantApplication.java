package com.wqh.publicexaminationassistant;

import com.wqh.publicexaminationassistant.config.FileUploadProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableConfigurationProperties({FileUploadProperties.class})
@EnableScheduling
public class PublicExaminationAssistantApplication {

    public static void main(String[] args) {

        SpringApplication.run(PublicExaminationAssistantApplication.class, args);
        System.out.println("Swagger UI at:http://localhost:8080/api/swagger-ui.html");
    }

}
