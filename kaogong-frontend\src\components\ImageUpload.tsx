import React, { useState, useRef, useCallback } from 'react';
import { 
  Upload, 
  Button, 
  Progress, 
  Toast, 
  Space, 
  Typography,
  Card,
  Spin
} from '@douyinfe/semi-ui';
import { IconUpload, IconImage, IconDelete } from '@douyinfe/semi-icons';
import { imageService } from '../services/studyService';

const { Text } = Typography;

export interface ImageInfo {
  id?: string;
  file?: File;
  url?: string;
  name: string;
  size: number;
  status: 'uploading' | 'success' | 'error' | 'pending';
  progress?: number;
  errorMessage?: string;
}

interface ImageUploadProps {
  imageType: 'question' | 'answer' | 'explanation';
  wrongQuestionId?: string;
  maxCount?: number;
  value?: ImageInfo[];
  onChange?: (images: ImageInfo[]) => void;
  disabled?: boolean;
  className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  imageType,
  wrongQuestionId,
  maxCount = 5,
  value = [],
  onChange,
  disabled = false,
  className = ''
}) => {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 图片类型映射
  const imageTypeMap = {
    question: '题目图片',
    answer: '答案图片',
    explanation: '解析图片'
  };

  // 文件验证
  const validateFile = (file: File): string | null => {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return '只支持 JPG、PNG、GIF、WebP 格式的图片';
    }

    // 检查文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      return '图片大小不能超过 10MB';
    }

    return null;
  };

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const currentCount = value.length;
    
    if (currentCount + fileArray.length > maxCount) {
      Toast.warning(`最多只能上传 ${maxCount} 张图片`);
      return;
    }

    const newImages: ImageInfo[] = [];
    
    fileArray.forEach((file, index) => {
      const error = validateFile(file);
      if (error) {
        Toast.error(`${file.name}: ${error}`);
        return;
      }

      const imageInfo: ImageInfo = {
        file,
        url: URL.createObjectURL(file),
        name: file.name,
        size: file.size,
        status: wrongQuestionId ? 'pending' : 'success', // 如果有wrongQuestionId则需要上传，否则只是预览
        progress: 0
      };

      newImages.push(imageInfo);
    });

    if (newImages.length > 0) {
      const updatedImages = [...value, ...newImages];
      onChange?.(updatedImages);

      // 如果有wrongQuestionId，立即上传
      if (wrongQuestionId) {
        newImages.forEach((imageInfo, index) => {
          uploadImage(imageInfo, updatedImages.length - newImages.length + index);
        });
      }
    }
  }, [value, maxCount, onChange, wrongQuestionId]);

  // 上传图片到服务器
  const uploadImage = async (imageInfo: ImageInfo, index: number) => {
    if (!imageInfo.file || !wrongQuestionId) return;

    try {
      // 更新状态为上传中
      updateImageStatus(index, { status: 'uploading', progress: 0 });

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        updateImageStatus(index, (prev) => ({
          progress: Math.min((prev.progress || 0) + 10, 90)
        }));
      }, 100);

      // 调用上传API
      const response = await imageService.uploadWrongQuestionImage(
        wrongQuestionId,
        imageInfo.file,
        imageType,
        imageInfo.sortOrder || index
      );

      clearInterval(progressInterval);

      // 上传成功
      updateImageStatus(index, {
        id: response.id,
        status: 'success',
        progress: 100,
        url: response.fileUrl
      });

      Toast.success('图片上传成功');

    } catch (error: any) {
      // 上传失败
      updateImageStatus(index, {
        status: 'error',
        progress: 0,
        errorMessage: error.message || '上传失败'
      });

      Toast.error(`图片上传失败: ${error.message || '未知错误'}`);
    }
  };

  // 更新图片状态
  const updateImageStatus = (index: number, updates: Partial<ImageInfo> | ((prev: ImageInfo) => Partial<ImageInfo>)) => {
    const newImages = [...value];
    if (newImages[index]) {
      const updateObj = typeof updates === 'function' ? updates(newImages[index]) : updates;
      newImages[index] = { ...newImages[index], ...updateObj };
      onChange?.(newImages);
    }
  };

  // 删除图片
  const handleRemoveImage = async (index: number) => {
    const imageInfo = value[index];
    
    try {
      // 如果图片已上传到服务器，需要调用删除API
      if (imageInfo.id && wrongQuestionId) {
        await imageService.deleteImage(imageInfo.id);
        Toast.success('图片删除成功');
      }

      // 从列表中移除
      const newImages = value.filter((_, i) => i !== index);
      onChange?.(newImages);

      // 清理本地URL
      if (imageInfo.url && imageInfo.url.startsWith('blob:')) {
        URL.revokeObjectURL(imageInfo.url);
      }

    } catch (error: any) {
      Toast.error(`删除失败: ${error.message || '未知错误'}`);
    }
  };

  // 拖拽处理
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  // 点击上传
  const handleClickUpload = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  };

  return (
    <div className={`image-upload-container ${className}`}>
      {/* 上传区域 */}
      <div
        className={`upload-area ${dragOver ? 'drag-over' : ''} ${disabled ? 'disabled' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClickUpload}
        style={{
          border: '2px dashed var(--semi-color-border)',
          borderRadius: '8px',
          padding: '24px',
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          backgroundColor: dragOver ? 'var(--semi-color-fill-0)' : 'transparent',
          borderColor: dragOver ? 'var(--semi-color-primary)' : 'var(--semi-color-border)',
          transition: 'all 0.3s ease'
        }}
      >
        <IconUpload size="large" style={{ color: 'var(--semi-color-text-2)' }} />
        <div style={{ marginTop: '8px' }}>
          <Text type="secondary">
            点击或拖拽{imageTypeMap[imageType]}到此区域上传
          </Text>
        </div>
        <div style={{ marginTop: '4px' }}>
          <Text size="small" type="tertiary">
            支持 JPG、PNG、GIF、WebP 格式，单个文件不超过 10MB
          </Text>
        </div>
        <div style={{ marginTop: '4px' }}>
          <Text size="small" type="tertiary">
            最多上传 {maxCount} 张图片 ({value.length}/{maxCount})
          </Text>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        style={{ display: 'none' }}
        onChange={handleFileInputChange}
        disabled={disabled}
      />

      {/* 图片预览列表 */}
      {value.length > 0 && (
        <div style={{ marginTop: '16px' }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
            gap: '12px'
          }}>
            {value.map((imageInfo, index) => (
              <Card
                key={index}
                style={{ 
                  position: 'relative',
                  padding: '8px',
                  border: '1px solid var(--semi-color-border)'
                }}
                bodyStyle={{ padding: 0 }}
              >
                {/* 图片预览 */}
                <div style={{ 
                  width: '100%', 
                  height: '80px', 
                  overflow: 'hidden',
                  borderRadius: '4px',
                  position: 'relative'
                }}>
                  {imageInfo.url && (
                    <img
                      src={imageInfo.url}
                      alt={imageInfo.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                    />
                  )}
                  
                  {/* 上传状态覆盖层 */}
                  {imageInfo.status === 'uploading' && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(0, 0, 0, 0.6)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white'
                    }}>
                      <Spin />
                    </div>
                  )}

                  {/* 错误状态覆盖层 */}
                  {imageInfo.status === 'error' && (
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(255, 0, 0, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <Text type="danger" size="small">上传失败</Text>
                    </div>
                  )}
                </div>

                {/* 文件信息 */}
                <div style={{ marginTop: '4px' }}>
                  <Text size="small" ellipsis={{ showTooltip: true }}>
                    {imageInfo.name}
                  </Text>
                </div>

                {/* 上传进度 */}
                {imageInfo.status === 'uploading' && (
                  <Progress
                    percent={imageInfo.progress || 0}
                    size="small"
                    style={{ marginTop: '4px' }}
                  />
                )}

                {/* 删除按钮 */}
                <Button
                  icon={<IconDelete />}
                  type="danger"
                  theme="borderless"
                  size="small"
                  style={{
                    position: 'absolute',
                    top: '4px',
                    right: '4px',
                    width: '24px',
                    height: '24px',
                    minWidth: '24px'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveImage(index);
                  }}
                />
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
