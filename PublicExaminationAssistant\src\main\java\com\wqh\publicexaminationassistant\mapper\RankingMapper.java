package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.entity.Ranking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 排行榜表Mapper接口
 * 提供排行榜数据访问操作
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface RankingMapper extends BaseMapper<Ranking> {

    /**
     * 获取全站排行榜数据（分页）
     *
     * @param page 分页对象
     * @param rankingType 排行榜类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param currentUserId 当前用户ID（用于标识）
     * @return 排行榜数据
     */
    Page<Map<String, Object>> getGlobalRanking(
            Page<Map<String, Object>> page,
            @Param("rankingType") String rankingType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("currentUserId") String currentUserId
    );

    /**
     * 获取用户在特定排行榜中的排名信息
     *
     * @param userId 用户ID
     * @param rankingType 排行榜类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户排名信息
     */
    Map<String, Object> getUserRank(
            @Param("userId") String userId,
            @Param("rankingType") String rankingType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 获取排行榜统计信息
     *
     * @param rankingType 排行榜类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getRankingStatistics(
            @Param("rankingType") String rankingType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 获取分数分布数据
     *
     * @param rankingType 排行榜类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分数分布列表
     */
    List<Map<String, Object>> getScoreDistribution(
            @Param("rankingType") String rankingType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 批量插入或更新排行榜记录
     *
     * @param rankings 排行榜记录列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("rankings") List<Ranking> rankings);

    /**
     * 删除指定时间之前的排行榜记录（用于清理历史数据）
     *
     * @param beforeTime 时间点
     * @param rankingType 排行榜类型
     * @return 删除行数
     */
    int deleteOldRankings(
            @Param("beforeTime") LocalDateTime beforeTime,
            @Param("rankingType") String rankingType
    );

    /**
     * 删除指定类型和时间范围的排行榜记录
     *
     * @param rankingType 排行榜类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 删除行数
     */
    int deleteByTypeAndPeriod(
            @Param("rankingType") String rankingType,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 获取用户排名变化（相比上一周期）
     *
     * @param userId 用户ID
     * @param rankingType 排行榜类型
     * @param currentStartTime 当前周期开始时间
     * @param previousStartTime 上一周期开始时间
     * @param previousEndTime 上一周期结束时间
     * @return 排名变化信息
     */
    Map<String, Object> getUserRankChange(
            @Param("userId") String userId,
            @Param("rankingType") String rankingType,
            @Param("currentStartTime") LocalDateTime currentStartTime,
            @Param("previousStartTime") LocalDateTime previousStartTime,
            @Param("previousEndTime") LocalDateTime previousEndTime
    );
}
