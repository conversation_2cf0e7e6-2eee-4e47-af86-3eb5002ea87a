package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 小桌成员表实体类
 * 管理小桌成员关系和状态
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("desk_members")
public class DeskMember implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成员关系ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 小桌ID
     */
    @TableField("desk_id")
    private String deskId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色(owner/member)
     */
    @TableField("role")
    private String role;

    /**
     * 成员状态
     */
    @TableField("status")
    private String status;

    /**
     * 加入理由
     */
    @TableField("join_reason")
    private String joinReason;

    /**
     * 加入时间
     */
    @TableField(value = "joined_at", fill = FieldFill.INSERT)
    private LocalDateTime joinedAt;

    /**
     * 最后活跃时间
     */
    @TableField(value = "last_active_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastActiveAt;

    /**
     * 默认构造函数
     */
    public DeskMember() {
        this.role = "member";
        this.status = "active";
    }

    /**
     * 构造函数
     */
    public DeskMember(String deskId, String userId, String joinReason) {
        this();
        this.deskId = deskId;
        this.userId = userId;
        this.joinReason = joinReason;
    }

    /**
     * 检查是否为桌长
     */
    public boolean isOwner() {
        return "owner".equals(this.role);
    }

    /**
     * 检查是否为普通成员
     */
    public boolean isMember() {
        return "member".equals(this.role);
    }

    /**
     * 检查成员是否活跃
     */
    public boolean isActive() {
        return "active".equals(this.status);
    }

    /**
     * 设置为桌长
     */
    public void setAsOwner() {
        this.role = "owner";
    }

    /**
     * 设置为普通成员
     */
    public void setAsMember() {
        this.role = "member";
    }

    /**
     * 停用成员
     */
    public void deactivate() {
        this.status = "inactive";
    }

    /**
     * 激活成员
     */
    public void activate() {
        this.status = "active";
    }
}
