package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户个人排名响应DTO
 * 用于返回用户在特定排行榜中的个人排名信息
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
public class UserRankResponse {

    /**
     * 当前排名
     */
    private Integer currentRank;

    /**
     * 总参与人数
     */
    private Integer totalParticipants;

    /**
     * 用户分数
     */
    private Integer score;

    /**
     * 排名变化（相比上一周期）
     * 正数表示上升，负数表示下降，0表示无变化，null表示新上榜
     */
    private Integer rankChange;

    /**
     * 百分位数（用户超过了多少百分比的用户）
     */
    private Double percentile;

    /**
     * 排行榜类型
     */
    private String rankingType;

    /**
     * 统计周期
     */
    private String period;

    /**
     * 计算时间
     */
    private LocalDateTime calculatedAt;

    /**
     * 距离上一名的分数差距
     */
    private Integer scoreGapToNext;

    /**
     * 距离下一名的分数差距
     */
    private Integer scoreGapToPrevious;

    /**
     * 用户详细统计信息
     */
    private UserDetailStats detailStats;

    /**
     * 用户详细统计信息内部类
     */
    @Data
    public static class UserDetailStats {
        /**
         * 题目总数
         */
        private Integer totalQuestions;

        /**
         * 正确题目数
         */
        private Integer correctQuestions;

        /**
         * 正确率（百分比）
         */
        private Double accuracyRate;

        /**
         * 学习时长（分钟）
         */
        private Integer studyTime;

        /**
         * 学习天数
         */
        private Integer studyDays;

        /**
         * 连续学习天数
         */
        private Integer consecutiveStudyDays;

        /**
         * 信誉分数
         */
        private Integer reputationScore;

        /**
         * 综合得分（仅综合排名时有值）
         */
        private Double comprehensiveScore;
    }

    /**
     * 排名等级描述
     */
    private String rankLevel;

    /**
     * 获取排名等级描述
     */
    public String getRankLevel() {
        if (currentRank == null || totalParticipants == null || totalParticipants == 0) {
            return "未上榜";
        }

        double percentage = (double) currentRank / totalParticipants * 100;

        if (percentage <= 1) {
            return "王者";
        } else if (percentage <= 5) {
            return "大师";
        } else if (percentage <= 10) {
            return "钻石";
        } else if (percentage <= 25) {
            return "铂金";
        } else if (percentage <= 50) {
            return "黄金";
        } else if (percentage <= 75) {
            return "白银";
        } else {
            return "青铜";
        }
    }
}
