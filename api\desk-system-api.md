# 小桌系统模块 API 接口文档 🌟

## 模块概述
小桌系统是本系统的核心创新功能，实现6-8人小组排行榜机制，通过桌长审核、成员互动、小桌排行等功能增强用户参与感和学习动力。

## 接口列表

| 功能模块 | 接口名称 | 接口地址 | 请求方式 | 入参 | 返回值 | 权限要求 | 备注 |
|---------|---------|---------|---------|------|--------|---------|------|
| 小桌管理 | 创建小桌 | `/api/v1/desks` | POST | `{"name": "学霸小桌", "description": "专注行测数学", "maxMembers": 8, "autoApproveRules": {"minReputationScore": 200, "autoApprove": false}}` | `{"code": 200, "message": "小桌创建成功", "data": {"id": "uuid", "name": "学霸小桌"}}` | user | 创建者自动成为桌长 |
| 小桌管理 | 获取小桌列表 | `/api/v1/desks` | GET | `page=1&size=20&keyword=string&status=active&sortBy=createdAt&sortOrder=desc` | `{"code": 200, "data": {"records": [...], "total": 50}}` | user | 公开小桌列表 |
| 小桌管理 | 获取小桌详情 | `/api/v1/desks/{deskId}` | GET | 路径参数: `deskId` | `{"code": 200, "data": {"id": "uuid", "name": "学霸小桌", "description": "专注行测数学", "ownerId": "uuid", "ownerInfo": {...}, "maxMembers": 8, "currentMembers": 5, "status": "active", "createdAt": "datetime", "memberList": [...]}}` | user | 包含成员基本信息 |
| 小桌管理 | 更新小桌信息 | `/api/v1/desks/{deskId}` | PUT | `{"name": "string?", "description": "string?", "maxMembers": 8, "autoApproveRules": {...}}` | `{"code": 200, "message": "更新成功"}` | owner | 只有桌长可操作 |
| 小桌管理 | 解散小桌 | `/api/v1/desks/{deskId}` | DELETE | 路径参数: `deskId` | `{"code": 200, "message": "小桌已解散"}` | owner | 只有桌长可操作，软删除 |
| 小桌管理 | 搜索小桌 | `/api/v1/desks/search` | GET | `keyword=string&hasVacancy=boolean&minReputationScore=number` | `{"code": 200, "data": {"records": [...], "total": 20}}` | user | 支持多条件搜索 |
| 成员管理 | 申请加入小桌 | `/api/v1/desks/{deskId}/apply` | POST | `{"reason": "申请理由", "studyPlan": "我的学习计划"}` | `{"code": 200, "message": "申请已提交", "data": {"applicationId": "uuid"}}` | user | 不能重复申请同一小桌 |
| 成员管理 | 获取小桌成员列表 | `/api/v1/desks/{deskId}/members` | GET | `page=1&size=20&role=member&status=active&sortBy=joinedAt` | `{"code": 200, "data": {"records": [...], "total": 8}}` | member | 只有成员可查看 |
| 成员管理 | 获取成员详情 | `/api/v1/desks/{deskId}/members/{userId}` | GET | 路径参数: `deskId, userId` | `{"code": 200, "data": {"userId": "uuid", "userInfo": {...}, "role": "member", "status": "active", "joinReason": "申请理由", "joinedAt": "datetime", "lastActiveAt": "datetime"}}` | member | 成员详细信息 |
| 成员管理 | 退出小桌 | `/api/v1/desks/{deskId}/leave` | POST | `{"reason": "退出理由?"}` | `{"code": 200, "message": "已退出小桌"}` | member | 桌长不能退出，需转让 |
| 成员管理 | 移除成员 | `/api/v1/desks/{deskId}/members/{userId}` | DELETE | 路径参数: `deskId, userId`, Body: `{"reason": "移除理由"}` | `{"code": 200, "message": "成员已移除"}` | owner | 只有桌长可操作 |
| 成员管理 | 转让桌长 | `/api/v1/desks/{deskId}/transfer` | POST | `{"newOwnerId": "uuid", "reason": "转让理由"}` | `{"code": 200, "message": "桌长转让成功"}` | owner | 新桌长必须是当前成员 |
| 申请审核 | 获取申请列表 | `/api/v1/desks/{deskId}/applications` | GET | `page=1&size=20&status=pending&sortBy=appliedAt` | `{"code": 200, "data": {"records": [...], "total": 10}}` | owner | 只有桌长可查看 |
| 申请审核 | 获取申请详情 | `/api/v1/desks/{deskId}/applications/{applicationId}` | GET | 路径参数: `deskId, applicationId` | `{"code": 200, "data": {"id": "uuid", "applicantInfo": {...}, "reason": "申请理由", "studyPlan": "学习计划", "status": "pending", "appliedAt": "datetime"}}` | owner | 申请详细信息 |
| 申请审核 | 审核申请 | `/api/v1/desks/{deskId}/applications/{applicationId}/review` | POST | `{"action": "approve|reject", "reason": "审核理由?"}` | `{"code": 200, "message": "审核完成"}` | owner | 48小时内处理，超时自动通过 |
| 申请审核 | 批量审核申请 | `/api/v1/desks/{deskId}/applications/batch-review` | POST | `{"applications": [{"id": "uuid", "action": "approve|reject", "reason": "理由"}]}` | `{"code": 200, "message": "批量审核完成", "data": {"approved": 3, "rejected": 1}}` | owner | 批量处理申请 |
| 申请审核 | 获取我的申请 | `/api/v1/my-applications` | GET | `page=1&size=20&status=pending&sortBy=appliedAt` | `{"code": 200, "data": {"records": [...], "total": 5}}` | user | 查看自己的申请状态 |
| 申请审核 | 撤销申请 | `/api/v1/desks/{deskId}/applications/{applicationId}/cancel` | POST | `{}` | `{"code": 200, "message": "申请已撤销"}` | user | 只能撤销pending状态申请 |
| 小桌排行 | 获取小桌排行榜 | `/api/v1/desks/{deskId}/rankings` | GET | `type=daily|weekly|monthly&limit=10` | `{"code": 200, "data": [{"rank": 1, "userId": "uuid", "userInfo": {...}, "score": 850, "change": "+2"}]}` | member | 多维度排行榜 |
| 小桌排行 | 获取个人排名 | `/api/v1/desks/{deskId}/rankings/my-rank` | GET | `type=daily|weekly|monthly` | `{"code": 200, "data": {"rank": 3, "score": 750, "change": "-1", "totalMembers": 8}}` | member | 个人在小桌中排名 |
| 小桌排行 | 获取排行历史 | `/api/v1/desks/{deskId}/rankings/history` | GET | `userId=uuid&type=weekly&limit=10` | `{"code": 200, "data": [{"period": "2024-W03", "rank": 2, "score": 800}]}` | member | 排名变化历史 |
| 小桌互动 | 发布小桌动态 | `/api/v1/desks/{deskId}/posts` | POST | `{"content": "今天刷了100道题", "type": "study_share", "attachments": ["image_url"]}` | `{"code": 200, "message": "动态发布成功", "data": {"id": "uuid"}}` | member | 小桌内分享学习动态 |
| 小桌互动 | 获取小桌动态 | `/api/v1/desks/{deskId}/posts` | GET | `page=1&size=20&type=study_share&sortBy=createdAt` | `{"code": 200, "data": {"records": [...], "total": 50}}` | member | 小桌动态列表 |
| 小桌互动 | 点赞动态 | `/api/v1/desks/{deskId}/posts/{postId}/like` | POST | `{}` | `{"code": 200, "message": "点赞成功"}` | member | 重复点赞取消 |
| 小桌互动 | 评论动态 | `/api/v1/desks/{deskId}/posts/{postId}/comments` | POST | `{"content": "评论内容"}` | `{"code": 200, "message": "评论成功", "data": {"id": "uuid"}}` | member | 动态评论功能 |
| 活跃度管理 | 获取成员活跃度 | `/api/v1/desks/{deskId}/activity` | GET | `period=week|month&sortBy=score` | `{"code": 200, "data": [{"userId": "uuid", "userInfo": {...}, "activityScore": 85, "lastActiveAt": "datetime", "studyDays": 15}]}` | owner | 桌长查看成员活跃度 |
| 活跃度管理 | 设置活跃度要求 | `/api/v1/desks/{deskId}/activity-rules` | PUT | `{"minActivityScore": 60, "checkPeriod": "week", "warningThreshold": 40, "autoRemove": false}` | `{"code": 200, "message": "活跃度要求已更新"}` | owner | 设置小桌活跃度规则 |
| 活跃度管理 | 获取活跃度统计 | `/api/v1/desks/{deskId}/activity/stats` | GET | `period=month` | `{"code": 200, "data": {"averageScore": 75, "activeMembers": 6, "inactiveMembers": 2, "trend": "increasing"}}` | owner | 小桌整体活跃度统计 |

## 数据模型

### 小桌模型 (Desk)
```json
{
  "id": "uuid",
  "name": "学霸小桌",
  "description": "专注行测数学",
  "ownerId": "uuid",
  "ownerInfo": {
    "username": "string",
    "nickname": "string",
    "reputationLevel": "expert"
  },
  "maxMembers": 8,
  "currentMembers": 5,
  "autoApproveRules": {
    "minReputationScore": 200,
    "autoApprove": false
  },
  "status": "active|inactive",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

### 小桌成员模型 (DeskMember)
```json
{
  "id": "uuid",
  "deskId": "uuid",
  "userId": "uuid",
  "userInfo": {
    "username": "string",
    "nickname": "string",
    "avatarUrl": "string",
    "reputationScore": 350,
    "reputationLevel": "expert"
  },
  "role": "owner|member",
  "status": "active|inactive",
  "joinReason": "申请理由",
  "joinedAt": "datetime",
  "lastActiveAt": "datetime"
}
```
