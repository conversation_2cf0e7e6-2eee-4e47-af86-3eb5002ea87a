/* 创建刷题记录页面样式 */

.create-study-record-container {
  background: var(--paper-bg);
  min-height: 100vh;
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 浮动装饰元素 */
.record-emoji-1 {
  position: fixed;
  top: 15%;
  left: 5%;
  font-size: 2rem;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
  z-index: 1;
}

.record-emoji-2 {
  position: fixed;
  top: 25%;
  right: 8%;
  font-size: 1.8rem;
  opacity: 0.5;
  animation: float 8s ease-in-out infinite reverse;
  z-index: 1;
}

.record-emoji-3 {
  position: fixed;
  bottom: 30%;
  left: 3%;
  font-size: 2.2rem;
  opacity: 0.4;
  animation: float 7s ease-in-out infinite;
  z-index: 1;
}

.record-emoji-4 {
  position: fixed;
  bottom: 20%;
  right: 5%;
  font-size: 1.9rem;
  opacity: 0.6;
  animation: float 9s ease-in-out infinite reverse;
  z-index: 1;
}

/* 页面内容 */
.create-record-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  color: var(--ink-medium);
  border: 2px solid var(--ink-light);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-button:hover {
  color: var(--accent-blue);
  border-color: var(--accent-blue);
  transform: translateY(-50%) translateX(-3px);
}

.page-title {
  color: var(--ink-dark);
  font-family: var(--font-handwritten);
  margin-bottom: 8px;
  transform: rotate(-1deg);
}

.page-subtitle {
  color: var(--ink-medium);
  font-size: 16px;
  transform: rotate(0.5deg);
}

/* 表单卡片 */
.create-form-card {
  background: var(--paper-warm);
  border: 3px solid var(--ink-dark);
  border-radius: 20px;
  padding: 40px;
  transform: rotate(-0.5deg);
  box-shadow: 5px 5px 0px var(--shadow-light);
  transition: all 0.3s ease;
}

.create-form-card:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 8px 8px 0px var(--shadow-medium);
}

/* 表单样式 */
.create-study-form {
  transform: rotate(0.5deg);
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  color: var(--accent-blue);
  font-family: var(--font-handwritten);
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px dashed var(--border-light);
  transform: rotate(-0.3deg);
}

.form-row {
  margin-bottom: 20px;
}

.form-row-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-col {
  min-width: 0;
}

/* 手绘风格输入框 */
.sketch-input .semi-input,
.sketch-input .semi-input-number,
.sketch-input .semi-select,
.sketch-input .semi-datepicker {
  border: 2px solid var(--ink-dark) !important;
  border-radius: 8px !important;
  background: var(--paper-bg) !important;
  font-family: var(--font-system) !important;
  transition: all 0.3s ease !important;
}

.sketch-input .semi-input:focus,
.sketch-input .semi-input-number:focus,
.sketch-input .semi-select:focus,
.sketch-input .semi-datepicker:focus {
  border-color: var(--accent-blue) !important;
  box-shadow: 2px 2px 0px var(--accent-blue) !important;
  transform: translateY(-1px) !important;
}

.sketch-input .semi-input:hover,
.sketch-input .semi-input-number:hover,
.sketch-input .semi-select:hover,
.sketch-input .semi-datepicker:hover {
  border-color: var(--accent-purple) !important;
}

/* 手绘风格文本域 */
.sketch-textarea .semi-textarea {
  border: 2px solid var(--ink-dark) !important;
  border-radius: 12px !important;
  background: var(--paper-bg) !important;
  font-family: var(--font-system) !important;
  transition: all 0.3s ease !important;
  resize: vertical !important;
}

.sketch-textarea .semi-textarea:focus {
  border-color: var(--accent-blue) !important;
  box-shadow: 3px 3px 0px var(--accent-blue) !important;
  transform: translateY(-2px) !important;
}

.sketch-textarea .semi-textarea:hover {
  border-color: var(--accent-purple) !important;
}

/* 表单标签 */
.semi-form-field-label {
  color: var(--ink-dark) !important;
  font-weight: 600 !important;
  font-family: var(--font-system) !important;
  margin-bottom: 8px !important;
}

/* 表单操作按钮 */
.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px dashed var(--border-light);
}

.sketch-button {
  border: 2px solid var(--ink-dark) !important;
  border-radius: 12px !important;
  font-family: var(--font-handwritten) !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  transition: all 0.3s ease !important;
  transform: rotate(-1deg) !important;
}

.sketch-button:hover {
  transform: rotate(0deg) translateY(-2px) !important;
  box-shadow: 3px 3px 0px var(--shadow-medium) !important;
}

.sketch-button.primary {
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple)) !important;
  color: white !important;
  border-color: var(--ink-dark) !important;
}

.sketch-button.primary:hover {
  background: linear-gradient(135deg, var(--accent-purple), var(--accent-pink)) !important;
}

.sketch-button.secondary {
  background: var(--paper-warm) !important;
  color: var(--ink-dark) !important;
  border-color: var(--ink-medium) !important;
}

.sketch-button.secondary:hover {
  background: var(--accent-orange) !important;
  color: white !important;
}

/* 表单验证错误样式 */
.semi-form-field-error .semi-input,
.semi-form-field-error .semi-input-number,
.semi-form-field-error .semi-select,
.semi-form-field-error .semi-datepicker,
.semi-form-field-error .semi-textarea {
  border-color: var(--accent-orange) !important;
  box-shadow: 2px 2px 0px var(--accent-orange) !important;
}

.semi-form-field-error-message {
  color: var(--accent-orange) !important;
  font-family: var(--font-system) !important;
  font-size: 14px !important;
  margin-top: 4px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .create-study-record-container {
    padding: 16px;
    padding-top: 64px;
  }

  .create-record-content {
    max-width: 100%;
  }

  .create-form-card {
    padding: 24px;
    transform: rotate(0deg);
    border-radius: 16px;
  }

  .create-study-form {
    transform: rotate(0deg);
  }

  .form-row-group {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .back-button {
    position: static;
    transform: none;
    margin-bottom: 16px;
  }

  .page-title {
    transform: rotate(0deg);
    font-size: 24px;
  }

  .page-subtitle {
    transform: rotate(0deg);
  }

  .section-title {
    transform: rotate(0deg);
    font-size: 18px;
  }

  .sketch-button {
    transform: rotate(0deg) !important;
    width: 100%;
    margin-bottom: 12px;
  }

  .sketch-button:hover {
    transform: translateY(-1px) !important;
  }

  /* 隐藏浮动装饰元素 */
  .floating-emoji {
    display: none;
  }
}

@media (max-width: 480px) {
  .create-form-card {
    padding: 20px;
    margin: 0 8px;
  }

  .form-actions {
    margin-top: 30px;
    padding-top: 20px;
  }
}
