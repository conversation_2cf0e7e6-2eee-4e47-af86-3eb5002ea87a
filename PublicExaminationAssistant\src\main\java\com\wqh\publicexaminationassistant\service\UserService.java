package com.wqh.publicexaminationassistant.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.*;
import com.wqh.publicexaminationassistant.dto.response.*;
import com.wqh.publicexaminationassistant.entity.InviteCode;
import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import com.wqh.publicexaminationassistant.mapper.InviteCodeMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import com.wqh.publicexaminationassistant.security.JwtTokenProvider;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.FileUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * 用户服务类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final InviteCodeMapper inviteCodeMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final FileUploadService fileUploadService;
    private final ReputationService reputationService;
    private final UserProtectionService userProtectionService;

    /**
     * 用户注册
     */
    @Transactional(rollbackFor = Exception.class)
    public RegisterResponse register(UserRegisterRequest request) {
        log.info("用户注册请求: username={}, email={}", request.getUsername(), request.getEmail());

        // 1. 验证用户名是否已存在
        if (existsByUsername(request.getUsername())) {
            throw new BusinessException(ResultCode.USERNAME_ALREADY_EXISTS);
        }

        // 2. 验证邮箱是否已存在
        if (existsByEmail(request.getEmail())) {
            throw new BusinessException(ResultCode.EMAIL_ALREADY_EXISTS);
        }

        // 3. 验证邀请码
        InviteCode inviteCode = validateInviteCode(request.getInviteCode());

        // 4. 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setNickname(StringUtils.hasText(request.getNickname()) ? request.getNickname() : request.getUsername());
        user.setReputationScore(100); // 初始信誉分数
        user.setReputationLevel("newbie"); // 初始信誉等级
        user.setSystemRole("user"); // 默认角色
        user.setIsActive(true);

        // 5. 保存用户
        userMapper.insert(user);

        // 6. 标记邀请码为已使用
        inviteCode.setUsedBy(user.getId());
        inviteCode.setUsedAt(LocalDateTime.now());
        inviteCodeMapper.updateById(inviteCode);

        // 7. 初始化用户信誉系统
        try {
            userProtectionService.initUserProtection(user.getId());
            log.info("为新用户 {} 初始化信誉系统成功", user.getId());
        } catch (Exception e) {
            log.error("为新用户 {} 初始化信誉系统失败", user.getId(), e);
            // 不影响注册流程，继续执行
        }

        log.info("用户注册成功: userId={}, username={}", user.getId(), user.getUsername());

        return RegisterResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .build();
    }

    /**
     * 用户登录
     */
    public LoginResponse login(UserLoginRequest request) {
        log.info("用户登录请求: username={}", request.getUsername());

        // 1. 查找用户（支持用户名或邮箱登录）
        User user = findByUsernameOrEmail(request.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.INVALID_CREDENTIALS);
        }

        // 2. 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            throw new BusinessException(ResultCode.INVALID_CREDENTIALS);
        }

        // 3. 检查账户状态
        if (!user.getIsActive()) {
            throw new BusinessException(ResultCode.ACCOUNT_DISABLED);
        }

        // 4. 更新最后登录时间
        user.setLastLoginAt(LocalDateTime.now());
        userMapper.updateById(user);

        // 5. 生成JWT Token
        Authentication authentication = createAuthentication(user);
        String token = jwtTokenProvider.createToken(authentication);
        String refreshToken = jwtTokenProvider.createRefreshToken(authentication);

        // 6. 处理信誉系统登录奖励
        try {
            handleLoginReward(user.getId());
        } catch (Exception e) {
            log.error("处理用户 {} 登录奖励失败", user.getId(), e);
            // 不影响登录流程
        }

        // 7. 构建用户信息响应
        UserInfoResponse userInfo = buildUserInfoResponse(user);

        log.info("用户登录成功: userId={}, username={}", user.getId(), user.getUsername());

        return LoginResponse.builder()
                .token(token)
                .refreshToken(refreshToken)
                .userInfo(userInfo)
                .build();
    }

    /**
     * 刷新Token
     */
    public LoginResponse refreshToken(RefreshTokenRequest request) {
        try {
            // 1. 验证刷新Token并获取用户名
            String username = jwtTokenProvider.getUsernameFromRefreshToken(request.getRefreshToken());

            // 2. 查找用户
            User user = findByUsernameOrEmail(username);
            if (user == null || !user.getIsActive()) {
                throw new BusinessException(ResultCode.INVALID_CREDENTIALS);
            }

            // 3. 生成新的Token
            Authentication authentication = createAuthentication(user);
            String newToken = jwtTokenProvider.createToken(authentication);
            String newRefreshToken = jwtTokenProvider.createRefreshToken(authentication);

            // 4. 构建用户信息响应
            UserInfoResponse userInfo = buildUserInfoResponse(user);

            return LoginResponse.builder()
                    .token(newToken)
                    .refreshToken(newRefreshToken)
                    .userInfo(userInfo)
                    .build();

        } catch (Exception e) {
            log.error("刷新Token失败", e);
            throw new BusinessException(ResultCode.TOKEN_EXPIRED);
        }
    }

    /**
     * 获取用户信息
     */
    public UserInfoResponse getUserInfo(String userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return buildUserInfoResponse(user);
    }

    /**
     * 更新个人资料
     */
    @Transactional(rollbackFor = Exception.class)
    public UserInfoResponse updateProfile(String userId, UpdateProfileRequest request) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 更新字段
        if (StringUtils.hasText(request.getNickname())) {
            user.setNickname(request.getNickname());
        }
        if (StringUtils.hasText(request.getAvatarUrl())) {
            user.setAvatarUrl(request.getAvatarUrl());
        }
        if (StringUtils.hasText(request.getTargetPosition())) {
            user.setTargetPosition(request.getTargetPosition());
        }
        if (StringUtils.hasText(request.getPhone())) {
            user.setPhone(request.getPhone());
        }

        userMapper.updateById(user);

        log.info("用户资料更新成功: userId={}", userId);
        return buildUserInfoResponse(user);
    }

    /**
     * 修改密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void changePassword(String userId, ChangePasswordRequest request) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPasswordHash())) {
            throw new BusinessException(ResultCode.INVALID_OLD_PASSWORD);
        }

        // 更新密码
        user.setPasswordHash(passwordEncoder.encode(request.getNewPassword()));
        userMapper.updateById(user);

        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 上传用户头像
     */
    @Transactional(rollbackFor = Exception.class)
    public UserInfoResponse uploadAvatar(String userId, MultipartFile file) {
        log.info("用户上传头像: userId={}, fileName={}, fileSize={}",
                userId, file.getOriginalFilename(), file.getSize());

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        try {
            // 上传头像文件到 avatars 子目录
            String avatarPath = fileUploadService.uploadImage(file, "avatars");

            // 构建完整的头像URL
            String avatarUrl = "/api/files/" + avatarPath;

            // 更新用户头像URL
            user.setAvatarUrl(avatarUrl);
            userMapper.updateById(user);

            log.info("用户头像上传成功: userId={}, avatarUrl={}", userId, avatarUrl);
            return buildUserInfoResponse(user);

        } catch (Exception e) {
            log.error("用户头像上传失败: userId={}", userId, e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_FAILED, "头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户名是否存在
     */
    private boolean existsByUsername(String username) {
        return userMapper.selectCount(
                new LambdaQueryWrapper<User>().eq(User::getUsername, username)
        ) > 0;
    }

    /**
     * 验证邮箱是否存在
     */
    private boolean existsByEmail(String email) {
        return userMapper.selectCount(
                new LambdaQueryWrapper<User>().eq(User::getEmail, email)
        ) > 0;
    }

    /**
     * 验证邀请码
     */
    private InviteCode validateInviteCode(String code) {
        InviteCode inviteCode = inviteCodeMapper.selectOne(
                new LambdaQueryWrapper<InviteCode>()
                        .eq(InviteCode::getCode, code)
                        .eq(InviteCode::getIsActive, true)
        );

        if (inviteCode == null) {
            throw new BusinessException(ResultCode.INVALID_INVITE_CODE);
        }

        if (inviteCode.getUsedBy() != null) {
            throw new BusinessException(ResultCode.INVITE_CODE_USED);
        }

        if (inviteCode.getExpiresAt().isBefore(LocalDateTime.now())) {
            throw new BusinessException(ResultCode.INVALID_INVITE_CODE);
        }

        return inviteCode;
    }

    /**
     * 根据用户名或邮箱查找用户
     */
    private User findByUsernameOrEmail(String usernameOrEmail) {
        return userMapper.selectOne(
                new LambdaQueryWrapper<User>()
                        .eq(User::getUsername, usernameOrEmail)
                        .or()
                        .eq(User::getEmail, usernameOrEmail)
        );
    }

    /**
     * 创建认证对象
     */
    private Authentication createAuthentication(User user) {
        JwtUserDetails userDetails = new JwtUserDetails(
                user.getId(),
                user.getUsername(),
                user.getPasswordHash(),
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + user.getSystemRole().toUpperCase()))
        );
        return new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    }

    /**
     * 构建用户信息响应
     */
    private UserInfoResponse buildUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        BeanUtils.copyProperties(user, response);
        return response;
    }

    // =====================================================
    // 信誉系统集成方法
    // =====================================================

    /**
     * 处理用户登录奖励
     * 确保每天只能获得一次登录奖励
     *
     * @param userId 用户ID
     */
    private void handleLoginReward(String userId) {
        try {
            // 获取用户信誉统计
            UserReputationStats stats = reputationService.getUserReputationStats(userId);
            if (stats == null) {
                // 如果没有信誉统计记录，初始化一个
                userProtectionService.initUserProtection(userId);
                stats = reputationService.getUserReputationStats(userId);
            }

            LocalDate today = LocalDate.now();
            LocalDate lastLoginDate = stats.getLastLoginDate();

            // 检查今天是否已经获得过登录奖励
            if (lastLoginDate != null && lastLoginDate.equals(today)) {
                log.debug("用户 {} 今天已经获得过登录奖励，跳过处理", userId);
                return;
            }

            // 计算新的连续登录天数
            int newConsecutiveLoginDays = calculateNewConsecutiveLoginDays(lastLoginDate, today, stats.getConsecutiveLoginDays());

            // 每日登录基础奖励
            reputationService.addPoints(
                userId,
                1,
                "每日登录奖励",
                "daily_login"
            );

            // 连续登录奖励
            if (newConsecutiveLoginDays >= 7) {
                int bonusPoints = calculateLoginBonus(newConsecutiveLoginDays);
                if (bonusPoints > 0) {
                    reputationService.addPoints(
                        userId,
                        bonusPoints,
                        String.format("连续登录%d天奖励", newConsecutiveLoginDays),
                        "daily_login",
                        null,
                        newConsecutiveLoginDays
                    );
                }
            }

            // 更新用户的登录日期和连续登录天数
            updateUserLoginStats(userId, today, newConsecutiveLoginDays);

            log.info("用户 {} 登录奖励处理完成，连续登录天数: {}", userId, newConsecutiveLoginDays);

        } catch (Exception e) {
            log.error("处理用户 {} 登录奖励失败", userId, e);
            throw e;
        }
    }

    /**
     * 计算新的连续登录天数
     *
     * @param lastLoginDate 上次登录日期
     * @param today 今天日期
     * @param currentConsecutiveDays 当前连续登录天数
     * @return 新的连续登录天数
     */
    private int calculateNewConsecutiveLoginDays(LocalDate lastLoginDate, LocalDate today, Integer currentConsecutiveDays) {
        if (lastLoginDate == null) {
            // 首次登录
            return 1;
        }

        int currentDays = currentConsecutiveDays != null ? currentConsecutiveDays : 0;

        if (lastLoginDate.equals(today.minusDays(1))) {
            // 上次登录是昨天，连续天数+1
            return currentDays + 1;
        } else if (lastLoginDate.isBefore(today.minusDays(1))) {
            // 中断了连续登录（超过1天没登录）
            return 1;
        } else {
            // 这种情况不应该发生（lastLoginDate在今天之后）
            log.warn("用户登录日期异常: lastLoginDate={}, today={}", lastLoginDate, today);
            return 1;
        }
    }

    /**
     * 更新用户登录统计信息
     *
     * @param userId 用户ID
     * @param loginDate 登录日期
     * @param consecutiveLoginDays 连续登录天数
     */
    private void updateUserLoginStats(String userId, LocalDate loginDate, int consecutiveLoginDays) {
        try {
            UserReputationStats stats = reputationService.getUserReputationStats(userId);
            if (stats != null) {
                stats.setLastLoginDate(loginDate);
                stats.setConsecutiveLoginDays(consecutiveLoginDays);
                stats.setUpdatedAt(LocalDateTime.now());

                // 这里需要调用mapper更新数据库
                // 由于没有直接的mapper注入，我们通过ReputationService来更新
                reputationService.updateUserLoginStats(userId, loginDate, consecutiveLoginDays);

                log.debug("更新用户 {} 登录统计: 登录日期={}, 连续天数={}", userId, loginDate, consecutiveLoginDays);
            }
        } catch (Exception e) {
            log.error("更新用户 {} 登录统计失败", userId, e);
        }
    }

    /**
     * 计算连续登录奖励分数
     *
     * @param consecutiveDays 连续登录天数
     * @return 奖励分数
     */
    private int calculateLoginBonus(int consecutiveDays) {
        if (consecutiveDays >= 100) {
            return 25; // 连续100天
        } else if (consecutiveDays >= 30) {
            return 10; // 连续30天
        } else if (consecutiveDays >= 7) {
            return 3;  // 连续7天
        }
        return 0;
    }
}
