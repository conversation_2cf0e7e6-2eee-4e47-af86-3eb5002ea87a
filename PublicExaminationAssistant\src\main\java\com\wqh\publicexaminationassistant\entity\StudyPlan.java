package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学习计划表实体类
 * 管理用户的学习计划和目标
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("study_plans")
public class StudyPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 计划ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 计划标题
     */
    @TableField("title")
    private String title;

    /**
     * 计划描述
     */
    @TableField("description")
    private String description;

    /**
     * 目标考试日期
     */
    @TableField("target_exam_date")
    private LocalDate targetExamDate;

    /**
     * 每日目标题量
     */
    @TableField("daily_target_questions")
    private Integer dailyTargetQuestions;

    /**
     * 每日目标时长(分钟)
     */
    @TableField("daily_target_time")
    private Integer dailyTargetTime;

    /**
     * 学习模块配置(JSON格式)
     */
    @TableField("modules")
    private String modules;

    /**
     * 计划状态
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
