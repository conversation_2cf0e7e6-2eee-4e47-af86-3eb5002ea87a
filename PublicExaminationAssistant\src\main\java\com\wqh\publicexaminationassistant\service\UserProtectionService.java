package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.entity.User;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import com.wqh.publicexaminationassistant.mapper.UserReputationStatsMapper;
import com.wqh.publicexaminationassistant.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 用户保护期服务
 * 管理新用户的保护期机制
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Slf4j
public class UserProtectionService {

    @Autowired
    private UserReputationStatsMapper userReputationStatsMapper;
    
    @Autowired
    private UserMapper userMapper;

    /**
     * 检查用户是否在保护期内
     * 
     * @param userId 用户ID
     * @return true-在保护期内，false-不在保护期内
     */
    public boolean isInProtectionPeriod(String userId) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                log.warn("用户 {} 的信誉统计记录不存在", userId);
                return false;
            }
            
            if (stats.getProtectionEndTime() == null) {
                return false;
            }
            
            boolean inProtection = LocalDateTime.now().isBefore(stats.getProtectionEndTime());
            log.debug("用户 {} 保护期检查结果: {}, 保护期结束时间: {}", 
                userId, inProtection, stats.getProtectionEndTime());
            
            return inProtection;
            
        } catch (Exception e) {
            log.error("检查用户 {} 保护期状态失败", userId, e);
            return false;
        }
    }

    /**
     * 获取保护期剩余时间
     * 
     * @param userId 用户ID
     * @return 剩余时间（Duration），如果不在保护期则返回Duration.ZERO
     */
    public Duration getProtectionTimeRemaining(String userId) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null || stats.getProtectionEndTime() == null) {
                return Duration.ZERO;
            }
            
            LocalDateTime now = LocalDateTime.now();
            if (now.isBefore(stats.getProtectionEndTime())) {
                return Duration.between(now, stats.getProtectionEndTime());
            }
            
            return Duration.ZERO;
            
        } catch (Exception e) {
            log.error("获取用户 {} 保护期剩余时间失败", userId, e);
            return Duration.ZERO;
        }
    }

    /**
     * 获取保护期剩余小时数
     * 
     * @param userId 用户ID
     * @return 剩余小时数
     */
    public long getProtectionHoursRemaining(String userId) {
        return getProtectionTimeRemaining(userId).toHours();
    }

    /**
     * 为新用户初始化保护期
     * 
     * @param userId 用户ID
     * @param protectionDays 保护期天数，默认3天
     */
    @Transactional(rollbackFor = Exception.class)
    public void initUserProtection(String userId, int protectionDays) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.error("用户 {} 不存在，无法初始化保护期", userId);
                return;
            }
            
            // 检查是否已存在信誉统计记录
            UserReputationStats existingStats = userReputationStatsMapper.selectByUserId(userId);
            if (existingStats != null) {
                log.info("用户 {} 已存在信誉统计记录，跳过保护期初始化", userId);
                return;
            }
            
            // 创建新的信誉统计记录
            UserReputationStats stats = new UserReputationStats();
            stats.setUserId(userId);
            stats.setCurrentScore(100); // 初始分数
            stats.setCurrentLevel(UserReputationStats.Level.NEWBIE);
            stats.setTotalEarned(0);
            stats.setTotalDeducted(0);
            stats.setConsecutiveLoginDays(0);
            stats.setConsecutiveStudyDays(0);
            stats.setConsecutiveNoStudyDays(0);
            stats.setWeeklyDeductPoints(0);
            stats.setMonthlyDeductPoints(0);
            
            // 设置保护期结束时间
            LocalDateTime protectionEndTime = user.getCreatedAt().plusDays(protectionDays);
            stats.setProtectionEndTime(protectionEndTime);
            stats.setLastScoreUpdate(LocalDateTime.now());
            
            userReputationStatsMapper.insert(stats);
            
            log.info("为用户 {} 初始化保护期成功，保护期结束时间: {}", userId, protectionEndTime);
            
        } catch (Exception e) {
            log.error("为用户 {} 初始化保护期失败", userId, e);
            throw new RuntimeException("初始化用户保护期失败", e);
        }
    }

    /**
     * 为新用户初始化保护期（使用默认3天）
     * 
     * @param userId 用户ID
     */
    public void initUserProtection(String userId) {
        initUserProtection(userId, 3);
    }

    /**
     * 手动结束用户保护期
     * 
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void endUserProtection(String userId) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                log.warn("用户 {} 的信誉统计记录不存在", userId);
                return;
            }
            
            stats.setProtectionEndTime(LocalDateTime.now().minusMinutes(1));
            userReputationStatsMapper.updateById(stats);
            
            log.info("手动结束用户 {} 的保护期", userId);
            
        } catch (Exception e) {
            log.error("结束用户 {} 保护期失败", userId, e);
            throw new RuntimeException("结束用户保护期失败", e);
        }
    }

    /**
     * 延长用户保护期
     * 
     * @param userId 用户ID
     * @param additionalHours 延长的小时数
     */
    @Transactional(rollbackFor = Exception.class)
    public void extendUserProtection(String userId, int additionalHours) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                log.warn("用户 {} 的信誉统计记录不存在", userId);
                return;
            }
            
            LocalDateTime currentEndTime = stats.getProtectionEndTime();
            if (currentEndTime == null) {
                currentEndTime = LocalDateTime.now();
            }
            
            LocalDateTime newEndTime = currentEndTime.plusHours(additionalHours);
            stats.setProtectionEndTime(newEndTime);
            userReputationStatsMapper.updateById(stats);
            
            log.info("延长用户 {} 保护期 {} 小时，新的结束时间: {}", userId, additionalHours, newEndTime);
            
        } catch (Exception e) {
            log.error("延长用户 {} 保护期失败", userId, e);
            throw new RuntimeException("延长用户保护期失败", e);
        }
    }

    /**
     * 获取用户保护期信息
     * 
     * @param userId 用户ID
     * @return 保护期信息
     */
    public ProtectionInfo getProtectionInfo(String userId) {
        try {
            UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
            if (stats == null) {
                return new ProtectionInfo(false, null, Duration.ZERO);
            }
            
            boolean inProtection = isInProtectionPeriod(userId);
            Duration remaining = getProtectionTimeRemaining(userId);
            
            return new ProtectionInfo(inProtection, stats.getProtectionEndTime(), remaining);
            
        } catch (Exception e) {
            log.error("获取用户 {} 保护期信息失败", userId, e);
            return new ProtectionInfo(false, null, Duration.ZERO);
        }
    }

    /**
     * 保护期信息类
     */
    public static class ProtectionInfo {
        private final boolean inProtection;
        private final LocalDateTime endTime;
        private final Duration remaining;

        public ProtectionInfo(boolean inProtection, LocalDateTime endTime, Duration remaining) {
            this.inProtection = inProtection;
            this.endTime = endTime;
            this.remaining = remaining;
        }

        public boolean isInProtection() { return inProtection; }
        public LocalDateTime getEndTime() { return endTime; }
        public Duration getRemaining() { return remaining; }
        public long getRemainingHours() { return remaining.toHours(); }
        public long getRemainingMinutes() { return remaining.toMinutes(); }
    }
}
