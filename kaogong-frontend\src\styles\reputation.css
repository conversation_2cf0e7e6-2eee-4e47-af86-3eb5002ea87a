/* 信誉系统页面样式 - 手绘温馨风格 */

.reputation-page {
  min-height: 100vh;
  background: var(--paper-bg);
  padding: 20px;
}

.reputation-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题区域 */
.reputation-header {
  text-align: center;
  margin-bottom: 40px;
}

.reputation-title {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-3xl);
  color: var(--ink-dark);
  margin-bottom: 12px;
  transform: rotate(-1deg);
  text-shadow: 2px 2px 0px var(--shadow-light);
}

.reputation-subtitle {
  font-size: 16px;
  color: var(--ink-medium);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.5;
}

/* 信誉概览卡片 */
.reputation-overview-card {
  background: var(--paper-warm);
  border: 3px solid var(--ink-dark);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  transform: rotate(-0.5deg);
  box-shadow: 5px 5px 0px var(--shadow-medium);
  transition: all 0.3s ease;
  position: relative;
}

.reputation-overview-card:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 8px 8px 0px var(--shadow-dark);
}

/* 装饰性emoji */
.floating-emoji {
  position: absolute;
  font-size: 24px;
  animation: float 3s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.6;
}

.floating-emoji.top-left {
  top: 15px;
  left: 15px;
  animation-delay: 0s;
}

.floating-emoji.top-right {
  top: 15px;
  right: 15px;
  animation-delay: 1s;
}

.floating-emoji.bottom-left {
  bottom: 15px;
  left: 15px;
  animation-delay: 2s;
}

.floating-emoji.bottom-right {
  bottom: 15px;
  right: 15px;
  animation-delay: 0.5s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-8px) rotate(3deg); }
}

/* 分数显示 */
.score-display {
  text-align: center;
  margin-bottom: 25px;
}

.current-score {
  font-family: var(--font-handwritten);
  font-size: 48px;
  font-weight: 700;
  color: var(--accent-blue);
  text-shadow: 2px 2px 0px var(--shadow-light);
  margin: 0;
}

.score-label {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-lg);
  color: var(--ink-medium);
  margin-top: 5px;
}

.score-change {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 12px;
  font-size: 14px;
}

.score-change .earned {
  color: var(--accent-green);
  font-weight: 600;
}

.score-change .deducted {
  color: var(--accent-red);
  font-weight: 600;
}

/* 等级徽章 */
.level-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: var(--paper-cream);
  border: 2px solid var(--ink-dark);
  border-radius: 25px;
  padding: 8px 16px;
  font-family: var(--font-handwritten);
  font-size: var(--font-size-lg);
  font-weight: 600;
  transform: rotate(1deg);
  box-shadow: 2px 2px 0px var(--shadow-light);
  margin-bottom: 20px;
}

.level-icon {
  font-size: 20px;
}

/* 等级进度条 */
.level-progress {
  margin-bottom: 25px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  color: var(--ink-medium);
}

.progress-points {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-sm);
  color: var(--ink-light);
}

.sketch-progress {
  height: 12px;
  background: var(--paper-cream);
  border: 2px solid var(--ink-dark);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  transform: rotate(-0.2deg);
}

.sketch-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
  border-radius: 8px;
  transition: width 0.8s ease;
  position: relative;
}

.sketch-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(255, 255, 255, 0.1) 2px,
    rgba(255, 255, 255, 0.1) 4px
  );
}

/* 保护期提示 */
.protection-notice {
  background: linear-gradient(135deg, #fff59d 0%, #fff176 100%);
  border: 2px dashed var(--accent-orange);
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 25px;
  transform: rotate(0.5deg);
  position: relative;
}

.protection-notice::before {
  content: '🛡️';
  position: absolute;
  top: -10px;
  left: 15px;
  font-size: 20px;
  background: var(--paper-bg);
  padding: 0 5px;
}

.protection-text {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  color: var(--ink-dark);
  margin: 0;
  text-align: center;
}

/* 统计信息网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: var(--paper-cream);
  border: 2px solid var(--ink-dark);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  transform: rotate(-0.3deg);
  transition: all 0.3s ease;
  box-shadow: 3px 3px 0px var(--shadow-light);
}

.stat-card:nth-child(even) {
  transform: rotate(0.3deg);
}

.stat-card:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 5px 5px 0px var(--shadow-medium);
}

.stat-value {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--accent-blue);
  margin: 0 0 5px 0;
}

.stat-label {
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  color: var(--ink-medium);
  margin: 0;
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

/* 操作按钮 */
.reputation-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.sketch-button {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 25px;
  padding: 12px 24px;
  font-family: var(--font-handwritten);
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--ink-dark);
  cursor: pointer;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  box-shadow: 3px 3px 0px var(--shadow-light);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.sketch-button:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 5px 5px 0px var(--shadow-medium);
  color: var(--accent-blue);
  border-color: var(--accent-blue);
}

.sketch-button.primary {
  background: var(--accent-blue);
  color: white;
  border-color: var(--accent-blue);
}

.sketch-button.primary:hover {
  background: var(--accent-blue-light);
  border-color: var(--accent-blue-light);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reputation-page {
    padding: 15px;
  }
  
  .reputation-overview-card {
    padding: 20px;
    transform: rotate(0deg);
  }
  
  .current-score {
    font-size: 36px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .stat-card {
    transform: rotate(0deg);
    padding: 15px;
  }
  
  .floating-emoji {
    display: none;
  }
  
  .reputation-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .sketch-button {
    transform: rotate(0deg);
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

/* 新增样式 - 保护期标题 */
.protection-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.protection-title {
  font-family: var(--font-handwritten);
  font-weight: 600;
  color: var(--accent-orange);
  font-size: 16px;
}

/* 活跃度信息 */
.activity-info {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
  padding: 16px;
  background: var(--paper-cream);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: var(--paper-warm);
  transform: translateY(-2px);
}

.activity-item.warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.activity-icon {
  color: var(--accent-blue);
  font-size: 18px;
}

.activity-item.warning .activity-icon {
  color: #ffc107;
}

.activity-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activity-label {
  font-size: 12px;
  color: var(--ink-medium);
  margin-bottom: 2px;
}

.activity-value {
  font-family: var(--font-handwritten);
  font-size: 16px;
  font-weight: 600;
  color: var(--ink-dark);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

/* 快速统计 */
.quick-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: var(--paper-cream);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.quick-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.quick-stat-item .stat-label {
  font-size: 12px;
  color: var(--ink-medium);
}

.quick-stat-item .stat-value {
  font-family: var(--font-handwritten);
  font-size: 14px;
  font-weight: 600;
  color: var(--ink-dark);
}
