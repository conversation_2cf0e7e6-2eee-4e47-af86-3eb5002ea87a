# 考公刷题记录系统 - 功能需求文档

## 项目概述

本项目是一个公益性的考公刷题记录网站，采用创新的"小桌"机制，让用户能够记录刷题进度、参与小组竞争，并获取考试资讯。系统不提供题库，专注于学习记录和进度管理。

## 系统功能架构图

```mermaid
graph TB
    A[考公刷题记录系统] --> B[用户管理系统]
    A --> C[刷题记录管理]
    A --> D[小桌排行系统]
    A --> E[考试公告系统]
    A --> F[数据分析面板]
    A --> G[信誉体系]

    B --> B1[邀请注册]
    B --> B2[用户认证]
    B --> B3[个人资料管理]

    C --> C1[模块化记录]
    C --> C2[学习统计]
    C --> C3[错题本]
    C --> C4[学习计划]

    D --> D1[小桌管理]
    D --> D2[小桌排行榜]
    D --> D3[全站排行榜]
    D --> D4[桌长审核机制]

    E --> E1[公告发布]
    E --> E2[公告管理]
    E --> E3[时间提醒]

    F --> F1[个人报告]
    F --> F2[AI智能分析]
    F --> F3[学习洞察]

    G --> G1[信誉评分]
    G --> G2[质量保障]
    G --> G3[权限管理]

    style D1 fill:#ff9999
    style D2 fill:#ff9999
    style D4 fill:#ff9999
    style G1 fill:#ff9999
    style G2 fill:#ff9999
```

_注：🌟 标记为核心创新功能_

---

## 功能需求详细说明

### 一、普通用户功能

#### 1.1 用户管理系统 【优先级：P0】

**1.1.1 邀请注册机制**

- 通过邀请码注册账户
- 邀请码验证和使用记录
- 邀请关系链追踪（无奖励机制）

**1.1.2 用户认证**

- 用户登录/登出
- 密码找回功能
- 账户安全设置

**1.1.3 个人资料管理**

- 基本信息编辑（头像、昵称、目标岗位）
- 学习偏好设置
- 隐私设置管理

#### 1.2 刷题记录管理 【优先级：P0】

**1.2.1 模块化记录**

- 按考试模块分类记录（行测各专项、申论、专业科目）
- 每次刷题的题量、正确率、用时记录
- 薄弱知识点标记和分类

**1.2.2 学习统计**

- 每日/每周/每月刷题统计
- 正确率趋势分析
- 刷题时长统计和分布

**1.2.3 错题本功能**

- 错题收集和分类
- 错题复习提醒
- 错题掌握状态跟踪

**1.2.4 学习计划管理**

- 个人学习计划制定
- 学习提醒设置
- 阶段性目标设定和跟踪

#### 1.3 小桌排行系统 【优先级：P1】🌟

**1.3.1 小桌参与**

- 浏览现有小桌列表
- 申请加入小桌（需填写加入理由、学习计划）
- 创建新的学习小桌
- 退出小桌功能

**1.3.2 小桌互动**

- 小桌内学习打卡分享
- 成员间学习交流
- 小桌共同目标设定

**1.3.3 排行榜查看**

- 小桌内排行榜（刷题量、学习时长、连续打卡等）
- 全站大排行榜
- 多维度排名切换

#### 1.4 考试公告系统 【优先级：P2】

**1.4.1 公告浏览**

- 按地区/类型筛选考试公告
- 公告详情查看
- 重要公告收藏

**1.4.2 时间提醒**

- 重要时间节点提醒
- 个人报名状态跟踪
- 考试倒计时显示

#### 1.5 数据分析面板 【优先级：P2】

**1.5.1 个人学习报告**

- 各模块掌握度可视化（进度条/雷达图）
- 学习效率分析报告
- 阶段性进步总结

**1.5.2 AI 智能分析**（后期功能）

- 个性化学习建议
- 备考策略优化建议
- 智能预测分析
- 学习效率诊断

---

### 二、桌长功能

#### 2.1 小桌管理 【优先级：P1】🌟

**2.1.1 桌长审核机制**

- 查看申请者详细信息（学习记录、目标考试、活跃度、信誉分）
- 审核加入申请（48 小时审核时间，超时自动通过）
- 设置自动审核条件（如信誉分阈值）

**2.1.2 成员管理**

- 移除不活跃成员
- 设置小桌活跃度要求
- 小桌信息和规则设置

**2.1.3 小桌运营**

- 发布小桌公告
- 组织学习活动
- 小桌氛围维护

---

### 三、管理员功能

#### 3.1 用户管理 【优先级：P0】

**3.1.1 邀请码管理**

- 批量生成邀请码
- 邀请码使用情况统计
- 邀请码有效期管理

**3.1.2 用户监管**

- 用户行为监控
- 违规用户处理
- 用户数据统计

#### 3.2 考试公告管理 【优先级：P2】

**3.2.1 公告发布**

- 发布考试公告
- 公告内容编辑和格式化
- 公告优先级设置

**3.2.2 公告维护**

- 公告信息更新
- 过期公告管理
- 公告统计分析

#### 3.3 系统管理 【优先级：P1】

**3.3.1 信誉体系管理**🌟

- 信誉规则配置
- 异常信誉处理
- 信誉统计分析

**3.3.2 小桌系统监管**

- 小桌质量监控
- 问题小桌处理
- 小桌数据统计

---

## 核心创新功能详解

### 🌟 小桌机制

- **概念**：6-8 人的小组排行榜，替代传统的全员大排行
- **优势**：增强参与感、减少竞争压力、促进社交互动
- **实现**：自由申请 + 桌长审核 + 人数限制

### 🌟 三重质量保障机制

1. **桌长审核机制**：人工把关，确保成员质量匹配
2. **信誉体系**：量化用户行为，建立准入门槛
3. **质量保障机制**：自动化监控，维持长期活跃

### 🌟 信誉体系设计

- **初始分值**：100 分
- **等级划分**：新手(0-60)、学员(61-80)、学霸(81-95)、导师(96-100)
- **加分项**：坚持打卡、帮助他人、积极互动
- **扣分项**：长期不活跃、恶意行为、频繁退桌

---

## 开发优先级说明

- **P0（核心功能）**：用户管理、刷题记录 - 系统基础功能
- **P1（重要功能）**：小桌系统、信誉体系 - 核心创新功能
- **P2（增值功能）**：考试公告、数据分析 - 用户体验增强

---

## 技术考虑

### 数据库设计要点

- 用户表、刷题记录表、小桌表、信誉记录表
- 考虑数据统计查询的性能优化
- 支持后续 AI 分析的数据结构

### 系统架构建议

- 前后端分离架构
- 支持高并发的排行榜计算
- 预留 AI 接口的扩展性

## 系统整体架构设计

### 技术架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web前端 - Next.js + React]
        B[移动端 - PWA]
        C[管理后台 - React Admin]
    end

    subgraph "网关层"
        D[API网关 - Nginx]
        E[负载均衡器]
        F[SSL终端]
    end

    subgraph "应用服务层"
        G[用户服务]
        H[刷题记录服务]
        I[小桌排行服务]
        J[考试公告服务]
        K[信誉系统服务]
        L[数据分析服务]
        M[通知服务]
    end

    subgraph "数据层"
        N[(主数据库 - MySQL)]
        O[(缓存 - Redis)]
        P[(搜索引擎 - Elasticsearch)]
        Q[(文件存储 - MinIO/OSS)]
    end

    subgraph "基础设施层"
        R[消息队列 - RabbitMQ]
        S[定时任务调度]
        T[监控告警 - Prometheus]
        U[日志收集 - ELK Stack]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    E --> L
    E --> M

    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N

    G --> O
    H --> O
    I --> O

    J --> P
    L --> P

    M --> R
    S --> R

    style I fill:#fff3e0
    style K fill:#fff3e0
    style N fill:#f3e5f5
    style O fill:#f3e5f5
```

### 微服务划分

1. **用户服务** - 用户注册/登录/认证/个人资料管理
2. **刷题记录服务** - 刷题数据记录/学习统计/错题本管理
3. **小桌排行服务** 🌟 - 小桌管理/排行榜计算/成员互动
4. **信誉系统服务** 🌟 - 信誉分计算/行为评估/等级管理
5. **考试公告服务** - 公告发布管理/搜索筛选/提醒通知
6. **数据分析服务** - 学习报告生成/数据可视化/AI 分析接口
7. **通知服务** - 消息推送/邮件通知/站内信

### 部署架构

- **开发环境**: Docker Compose 本地部署
- **测试环境**: 单机 Docker 部署 + 基础监控
- **生产环境**: Kubernetes 集群 + 完整监控告警体系

---

_文档版本：v1.1_
_最后更新：2024 年_
_项目性质：公益项目_
