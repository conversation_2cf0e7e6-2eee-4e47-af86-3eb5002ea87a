package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 刷题记录表实体类
 * 记录每次刷题的详细数据
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("study_records")
public class StudyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 模块类型
     */
    @TableField("module_type")
    private String moduleType;

    /**
     * 题目数量
     */
    @TableField("question_count")
    private Integer questionCount;

    /**
     * 正确数量
     */
    @TableField("correct_count")
    private Integer correctCount;

    /**
     * 正确率(%)
     */
    @TableField("accuracy_rate")
    private BigDecimal accuracyRate;

    /**
     * 学习时长(分钟)
     */
    @TableField("study_duration")
    private Integer studyDuration;

    /**
     * 薄弱知识点(JSON格式)
     */
    @TableField("weak_points")
    private String weakPoints;

    /**
     * 学习日期
     */
    @TableField("study_date")
    private LocalDate studyDate;

    /**
     * 学习笔记
     */
    @TableField("notes")
    private String notes;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
