package com.wqh.publicexaminationassistant.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 定时任务执行记录实体类
 * 记录定时任务的执行情况
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("scheduled_task_logs")
public class ScheduledTaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 执行日期
     */
    @TableField("execution_date")
    private LocalDate executionDate;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 执行状态: running-运行中, completed-已完成, failed-失败
     */
    @TableField("status")
    private String status;

    /**
     * 处理数量
     */
    @TableField("processed_count")
    private Integer processedCount;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    // 常量定义
    public static class Status {
        public static final String RUNNING = "running";
        public static final String COMPLETED = "completed";
        public static final String FAILED = "failed";
    }

    public static class TaskName {
        public static final String DAILY_STUDY_CHECK = "daily_study_check";
        public static final String WEEKLY_DESK_CHECK = "weekly_desk_check";
        public static final String MONTHLY_STATS_RESET = "monthly_stats_reset";
        public static final String INACTIVE_USER_CHECK = "inactive_user_check";
        public static final String RANKING_CALCULATION = "ranking_calculation";
    }

    /**
     * 计算执行时长（秒）
     */
    public long getExecutionDurationSeconds() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, endTime).getSeconds();
    }

    /**
     * 检查任务是否成功
     */
    public boolean isSuccessful() {
        return Status.COMPLETED.equals(status);
    }

    /**
     * 检查任务是否失败
     */
    public boolean isFailed() {
        return Status.FAILED.equals(status);
    }

    /**
     * 检查任务是否正在运行
     */
    public boolean isRunning() {
        return Status.RUNNING.equals(status);
    }
}
