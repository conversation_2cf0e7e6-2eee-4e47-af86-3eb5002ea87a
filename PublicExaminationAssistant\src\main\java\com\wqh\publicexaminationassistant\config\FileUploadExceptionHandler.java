package com.wqh.publicexaminationassistant.config;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

/**
 * 文件上传异常处理器
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@RestControllerAdvice
public class FileUploadExceptionHandler {

    /**
     * 处理文件大小超出限制异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ApiResponse<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.error("文件大小超出限制", e);
        return ApiResponse.error(ResultCode.FILE_SIZE_EXCEEDED.getCode(), "文件大小超出限制，最大允许10MB");
    }

    /**
     * 处理multipart解析异常
     */
    @ExceptionHandler(MultipartException.class)
    public ApiResponse<Void> handleMultipartException(MultipartException e) {
        log.error("文件上传解析失败", e);
        
        String message = "文件上传失败";
        if (e.getMessage() != null) {
            if (e.getMessage().contains("EOFException")) {
                message = "文件上传中断，请检查网络连接或文件完整性";
            } else if (e.getMessage().contains("SizeLimitExceededException")) {
                message = "文件大小超出限制，最大允许10MB";
            } else if (e.getMessage().contains("FileSizeLimitExceededException")) {
                message = "单个文件大小超出限制，最大允许10MB";
            }
        }
        
        return ApiResponse.error(ResultCode.FILE_UPLOAD_FAILED.getCode(), message);
    }

    /**
     * 处理IO异常
     */
    @ExceptionHandler(java.io.IOException.class)
    public ApiResponse<Void> handleIOException(java.io.IOException e) {
        log.error("文件IO异常", e);
        return ApiResponse.error(ResultCode.FILE_UPLOAD_FAILED.getCode(), "文件读写失败，请重试");
    }
}
