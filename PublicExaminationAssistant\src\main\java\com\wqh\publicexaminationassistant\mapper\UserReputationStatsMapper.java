package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.UserReputationStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户信誉统计Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface UserReputationStatsMapper extends BaseMapper<UserReputationStats> {

    /**
     * 根据用户ID查询信誉统计
     */
    @Select("SELECT * FROM user_reputation_stats WHERE user_id = #{userId}")
    UserReputationStats selectByUserId(@Param("userId") String userId);

    /**
     * 获取所有活跃用户（排除保护期用户）
     */
    @Select("SELECT urs.* FROM user_reputation_stats urs " +
            "INNER JOIN users u ON urs.user_id = u.id " +
            "WHERE u.status = 'active' " +
            "AND (urs.protection_end_time IS NULL OR urs.protection_end_time <= NOW())")
    List<UserReputationStats> selectActiveUsersExcludeProtected();

    /**
     * 获取保护期内的用户
     */
    @Select("SELECT * FROM user_reputation_stats " +
            "WHERE protection_end_time IS NOT NULL AND protection_end_time > NOW()")
    List<UserReputationStats> selectUsersInProtection();

    /**
     * 根据等级查询用户数量
     */
    @Select("SELECT COUNT(*) FROM user_reputation_stats WHERE current_level = #{level}")
    int countByLevel(@Param("level") String level);

    /**
     * 获取等级分布统计
     */
    @Select("SELECT current_level, COUNT(*) as count " +
            "FROM user_reputation_stats " +
            "GROUP BY current_level " +
            "ORDER BY CASE current_level " +
            "WHEN 'newbie' THEN 1 " +
            "WHEN 'bronze' THEN 2 " +
            "WHEN 'silver' THEN 3 " +
            "WHEN 'gold' THEN 4 " +
            "WHEN 'platinum' THEN 5 " +
            "WHEN 'diamond' THEN 6 " +
            "WHEN 'master' THEN 7 " +
            "WHEN 'grandmaster' THEN 8 " +
            "END")
    List<java.util.Map<String, Object>> selectLevelDistribution();

    /**
     * 更新用户分数和等级
     */
    @Update("UPDATE user_reputation_stats " +
            "SET current_score = #{score}, current_level = #{level}, " +
            "last_score_update = NOW(), updated_at = NOW() " +
            "WHERE user_id = #{userId}")
    int updateScoreAndLevel(@Param("userId") String userId, 
                           @Param("score") Integer score, 
                           @Param("level") String level);

    /**
     * 更新连续学习天数
     */
    @Update("UPDATE user_reputation_stats " +
            "SET consecutive_study_days = #{days}, " +
            "consecutive_no_study_days = 0, " +
            "last_study_date = #{date}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId}")
    int updateConsecutiveStudyDays(@Param("userId") String userId, 
                                  @Param("days") Integer days, 
                                  @Param("date") LocalDate date);

    /**
     * 更新连续未学习天数
     */
    @Update("UPDATE user_reputation_stats " +
            "SET consecutive_no_study_days = #{days}, " +
            "consecutive_study_days = 0, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId}")
    int updateConsecutiveNoStudyDays(@Param("userId") String userId, 
                                    @Param("days") Integer days);

    /**
     * 更新连续登录天数
     */
    @Update("UPDATE user_reputation_stats " +
            "SET consecutive_login_days = #{days}, " +
            "last_login_date = #{date}, " +
            "updated_at = NOW() " +
            "WHERE user_id = #{userId}")
    int updateConsecutiveLoginDays(@Param("userId") String userId, 
                                  @Param("days") Integer days, 
                                  @Param("date") LocalDate date);

    /**
     * 重置周扣分统计
     */
    @Update("UPDATE user_reputation_stats " +
            "SET weekly_deduct_points = 0, " +
            "last_weekly_reset = #{resetDate}, " +
            "updated_at = NOW() " +
            "WHERE last_weekly_reset IS NULL OR last_weekly_reset < #{resetDate}")
    int resetWeeklyDeductPoints(@Param("resetDate") LocalDate resetDate);

    /**
     * 重置月扣分统计
     */
    @Update("UPDATE user_reputation_stats " +
            "SET monthly_deduct_points = 0, " +
            "last_monthly_reset = #{resetDate}, " +
            "updated_at = NOW() " +
            "WHERE last_monthly_reset IS NULL OR last_monthly_reset < #{resetDate}")
    int resetMonthlyDeductPoints(@Param("resetDate") LocalDate resetDate);

    /**
     * 获取需要检查长期不活跃的用户
     */
    @Select("SELECT urs.* FROM user_reputation_stats urs " +
            "INNER JOIN users u ON urs.user_id = u.id " +
            "WHERE u.status = 'active' " +
            "AND (urs.protection_end_time IS NULL OR urs.protection_end_time <= NOW()) " +
            "AND (urs.last_login_date IS NULL OR urs.last_login_date < #{cutoffDate})")
    List<UserReputationStats> selectInactiveUsers(@Param("cutoffDate") LocalDate cutoffDate);
}
