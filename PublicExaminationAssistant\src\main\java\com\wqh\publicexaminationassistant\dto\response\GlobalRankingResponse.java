package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 全站排行榜响应DTO
 * 用于返回全站用户排行榜数据
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
public class GlobalRankingResponse {

    /**
     * 排名位置
     */
    private Integer rank;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 排行分数
     */
    private Integer score;

    /**
     * 信誉等级
     */
    private String reputationLevel;

    /**
     * 排名变化（相比上一周期）
     * 正数表示上升，负数表示下降，0表示无变化，null表示新上榜
     */
    private Integer rankChange;

    /**
     * 排行榜类型
     */
    private String rankingType;

    /**
     * 统计周期
     */
    private String period;

    /**
     * 计算时间
     */
    private LocalDateTime calculatedAt;

    /**
     * 是否为当前用户
     */
    private Boolean isCurrentUser;

    /**
     * 额外统计信息（根据排行榜类型不同而不同）
     */
    private ExtraStats extraStats;

    /**
     * 额外统计信息内部类
     */
    @Data
    public static class ExtraStats {
        /**
         * 题目总数（刷题数量排名时使用）
         */
        private Integer totalQuestions;

        /**
         * 正确题目数
         */
        private Integer correctQuestions;

        /**
         * 正确率（百分比）
         */
        private Double accuracyRate;

        /**
         * 学习时长（分钟）
         */
        private Integer studyTime;

        /**
         * 学习天数
         */
        private Integer studyDays;

        /**
         * 连续学习天数
         */
        private Integer consecutiveStudyDays;

        /**
         * 信誉分数
         */
        private Integer reputationScore;
    }
}
