package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传测试控制器
 * 用于测试文件上传功能是否正常
 * 
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@RestController
@RequestMapping("/v1/test")
@Tag(name = "文件上传测试", description = "文件上传功能测试接口")
public class FileTestController {

    @PostMapping("/upload")
    @Operation(summary = "测试文件上传", description = "简单的文件上传测试接口")
    public ApiResponse<Map<String, Object>> testUpload(
            @Parameter(description = "上传的文件") 
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "测试参数") 
            @RequestParam(value = "testParam", defaultValue = "test") String testParam) {
        
        log.info("测试文件上传: fileName={}, fileSize={}, contentType={}, testParam={}", 
                file.getOriginalFilename(), file.getSize(), file.getContentType(), testParam);
        
        Map<String, Object> result = new HashMap<>();
        result.put("fileName", file.getOriginalFilename());
        result.put("fileSize", file.getSize());
        result.put("contentType", file.getContentType());
        result.put("testParam", testParam);
        result.put("isEmpty", file.isEmpty());
        result.put("message", "文件上传测试成功");
        
        return ApiResponse.success(result, "文件上传测试成功");
    }

    @GetMapping("/ping")
    @Operation(summary = "测试连接", description = "测试服务器连接")
    public ApiResponse<String> ping() {
        log.info("收到ping请求");
        return ApiResponse.success("pong", "服务器连接正常");
    }
}
