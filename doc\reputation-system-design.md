# 信誉等级分数管理系统设计方案

## 📋 文档信息

- **文档版本**: v2.0
- **创建日期**: 2025-07-20
- **最后更新**: 2025-07-20
- **适用系统**: 考公刷题记录系统
- **设计目标**: 激励学习行为，约束消极行为，构建良性学习生态

---

## 🎯 设计理念与核心原则

### 核心原则

1. **学习导向** - 以促进学习为首要目标
2. **公平透明** - 所有规则公开透明，用户可预期
3. **渐进平衡** - 分数变化平滑，避免大起大落
4. **快速适应** - 缩短保护期，让用户快速融入系统

### 设计目标

- 鼓励每日坚持学习
- 促进小桌内良性竞争
- 约束消极学习行为
- 建立可持续的激励机制

---

## 📊 等级体系设计

### 等级划分

```
新手 (Newbie):     0-99分    - 绿色徽章
青铜 (Bronze):     100-299分  - 棕色徽章
白银 (Silver):     300-599分  - 银色徽章
黄金 (Gold):       600-999分  - 金色徽章
铂金 (Platinum):   1000-1999分 - 白金徽章
钻石 (Diamond):    2000-4999分 - 蓝色徽章
大师 (Master):     5000-9999分 - 红色徽章
宗师 (Grandmaster): 10000+分   - 紫色徽章
```

### 等级变化规则

- **晋升**: 达到等级分数线即可晋升，获得等级奖励分数
- **降级保护**: 刚晋升用户有 3 天保护期
- **降级条件**: 连续 3 天低于等级线且无保护期

---

## ⬆️ 加分机制设计

### 1. 每日学习奖励（主要激励）

```
基础学习奖励:
- 完成1-10题:    +1分
- 完成11-30题:   +2分
- 完成31-50题:   +3分
- 完成51-100题:  +5分
- 完成100题以上: +7分

学习质量奖励:
- 正确率70-79%:  +1分
- 正确率80-89%:  +2分
- 正确率90-95%:  +3分
- 正确率96-100%: +5分

连续学习奖励:
- 连续学习3天:   +2分
- 连续学习7天:   +5分
- 连续学习15天:  +10分
- 连续学习30天:  +20分
```

### 2. 每日登录奖励

```
基础登录:
- 每日首次登录:   +1分

连续登录奖励:
- 连续登录7天:    +3分（额外）
- 连续登录30天:   +10分（额外）
- 连续登录100天:  +25分（额外）
```

### 3. 小桌参与奖励（适度激励）

```
小桌管理:
- 创建小桌:       +10分（一次性）
- 成功邀请他人:   +3分/人（上限15分/月）

小桌表现:
- 小桌排行榜第1名: +5分/周
- 小桌排行榜前3名: +3分/周
- 小桌排行榜前50%: +1分/周
- 达到小桌学习目标: +2分/周
```

### 4. 社区贡献奖励

```
平台建设:
- 提交有效建议:   +5分
- 发现并报告Bug: +8分
- 参与平台活动:   +2分
```

---

## ⬇️ 扣分机制设计

### 1. 学习缺失扣分（核心约束）

```
每日学习检查（00:00自动执行）:
- 前一天无任何学习记录: -3分
- 连续2天无学习记录:    -5分（累计-8分）
- 连续3天无学习记录:    -8分（累计-16分）
- 连续7天无学习记录:    -15分（累计-31分）

注意: 新用户有3天保护期，保护期内不执行学习缺失扣分
```

### 2. 小桌违约扣分

```
小桌要求未达成:
- 未达到小桌最低题量要求: -5分/周
- 小桌排行榜倒数第1名:    -3分/周
- 小桌排行榜倒数前10%:    -1分/周

小桌责任:
- 桌长恶意解散小桌:       -20分
- 被踢出小桌（违规）:     -15分
- 频繁退出小桌:           -5分/次（30天内超过3次）
```

### 3. 平台违规扣分

```
行为违规:
- 发布不当内容:    -10分
- 恶意刷题作弊:    -25分
- 骚扰其他用户:    -15分
- 虚假信息:        -12分

严重违规:
- 账号被封禁:      -50分
- 恶意破坏平台:    -100分
```

### 4. 长期不活跃扣分

```
长期离线:
- 连续15天未登录: -10分
- 连续30天未登录: -20分
- 连续60天未登录: -40分
```

---

## ⏰ 定时任务实现方案

### 1. 每日学习检查任务

```java
@Component
@Slf4j
public class DailyStudyCheckTask {

    @Autowired
    private ReputationService reputationService;

    @Autowired
    private StudyRecordService studyRecordService;

    @Autowired
    private UserService userService;

    /**
     * 每日00:00执行学习检查任务
     * 检查前一天没有学习记录的用户并扣分
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void checkDailyStudy() {
        log.info("开始执行每日学习检查任务");

        LocalDate yesterday = LocalDate.now().minusDays(1);

        try {
            // 1. 获取所有活跃用户（排除保护期用户）
            List<User> activeUsers = userService.getActiveUsersExcludeProtected();

            // 2. 检查每个用户昨天的学习记录
            for (User user : activeUsers) {
                checkUserDailyStudy(user, yesterday);
            }

            log.info("每日学习检查任务完成，检查用户数: {}", activeUsers.size());

        } catch (Exception e) {
            log.error("每日学习检查任务执行失败", e);
        }
    }

    /**
     * 检查单个用户的每日学习情况
     */
    private void checkUserDailyStudy(User user, LocalDate checkDate) {
        // 检查指定日期是否有学习记录
        boolean hasStudyRecord = studyRecordService.hasStudyRecordOnDate(
            user.getId(), checkDate);

        if (!hasStudyRecord) {
            // 计算连续未学习天数
            int consecutiveDays = studyRecordService.getConsecutiveNoStudyDays(
                user.getId(), checkDate);

            // 根据连续天数扣分
            int deductPoints = calculateDeductPoints(consecutiveDays);

            if (deductPoints > 0) {
                reputationService.deductPoints(
                    user.getId(),
                    deductPoints,
                    String.format("连续%d天无学习记录", consecutiveDays),
                    "daily_study_check"
                );

                log.info("用户 {} 因连续{}天无学习记录被扣除{}分",
                    user.getUsername(), consecutiveDays, deductPoints);
            }
        }
    }

    /**
     * 根据连续未学习天数计算扣分
     */
    private int calculateDeductPoints(int consecutiveDays) {
        switch (consecutiveDays) {
            case 1: return 3;
            case 2: return 5;
            case 3: return 8;
            case 7: return 15;
            default: return consecutiveDays >= 7 ? 15 : 0;
        }
    }
}
```

### 2. 每周小桌表现检查任务

```java
@Component
@Slf4j
public class WeeklyDeskCheckTask {

    /**
     * 每周一00:30执行小桌表现检查
     */
    @Scheduled(cron = "0 30 0 ? * MON")
    public void checkWeeklyDeskPerformance() {
        log.info("开始执行每周小桌表现检查任务");

        LocalDate lastWeekStart = LocalDate.now().minusWeeks(1).with(DayOfWeek.MONDAY);
        LocalDate lastWeekEnd = lastWeekStart.plusDays(6);

        // 检查所有活跃小桌的成员表现
        List<Desk> activeDesks = deskService.getActiveDesks();

        for (Desk desk : activeDesks) {
            checkDeskMemberPerformance(desk, lastWeekStart, lastWeekEnd);
        }

        log.info("每周小桌表现检查任务完成");
    }
}
```

---

## 🔒 保护机制设计

### 1. 新用户保护期（3 天）

```java
@Service
public class UserProtectionService {

    /**
     * 检查用户是否在保护期内
     */
    public boolean isInProtectionPeriod(String userId) {
        User user = userService.getById(userId);
        LocalDateTime registrationTime = user.getCreatedAt();
        LocalDateTime protectionEndTime = registrationTime.plusDays(3);

        return LocalDateTime.now().isBefore(protectionEndTime);
    }

    /**
     * 获取保护期剩余时间
     */
    public Duration getProtectionTimeRemaining(String userId) {
        User user = userService.getById(userId);
        LocalDateTime protectionEndTime = user.getCreatedAt().plusDays(3);

        return Duration.between(LocalDateTime.now(), protectionEndTime);
    }
}
```

### 2. 分数变化限制

```
每日扣分上限: -15分
每周扣分上限: -50分
每月扣分上限: -100分
最低分数保护: 不低于0分
```

### 3. 恢复机制

```
学习恢复奖励:
- 连续3天正常学习: 恢复最近一次扣分的50%
- 连续7天正常学习: 恢复最近一次扣分的100%

小桌表现恢复:
- 小桌排名提升到前50%: 恢复小桌相关扣分的50%
```

---

## 💾 数据库设计

### 1. 信誉变更记录表

```sql
CREATE TABLE reputation_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    change_type ENUM('earn', 'deduct') NOT NULL,
    points INT NOT NULL,
    reason VARCHAR(200) NOT NULL,
    category ENUM('daily_login', 'daily_study', 'study_quality', 'desk_performance',
                  'desk_violation', 'platform_violation', 'inactive', 'recovery') NOT NULL,
    related_id VARCHAR(36) COMMENT '关联ID（小桌ID、学习记录ID等）',
    consecutive_days INT DEFAULT 0 COMMENT '连续天数（用于连续学习/未学习记录）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_by VARCHAR(50) DEFAULT 'system' COMMENT '处理方式：system/manual/admin',

    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_category (category),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2. 用户信誉统计表

```sql
CREATE TABLE user_reputation_stats (
    user_id VARCHAR(36) PRIMARY KEY,
    current_score INT DEFAULT 100,
    current_level VARCHAR(20) DEFAULT 'newbie',
    total_earned INT DEFAULT 0,
    total_deducted INT DEFAULT 0,

    -- 连续记录
    consecutive_login_days INT DEFAULT 0,
    consecutive_study_days INT DEFAULT 0,
    consecutive_no_study_days INT DEFAULT 0,

    -- 时间记录
    last_login_date DATE,
    last_study_date DATE,
    last_score_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    protection_end_time TIMESTAMP COMMENT '保护期结束时间',

    -- 统计数据
    weekly_deduct_points INT DEFAULT 0 COMMENT '本周已扣分数',
    monthly_deduct_points INT DEFAULT 0 COMMENT '本月已扣分数',
    last_weekly_reset DATE COMMENT '上次周重置日期',
    last_monthly_reset DATE COMMENT '上次月重置日期',

    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 3. 定时任务执行记录表

```sql
CREATE TABLE scheduled_task_logs (
    id VARCHAR(36) PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    execution_date DATE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status ENUM('running', 'completed', 'failed') NOT NULL,
    processed_count INT DEFAULT 0,
    error_message TEXT,

    INDEX idx_task_date (task_name, execution_date),
    INDEX idx_status (status)
);
```

---

## 🔧 核心服务实现

### 1. 信誉分数管理服务

```java
@Service
@Transactional
@Slf4j
public class ReputationService {

    @Autowired
    private ReputationLogMapper reputationLogMapper;

    @Autowired
    private UserReputationStatsMapper userReputationStatsMapper;

    @Autowired
    private UserProtectionService userProtectionService;

    /**
     * 增加信誉分数
     */
    public void addPoints(String userId, int points, String reason, String category) {
        addPoints(userId, points, reason, category, null, 0);
    }

    public void addPoints(String userId, int points, String reason, String category,
                         String relatedId, int consecutiveDays) {

        // 记录分数变更日志
        ReputationLog log = new ReputationLog();
        log.setUserId(userId);
        log.setChangeType("earn");
        log.setPoints(points);
        log.setReason(reason);
        log.setCategory(category);
        log.setRelatedId(relatedId);
        log.setConsecutiveDays(consecutiveDays);
        reputationLogMapper.insert(log);

        // 更新用户信誉统计
        updateUserReputationStats(userId, points, true);

        log.info("用户 {} 获得 {} 分，原因：{}", userId, points, reason);
    }

    /**
     * 扣除信誉分数
     */
    public void deductPoints(String userId, int points, String reason, String category) {
        deductPoints(userId, points, reason, category, null, 0);
    }

    public void deductPoints(String userId, int points, String reason, String category,
                           String relatedId, int consecutiveDays) {

        // 检查保护期
        if (userProtectionService.isInProtectionPeriod(userId)) {
            log.info("用户 {} 在保护期内，跳过扣分：{}", userId, reason);
            return;
        }

        // 检查扣分限制
        if (!canDeductPoints(userId, points)) {
            log.warn("用户 {} 达到扣分上限，跳过扣分：{}", userId, reason);
            return;
        }

        // 记录分数变更日志
        ReputationLog log = new ReputationLog();
        log.setUserId(userId);
        log.setChangeType("deduct");
        log.setPoints(points);
        log.setReason(reason);
        log.setCategory(category);
        log.setRelatedId(relatedId);
        log.setConsecutiveDays(consecutiveDays);
        reputationLogMapper.insert(log);

        // 更新用户信誉统计
        updateUserReputationStats(userId, points, false);

        log.info("用户 {} 被扣除 {} 分，原因：{}", userId, points, reason);
    }

    /**
     * 检查是否可以扣分（检查扣分限制）
     */
    private boolean canDeductPoints(String userId, int points) {
        UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
        if (stats == null) return true;

        // 检查每日扣分限制
        LocalDate today = LocalDate.now();
        int todayDeducted = reputationLogMapper.getTodayDeductedPoints(userId, today);
        if (todayDeducted + points > 15) {
            return false;
        }

        // 检查每周扣分限制
        if (stats.getWeeklyDeductPoints() + points > 50) {
            return false;
        }

        // 检查每月扣分限制
        if (stats.getMonthlyDeductPoints() + points > 100) {
            return false;
        }

        return true;
    }

    /**
     * 更新用户信誉统计
     */
    private void updateUserReputationStats(String userId, int points, boolean isAdd) {
        UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
        if (stats == null) {
            stats = initUserReputationStats(userId);
        }

        // 更新分数
        int newScore = isAdd ?
            stats.getCurrentScore() + points :
            Math.max(0, stats.getCurrentScore() - points);

        stats.setCurrentScore(newScore);

        // 更新累计统计
        if (isAdd) {
            stats.setTotalEarned(stats.getTotalEarned() + points);
        } else {
            stats.setTotalDeducted(stats.getTotalDeducted() + points);
            updateDeductLimits(stats, points);
        }

        // 更新等级
        String newLevel = calculateLevel(newScore);
        stats.setCurrentLevel(newLevel);

        stats.setLastScoreUpdate(LocalDateTime.now());
        userReputationStatsMapper.updateById(stats);
    }

    /**
     * 更新扣分限制统计
     */
    private void updateDeductLimits(UserReputationStats stats, int points) {
        LocalDate today = LocalDate.now();

        // 更新周扣分统计
        if (stats.getLastWeeklyReset() == null ||
            !stats.getLastWeeklyReset().equals(today.with(DayOfWeek.MONDAY))) {
            stats.setWeeklyDeductPoints(points);
            stats.setLastWeeklyReset(today.with(DayOfWeek.MONDAY));
        } else {
            stats.setWeeklyDeductPoints(stats.getWeeklyDeductPoints() + points);
        }

        // 更新月扣分统计
        if (stats.getLastMonthlyReset() == null ||
            !stats.getLastMonthlyReset().equals(today.withDayOfMonth(1))) {
            stats.setMonthlyDeductPoints(points);
            stats.setLastMonthlyReset(today.withDayOfMonth(1));
        } else {
            stats.setMonthlyDeductPoints(stats.getMonthlyDeductPoints() + points);
        }
    }

    /**
     * 根据分数计算等级
     */
    private String calculateLevel(int score) {
        if (score < 100) return "newbie";
        if (score < 300) return "bronze";
        if (score < 600) return "silver";
        if (score < 1000) return "gold";
        if (score < 2000) return "platinum";
        if (score < 5000) return "diamond";
        if (score < 10000) return "master";
        return "grandmaster";
    }

    /**
     * 初始化用户信誉统计
     */
    private UserReputationStats initUserReputationStats(String userId) {
        UserReputationStats stats = new UserReputationStats();
        stats.setUserId(userId);
        stats.setCurrentScore(100); // 初始分数
        stats.setCurrentLevel("newbie");
        stats.setProtectionEndTime(LocalDateTime.now().plusDays(3));
        userReputationStatsMapper.insert(stats);
        return stats;
    }
}
```

### 2. 学习记录检查服务

```java
@Service
@Slf4j
public class StudyRecordService {

    @Autowired
    private StudyRecordMapper studyRecordMapper;

    /**
     * 检查指定日期是否有学习记录
     */
    public boolean hasStudyRecordOnDate(String userId, LocalDate date) {
        return studyRecordMapper.countByUserIdAndDate(userId, date) > 0;
    }

    /**
     * 获取连续无学习记录的天数
     */
    public int getConsecutiveNoStudyDays(String userId, LocalDate endDate) {
        int consecutiveDays = 0;
        LocalDate checkDate = endDate;

        // 向前查找连续无学习记录的天数
        while (consecutiveDays < 30) { // 最多查找30天
            if (hasStudyRecordOnDate(userId, checkDate)) {
                break;
            }
            consecutiveDays++;
            checkDate = checkDate.minusDays(1);
        }

        return consecutiveDays;
    }

    /**
     * 获取连续学习天数
     */
    public int getConsecutiveStudyDays(String userId, LocalDate endDate) {
        int consecutiveDays = 0;
        LocalDate checkDate = endDate;

        // 向前查找连续学习的天数
        while (consecutiveDays < 365) { // 最多查找365天
            if (!hasStudyRecordOnDate(userId, checkDate)) {
                break;
            }
            consecutiveDays++;
            checkDate = checkDate.minusDays(1);
        }

        return consecutiveDays;
    }
}
```

---

## 📱 前端界面设计

### 1. 信誉中心页面组件

```typescript
interface ReputationCenterProps {
  userId: string
}

interface ReputationStats {
  currentScore: number
  currentLevel: string
  totalEarned: number
  totalDeducted: number
  weeklyChange: number
  protectionTimeRemaining?: number
}

interface ReputationLog {
  id: string
  changeType: 'earn' | 'deduct'
  points: number
  reason: string
  category: string
  createdAt: string
}

const ReputationCenter: React.FC<ReputationCenterProps> = ({ userId }) => {
  const [stats, setStats] = useState<ReputationStats | null>(null)
  const [logs, setLogs] = useState<ReputationLog[]>([])
  const [loading, setLoading] = useState(true)

  // 组件实现...
}
```

### 2. 信誉变更通知组件

```typescript
interface ReputationNotificationProps {
  type: 'earn' | 'deduct' | 'level_up' | 'level_down'
  points?: number
  reason: string
  newLevel?: string
}

const ReputationNotification: React.FC<ReputationNotificationProps> = ({
  type,
  points,
  reason,
  newLevel,
}) => {
  // 通知组件实现...
}
```

---

## 🚀 实施计划

### 第一阶段：基础框架（1-2 周）

1. 数据库表结构创建
2. 基础服务类实现
3. 定时任务框架搭建
4. 基础前端页面

### 第二阶段：核心功能（2-3 周）

1. 每日学习检查任务
2. 加分扣分逻辑实现
3. 保护机制实现
4. 前端信誉中心页面

### 第三阶段：优化完善（1-2 周）

1. 性能优化
2. 异常处理完善
3. 用户界面优化
4. 测试和调试

### 第四阶段：监控运营（持续）

1. 数据监控和分析
2. 参数调优
3. 用户反馈收集
4. 功能迭代优化

---

## 📊 监控指标

### 系统监控指标

- 每日活跃学习用户数
- 平均每日学习题量
- 信誉分数分布情况
- 扣分用户比例
- 定时任务执行状态

### 用户行为指标

- 连续学习天数分布
- 小桌参与活跃度
- 等级晋升速度
- 用户流失率变化

---

## ⚠️ 风险控制

### 技术风险

- 定时任务执行失败处理
- 大量用户并发处理
- 数据一致性保证

### 业务风险

- 扣分过严导致用户流失
- 加分过松失去激励效果
- 恶意刷分行为

### 应对措施

- 完善的日志记录和监控
- 灰度发布和 A/B 测试
- 用户反馈快速响应机制
