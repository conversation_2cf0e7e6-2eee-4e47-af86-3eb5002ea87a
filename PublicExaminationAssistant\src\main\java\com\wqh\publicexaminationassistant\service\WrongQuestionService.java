package com.wqh.publicexaminationassistant.service;

import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.request.WrongQuestionRequest;
import com.wqh.publicexaminationassistant.dto.response.ImageUploadResponse;
import com.wqh.publicexaminationassistant.dto.response.WrongQuestionResponse;
import com.wqh.publicexaminationassistant.entity.StudyRecord;
import com.wqh.publicexaminationassistant.entity.WrongQuestion;
import com.wqh.publicexaminationassistant.mapper.StudyRecordMapper;
import com.wqh.publicexaminationassistant.mapper.WrongQuestionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 错题管理服务类
 * 提供错题的业务逻辑处理
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WrongQuestionService {

    private final WrongQuestionMapper wrongQuestionMapper;
    private final StudyRecordMapper studyRecordMapper;
    private final WrongQuestionImageService wrongQuestionImageService;

    /**
     * 创建错题
     */
    @Transactional
    public WrongQuestionResponse createWrongQuestion(WrongQuestionRequest request, String userId) {
        log.info("开始创建错题: userId={}, questionType={}, moduleType={}",
                userId, request.getQuestionType(), request.getModuleType());

        // 智能关联学习记录
        StudyRecord studyRecord = findOrCreateStudyRecord(request, userId);

        // 如果找到了学习记录，设置关联ID
        if (studyRecord != null) {
            request.setStudyRecordId(studyRecord.getId());
        }

        // 创建错题实体
        WrongQuestion wrongQuestion = new WrongQuestion();
        BeanUtils.copyProperties(request, wrongQuestion);
        wrongQuestion.setUserId(userId);
        wrongQuestion.setReviewCount(0);
        wrongQuestion.setCreatedAt(LocalDateTime.now());

        // 显式设置学习模块类型
        if (StringUtils.hasText(request.getModuleType())) {
            wrongQuestion.setModuleType(request.getModuleType());
        }

        // 设置默认掌握状态
        if (!StringUtils.hasText(wrongQuestion.getMasteryStatus())) {
            wrongQuestion.setMasteryStatus("not_mastered");
        }

        // 保存到数据库
        int result = wrongQuestionMapper.insert(wrongQuestion);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR);
        }

        log.info("错题创建成功: wrongQuestionId={}", wrongQuestion.getId());

        // 构建响应对象
        return buildWrongQuestionResponse(wrongQuestion, studyRecord);
    }

    /**
     * 智能关联学习记录
     * 优先级：1. 明确指定的studyRecordId 2. 根据moduleType查找最近的学习记录 3. 不关联
     */
    private StudyRecord findOrCreateStudyRecord(WrongQuestionRequest request, String userId) {
        // 1. 如果明确指定了studyRecordId，验证并使用
        if (StringUtils.hasText(request.getStudyRecordId())) {
            StudyRecord studyRecord = studyRecordMapper.selectById(request.getStudyRecordId());
            if (studyRecord == null || !studyRecord.getUserId().equals(userId)) {
                throw new BusinessException(ResultCode.RECORD_NOT_FOUND);
            }
            return studyRecord;
        }

        // 2. 如果提供了moduleType，查找该模块最近的学习记录
        if (StringUtils.hasText(request.getModuleType())) {
            StudyRecord recentRecord = studyRecordMapper.findRecentByUserIdAndModuleType(userId, request.getModuleType());
            if (recentRecord != null) {
                log.info("找到最近的学习记录: recordId={}, moduleType={}",
                        recentRecord.getId(), recentRecord.getModuleType());
                return recentRecord;
            }
        }

        // 3. 没有找到合适的学习记录，返回null（创建独立错题）
        log.info("未找到合适的学习记录，将创建独立错题");
        return null;
    }

    /**
     * 根据ID获取错题详情
     */
    public WrongQuestionResponse getWrongQuestionById(String wrongQuestionId, String userId) {
        log.info("查询错题详情: wrongQuestionId={}, userId={}", wrongQuestionId, userId);

        WrongQuestion wrongQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        if (wrongQuestion == null || !wrongQuestion.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        // 获取关联的学习记录
        StudyRecord studyRecord = null;
        if (StringUtils.hasText(wrongQuestion.getStudyRecordId())) {
            studyRecord = studyRecordMapper.selectById(wrongQuestion.getStudyRecordId());
        }

        return buildWrongQuestionResponse(wrongQuestion, studyRecord);
    }

    /**
     * 更新错题
     */
    @Transactional
    public WrongQuestionResponse updateWrongQuestion(String wrongQuestionId, WrongQuestionRequest request, String userId) {
        log.info("更新错题: wrongQuestionId={}, userId={}", wrongQuestionId, userId);

        // 检查错题是否存在且属于当前用户
        WrongQuestion existingQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        if (existingQuestion == null || !existingQuestion.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        // 验证关联的学习记录（如果提供了studyRecordId）
        StudyRecord studyRecord = null;
        if (StringUtils.hasText(request.getStudyRecordId())) {
            studyRecord = studyRecordMapper.selectById(request.getStudyRecordId());
            if (studyRecord == null || !studyRecord.getUserId().equals(userId)) {
                throw new BusinessException(ResultCode.RECORD_NOT_FOUND);
            }
        }

        // 更新错题信息
        BeanUtils.copyProperties(request, existingQuestion);
        existingQuestion.setId(wrongQuestionId); // 确保ID不被覆盖
        existingQuestion.setUserId(userId); // 确保用户ID不被覆盖

        int result = wrongQuestionMapper.updateById(existingQuestion);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR);
        }

        log.info("错题更新成功: wrongQuestionId={}", wrongQuestionId);

        return buildWrongQuestionResponse(existingQuestion, studyRecord);
    }

    /**
     * 删除错题（包括关联的图片）
     */
    @Transactional
    public void deleteWrongQuestion(String wrongQuestionId, String userId) {
        log.info("删除错题: wrongQuestionId={}, userId={}", wrongQuestionId, userId);

        // 检查错题是否存在且属于当前用户
        WrongQuestion wrongQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        if (wrongQuestion == null || !wrongQuestion.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        try {
            // 1. 先删除关联的图片（包括文件和数据库记录）
            List<ImageUploadResponse> images = wrongQuestionImageService.getImagesByWrongQuestionId(wrongQuestionId, userId);
            for (ImageUploadResponse image : images) {
                try {
                    wrongQuestionImageService.deleteImage(image.getId(), userId);
                    log.debug("删除错题关联图片成功: imageId={}", image.getId());
                } catch (Exception e) {
                    log.warn("删除错题关联图片失败: imageId={}, error={}", image.getId(), e.getMessage());
                    // 图片删除失败不影响错题删除，继续执行
                }
            }

            // 2. 删除错题记录
            int result = wrongQuestionMapper.deleteById(wrongQuestionId);
            if (result <= 0) {
                throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "错题删除失败");
            }

            log.info("错题删除成功: wrongQuestionId={}, 删除关联图片数量={}", wrongQuestionId, images.size());

        } catch (BusinessException e) {
            throw e; // 重新抛出业务异常
        } catch (Exception e) {
            log.error("删除错题失败: wrongQuestionId={}", wrongQuestionId, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除错题失败: " + e.getMessage());
        }
    }

    /**
     * 增加错题复习次数
     */
    @Transactional
    public void incrementReviewCount(String wrongQuestionId, String userId) {
        log.info("增加错题复习次数: wrongQuestionId={}, userId={}", wrongQuestionId, userId);

        // 检查错题是否存在且属于当前用户
        WrongQuestion existingQuestion = wrongQuestionMapper.selectById(wrongQuestionId);
        if (existingQuestion == null || !existingQuestion.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.WRONG_QUESTION_NOT_FOUND);
        }

        // 增加复习次数
        int result = wrongQuestionMapper.incrementReviewCount(wrongQuestionId, userId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "复习次数更新失败");
        }

        log.info("错题复习次数增加成功: wrongQuestionId={}, 当前复习次数={}",
                wrongQuestionId, existingQuestion.getReviewCount() + 1);
    }

    /**
     * 构建错题响应对象
     */
    private WrongQuestionResponse buildWrongQuestionResponse(WrongQuestion wrongQuestion, StudyRecord studyRecord) {
        WrongQuestionResponse response = new WrongQuestionResponse();
        BeanUtils.copyProperties(wrongQuestion, response);

        // 设置中文显示名称
        response.setQuestionTypeName(getQuestionTypeName(wrongQuestion.getQuestionType()));
        response.setDifficultyLevelName(getDifficultyLevelName(wrongQuestion.getDifficultyLevel()));
        response.setMasteryStatusName(getMasteryStatusName(wrongQuestion.getMasteryStatus()));

        // 设置学习模块信息（优先级：错题直接字段 > 学习记录字段 > 未分类）
        String effectiveModuleType = determineEffectiveModuleType(wrongQuestion, studyRecord);
        if (StringUtils.hasText(effectiveModuleType)) {
            response.setModuleType(effectiveModuleType);
            response.setModuleName(getModuleName(effectiveModuleType));
        }

        // 设置关联的学习记录信息
        if (studyRecord != null) {
            WrongQuestionResponse.StudyRecordBasicInfo studyRecordInfo = new WrongQuestionResponse.StudyRecordBasicInfo();
            studyRecordInfo.setId(studyRecord.getId());
            studyRecordInfo.setModuleType(studyRecord.getModuleType());
            studyRecordInfo.setModuleName(getModuleName(studyRecord.getModuleType()));
            studyRecordInfo.setStudyDate(studyRecord.getStudyDate());
            response.setStudyRecord(studyRecordInfo);
        }

        // 设置图片信息
        try {
            List<ImageUploadResponse> images = wrongQuestionImageService.getImagesByWrongQuestionId(
                    wrongQuestion.getId(), wrongQuestion.getUserId());
            response.setImages(images);
        } catch (Exception e) {
            log.warn("获取错题图片信息失败: wrongQuestionId={}", wrongQuestion.getId(), e);
            // 图片信息获取失败不影响主要功能，设置为空列表
            response.setImages(new ArrayList<ImageUploadResponse>());
        }

        return response;
    }

    /**
     * 确定有效的学习模块类型
     * 优先级：错题直接字段 > 学习记录字段 > null
     *
     * @param wrongQuestion 错题实体
     * @param studyRecord 关联的学习记录（可能为null）
     * @return 有效的模块类型，如果都为空则返回null
     */
    private String determineEffectiveModuleType(WrongQuestion wrongQuestion, StudyRecord studyRecord) {
        // 1. 优先使用错题表中的module_type字段
        if (StringUtils.hasText(wrongQuestion.getModuleType())) {
            return wrongQuestion.getModuleType();
        }

        // 2. 如果错题表中没有，则使用关联学习记录中的module_type
        if (studyRecord != null && StringUtils.hasText(studyRecord.getModuleType())) {
            return studyRecord.getModuleType();
        }

        // 3. 都没有则返回null，前端会显示"未分类"
        return null;
    }

    /**
     * 获取学习模块名称
     */
    private String getModuleName(String moduleType) {
        if (moduleType == null) return "";
        Map<String, String> moduleNames = new HashMap<>();
        moduleNames.put("math", "数学运算");
        moduleNames.put("logic", "逻辑推理");
        moduleNames.put("language", "言语理解");
        moduleNames.put("knowledge", "常识判断");
        moduleNames.put("essay", "申论写作");
        return moduleNames.getOrDefault(moduleType, moduleType);
    }

    /**
     * 获取题目类型名称
     */
    private String getQuestionTypeName(String questionType) {
        if (questionType == null) return "";
        Map<String, String> typeNames = new HashMap<>();
        typeNames.put("single_choice", "单选题");
        typeNames.put("multiple_choice", "多选题");
        typeNames.put("judgment", "判断题");
        typeNames.put("fill_blank", "填空题");
        typeNames.put("essay", "论述题");
        return typeNames.getOrDefault(questionType, questionType);
    }

    /**
     * 获取难度等级名称
     */
    private String getDifficultyLevelName(String difficultyLevel) {
        if (difficultyLevel == null) return "";
        Map<String, String> levelNames = new HashMap<>();
        levelNames.put("easy", "简单");
        levelNames.put("medium", "中等");
        levelNames.put("hard", "困难");
        return levelNames.getOrDefault(difficultyLevel, difficultyLevel);
    }

    /**
     * 获取掌握状态名称
     */
    private String getMasteryStatusName(String masteryStatus) {
        if (masteryStatus == null) return "";
        Map<String, String> statusNames = new HashMap<>();
        statusNames.put("not_mastered", "未掌握");
        statusNames.put("reviewing", "复习中");
        statusNames.put("mastered", "已掌握");
        return statusNames.getOrDefault(masteryStatus, masteryStatus);
    }
}
