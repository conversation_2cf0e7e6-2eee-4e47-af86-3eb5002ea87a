import React from 'react';
import { Card } from '@douyinfe/semi-ui';
import Navigation from '../components/Navigation';

const ReputationCenterSimple: React.FC = () => {
  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'var(--paper-bg)',
      paddingTop: '80px'
    }}>
      <Navigation />
      
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '20px' 
      }}>
        <h1 style={{
          fontFamily: 'var(--font-handwritten)',
          fontSize: '32px',
          color: 'var(--ink-dark)',
          textAlign: 'center',
          marginBottom: '30px',
          transform: 'rotate(-1deg)'
        }}>
          ⭐ 我的信誉中心 ⭐
        </h1>

        <Card 
          style={{
            background: 'var(--paper-warm)',
            border: '2px solid var(--ink-dark)',
            borderRadius: '15px',
            padding: '30px',
            textAlign: 'center',
            transform: 'rotate(-0.5deg)',
            boxShadow: '5px 5px 0px var(--shadow-medium)'
          }}
        >
          <div style={{ fontSize: '48px', marginBottom: '20px' }}>
            🏆
          </div>
          
          <h2 style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '24px',
            color: 'var(--ink-dark)',
            marginBottom: '15px'
          }}>
            信誉系统开发中...
          </h2>
          
          <p style={{
            fontFamily: 'var(--font-handwritten)',
            fontSize: '16px',
            color: 'var(--ink-medium)',
            lineHeight: '1.6'
          }}>
            🚧 信誉中心页面正在开发中<br/>
            📊 功能包括：信誉分数、等级系统、记录查看<br/>
            ⏰ 敬请期待完整版本！
          </p>
          
          <div style={{ 
            marginTop: '30px',
            display: 'flex',
            justifyContent: 'center',
            gap: '15px',
            flexWrap: 'wrap'
          }}>
            <div style={{
              background: 'var(--accent-blue)',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '20px',
              fontFamily: 'var(--font-handwritten)',
              transform: 'rotate(1deg)'
            }}>
              当前分数: 100
            </div>
            
            <div style={{
              background: 'var(--accent-green)',
              color: 'white',
              padding: '10px 20px',
              borderRadius: '20px',
              fontFamily: 'var(--font-handwritten)',
              transform: 'rotate(-1deg)'
            }}>
              等级: 新手
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ReputationCenterSimple;
