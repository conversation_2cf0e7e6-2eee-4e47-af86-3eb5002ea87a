-- =====================================================
-- 错题图片功能数据库迁移验证脚本
-- 版本: 001
-- 创建时间: 2024-07-19
-- 描述: 验证错题图片功能数据库迁移是否成功
-- =====================================================

-- 1. 验证表是否创建成功
SELECT 
  TABLE_NAME,
  TABLE_COMMENT,
  ENGINE,
  TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('wrong_question_images', 'file_upload_logs');

-- 2. 验证表结构
DESCRIBE `wrong_question_images`;
DESCRIBE `file_upload_logs`;

-- 3. 验证索引是否创建
SELECT 
  TABLE_NAME,
  INDEX_NAME,
  COLUMN_NAME,
  NON_UNIQUE,
  INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wrong_question_images'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 4. 验证外键约束
SELECT 
  CONSTRAINT_NAME,
  TABLE_NAME,
  COLUMN_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME,
  DELETE_RULE,
  UPDATE_RULE
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'wrong_question_images'
  AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 5. 验证检查约束（兼容MySQL 5.7和8.0+）
-- MySQL 8.0+支持CHECK约束查询
SET @mysql_version = (SELECT SUBSTRING_INDEX(VERSION(), '.', 2));
SET @mysql_major = CAST(SUBSTRING_INDEX(@mysql_version, '.', 1) AS UNSIGNED);
SET @mysql_minor = CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(@mysql_version, '.', 2), '.', -1) AS UNSIGNED);

SELECT
  CASE
    WHEN @mysql_major > 8 OR (@mysql_major = 8 AND @mysql_minor >= 0) THEN 'MySQL 8.0+ - 支持CHECK约束查询'
    ELSE 'MySQL 5.7 - CHECK约束在表定义中，请手动验证'
  END AS check_constraint_support,
  @mysql_version AS mysql_version;

-- 仅在MySQL 8.0+中执行CHECK约束查询
-- 注意：在MySQL 5.7中此查询会失败，这是正常的
-- SELECT CONSTRAINT_NAME, CHECK_CLAUSE FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS
-- WHERE CONSTRAINT_SCHEMA = DATABASE() AND TABLE_NAME = 'wrong_question_images';

-- 6. 验证应用配置说明
-- 注意：文件上传配置在应用配置文件(application.yml)中管理
-- 请确认以下配置项已正确设置：
-- - app.upload.max-size: 10MB
-- - app.upload.allowed-types: jpg,jpeg,png,gif,webp
-- - app.upload.path: /uploads
-- - app.image.max-width: 1920
-- - app.image.max-height: 1080
-- - app.image.quality: 85
-- - app.thumbnail.width: 200
-- - app.thumbnail.height: 200
SELECT 'Please check application.yml for file upload configuration' AS config_note;

-- 7. 测试基本CRUD操作
-- 注意：这些是测试语句，实际执行时请谨慎

-- 测试插入（需要有效的wrong_question_id）
/*
INSERT INTO `wrong_question_images` (
  `id`, `wrong_question_id`, `image_type`, `file_name`, 
  `file_path`, `file_size`, `mime_type`, `sort_order`
) VALUES (
  UUID(), 'test-wrong-question-id', 'question', 'test.jpg',
  '/uploads/test.jpg', 1024, 'image/jpeg', 1
);
*/

-- 测试查询
/*
SELECT * FROM `wrong_question_images` WHERE `wrong_question_id` = 'test-wrong-question-id';
*/

-- 测试删除
/*
DELETE FROM `wrong_question_images` WHERE `wrong_question_id` = 'test-wrong-question-id';
*/

-- 8. 性能测试查询（模拟实际使用场景）
EXPLAIN SELECT 
  wq.id,
  wq.question_content,
  wqi.file_path,
  wqi.image_type,
  wqi.sort_order
FROM `wrong_questions` wq
LEFT JOIN `wrong_question_images` wqi ON wq.id = wqi.wrong_question_id
WHERE wq.user_id = 'test-user-id'
  AND wqi.image_type = 'question'
ORDER BY wqi.sort_order;

-- 9. 存储空间统计
SELECT 
  TABLE_NAME,
  ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
  TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('wrong_questions', 'wrong_question_images', 'file_upload_logs');

-- 10. 验证完成提示
SELECT 
  '错题图片功能数据库迁移验证完成' AS status,
  NOW() AS verification_time;
