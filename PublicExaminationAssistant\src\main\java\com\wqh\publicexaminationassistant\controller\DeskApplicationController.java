package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.request.ApplyDeskRequest;
import com.wqh.publicexaminationassistant.dto.request.ProcessApplicationRequest;
import com.wqh.publicexaminationassistant.dto.response.DeskApplicationResponse;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.DeskApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 小桌申请管理控制器
 * 提供加入申请、审核处理、申请历史等API
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@RestController
@RequestMapping("/v1/desk-applications")
@RequiredArgsConstructor
@Tag(name = "小桌申请管理", description = "加入申请、审核处理、申请历史等功能")
public class DeskApplicationController {

    private final DeskApplicationService deskApplicationService;

    /**
     * 申请加入小桌
     */
    @PostMapping
    @Operation(summary = "申请加入小桌", description = "提交加入小桌的申请")
    public ApiResponse<DeskApplicationResponse> applyToJoinDesk(
            @Valid @RequestBody ApplyDeskRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户申请加入小桌: userId={}, deskId={}", userDetails.getId(), request.getDeskId());
        
        DeskApplicationResponse response = deskApplicationService.applyToJoinDesk(request, userDetails.getId());
        return ApiResponse.success(response, "申请提交成功");
    }

    /**
     * 获取我的申请历史
     */
    @GetMapping
    @Operation(summary = "获取申请历史", description = "获取当前用户的所有申请历史")
    public ApiResponse<List<DeskApplicationResponse>> getMyApplications(
            @Parameter(description = "申请状态筛选（pending/approved/rejected）") 
            @RequestParam(required = false) String status,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查看申请历史: userId={}, status={}", userDetails.getId(), status);
        
        List<DeskApplicationResponse> response = deskApplicationService.getMyApplications(userDetails.getId(), status);
        return ApiResponse.success(response, "获取申请历史成功");
    }

    /**
     * 处理申请
     */
    @PutMapping("/{applicationId}/process")
    @Operation(summary = "处理申请", description = "桌长审核成员加入申请（通过或拒绝）")
    public ApiResponse<Void> processApplication(
            @Parameter(description = "申请ID") 
            @PathVariable String applicationId,
            @Valid @RequestBody ProcessApplicationRequest request,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户处理申请: operatorId={}, applicationId={}, action={}", 
                userDetails.getId(), applicationId, request.getAction());
        
        deskApplicationService.processApplication(applicationId, request, userDetails.getId());
        return ApiResponse.success(null, "申请处理成功");
    }

    /**
     * 取消申请
     */
    @DeleteMapping("/{applicationId}")
    @Operation(summary = "取消申请", description = "申请者取消自己的待处理申请")
    public ApiResponse<Void> cancelApplication(
            @Parameter(description = "申请ID") 
            @PathVariable String applicationId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户取消申请: userId={}, applicationId={}", userDetails.getId(), applicationId);
        
        deskApplicationService.cancelApplication(applicationId, userDetails.getId());
        return ApiResponse.success(null, "申请取消成功");
    }

    /**
     * 获取小桌的申请列表
     */
    @GetMapping("/desk/{deskId}")
    @Operation(summary = "获取小桌申请列表", description = "获取指定小桌的所有申请（仅桌长可查看）")
    public ApiResponse<List<DeskApplicationResponse>> getApplicationsByDesk(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @Parameter(description = "申请状态筛选（pending/approved/rejected）") 
            @RequestParam(required = false) String status,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查看小桌申请列表: userId={}, deskId={}, status={}", 
                userDetails.getId(), deskId, status);
        
        List<DeskApplicationResponse> response = deskApplicationService.getApplicationsByDesk(deskId, userDetails.getId(), status);
        return ApiResponse.success(response, "获取申请列表成功");
    }
}
