package com.wqh.publicexaminationassistant.common.result;

import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Getter
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 (400-499)
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),
    
    // 服务端错误 (500-599)
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 (1000-9999)
    
    // 用户模块错误 (1000-1999)
    USER_NOT_FOUND(1001, "用户不存在"),
    USERNAME_ALREADY_EXISTS(1002, "用户名已存在"),
    EMAIL_ALREADY_EXISTS(1003, "邮箱已存在"),
    INVALID_INVITE_CODE(1004, "邀请码无效"),
    INVITE_CODE_USED(1005, "邀请码已使用"),
    INVALID_CREDENTIALS(1006, "用户名或密码错误"),
    ACCOUNT_DISABLED(1007, "账户已被禁用"),
    TOKEN_EXPIRED(1008, "Token已过期"),
    INSUFFICIENT_PERMISSIONS(1009, "权限不足"),
    INVALID_OLD_PASSWORD(1010, "旧密码错误"),
    
    // 刷题记录模块错误 (2000-2999)
    RECORD_NOT_FOUND(2001, "学习记录不存在"),
    WRONG_QUESTION_NOT_FOUND(2002, "错题不存在"),
    STUDY_PLAN_NOT_FOUND(2003, "学习计划不存在"),
    INVALID_STUDY_DATA(2004, "学习数据无效"),
    DUPLICATE_STUDY_RECORD(2005, "同一天同一模块已有学习记录"),
    INVALID_ACCURACY_RATE(2006, "正确率计算错误"),
    INVALID_DATE_RANGE(2007, "日期范围无效"),
    STUDY_RECORD_ACCESS_DENIED(2008, "无权限访问该学习记录"),
    WRONG_QUESTION_ACCESS_DENIED(2009, "无权限访问该错题"),
    INVALID_MODULE_TYPE(2010, "无效的模块类型"),
    INVALID_QUESTION_TYPE(2011, "无效的题目类型"),
    INVALID_DIFFICULTY_LEVEL(2012, "无效的难度等级"),
    INVALID_MASTERY_STATUS(2013, "无效的掌握状态"),
    INVALID_STATISTICS_PERIOD(2014, "无效的统计周期"),
    WEAK_POINTS_LIMIT_EXCEEDED(2015, "薄弱知识点数量超出限制"),
    
    // 小桌系统模块错误 (3000-3999)
    DESK_NOT_FOUND(3001, "小桌不存在"),
    DESK_MEMBER_LIMIT(3002, "小桌成员已达上限"),
    NOT_DESK_MEMBER(3003, "非小桌成员"),
    NOT_DESK_OWNER(3004, "非小桌桌长"),
    ALREADY_DESK_MEMBER(3005, "已是小桌成员"),
    APPLICATION_NOT_FOUND(3006, "申请不存在"),
    APPLICATION_ALREADY_PROCESSED(3007, "申请已处理"),
    OWNER_CANNOT_LEAVE(3008, "桌长不能退出小桌"),
    
    // 信誉系统模块错误 (4000-4999)
    REPUTATION_LOG_NOT_FOUND(4001, "信誉记录不存在"),
    INVALID_REPUTATION_ACTION(4002, "无效的信誉行为"),
    REPORT_NOT_FOUND(4003, "举报不存在"),
    REPORT_ALREADY_HANDLED(4004, "举报已处理"),
    
    // 排行榜模块错误 (5000-5999)
    INVALID_RANKING_TYPE(5001, "无效的排行榜类型"),
    INVALID_PERIOD(5002, "无效的时间周期"),
    
    // 考试公告模块错误 (6000-6999)
    ANNOUNCEMENT_NOT_FOUND(6001, "公告不存在"),
    ANNOUNCEMENT_ALREADY_PUBLISHED(6002, "公告已发布"),
    ANNOUNCEMENT_ARCHIVED(6003, "公告已归档"),
    INVALID_IMPORTANT_DATES(6004, "重要日期格式错误"),
    REMINDER_ALREADY_EXISTS(6005, "提醒已存在"),
    REMINDER_NOT_FOUND(6006, "提醒不存在"),
    
    // 通知系统模块错误 (7000-7999)
    NOTIFICATION_NOT_FOUND(7001, "通知不存在"),
    NOTIFICATION_ALREADY_READ(7002, "通知已读"),
    INVALID_NOTIFICATION_TYPE(7003, "无效的通知类型"),
    PUSH_DEVICE_NOT_REGISTERED(7004, "推送设备未注册"),

    // 文件上传模块错误 (8000-8999)
    INVALID_PARAM(8001, "参数无效"),
    FILE_UPLOAD_FAILED(8002, "文件上传失败"),
    FILE_NOT_FOUND(8003, "文件不存在"),
    FILE_DELETE_FAILED(8004, "文件删除失败"),
    INVALID_FILE_TYPE(8005, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(8006, "文件大小超出限制"),
    INVALID_FILE_FORMAT(8007, "文件格式无效"),
    IMAGE_PROCESSING_FAILED(8008, "图片处理失败"),
    THUMBNAIL_GENERATION_FAILED(8009, "缩略图生成失败"),
    FILE_STORAGE_ERROR(8010, "文件存储错误"),
    ACCESS_DENIED(8011, "访问被拒绝");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态消息
     */
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
