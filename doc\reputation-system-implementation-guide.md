# 信誉等级分数管理系统实施指南

## 📋 快速开始

本文档是 `reputation-system-design.md` 的实施指南，提供具体的开发步骤和代码示例。

---

## 🗄️ 数据库初始化

### 1. 创建信誉相关表

```sql
-- 执行顺序：先创建 reputation_logs 表
CREATE TABLE reputation_logs (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    change_type ENUM('earn', 'deduct') NOT NULL,
    points INT NOT NULL,
    reason VARCHAR(200) NOT NULL,
    category ENUM('daily_login', 'daily_study', 'study_quality', 'desk_performance', 
                  'desk_violation', 'platform_violation', 'inactive', 'recovery') NOT NULL,
    related_id VARCHAR(36) COMMENT '关联ID',
    consecutive_days INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_by VARCHAR(50) DEFAULT 'system',
    
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_category (category),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 然后创建用户信誉统计表
CREATE TABLE user_reputation_stats (
    user_id VARCHAR(36) PRIMARY KEY,
    current_score INT DEFAULT 100,
    current_level VARCHAR(20) DEFAULT 'newbie',
    total_earned INT DEFAULT 0,
    total_deducted INT DEFAULT 0,
    consecutive_login_days INT DEFAULT 0,
    consecutive_study_days INT DEFAULT 0,
    consecutive_no_study_days INT DEFAULT 0,
    last_login_date DATE,
    last_study_date DATE,
    last_score_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    protection_end_time TIMESTAMP COMMENT '保护期结束时间',
    weekly_deduct_points INT DEFAULT 0,
    monthly_deduct_points INT DEFAULT 0,
    last_weekly_reset DATE,
    last_monthly_reset DATE,
    
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 定时任务执行记录表
CREATE TABLE scheduled_task_logs (
    id VARCHAR(36) PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    execution_date DATE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    status ENUM('running', 'completed', 'failed') NOT NULL,
    processed_count INT DEFAULT 0,
    error_message TEXT,
    
    INDEX idx_task_date (task_name, execution_date),
    INDEX idx_status (status)
);
```

### 2. 更新现有用户表

```sql
-- 为现有用户初始化信誉统计
INSERT INTO user_reputation_stats (user_id, protection_end_time)
SELECT id, DATE_ADD(created_at, INTERVAL 3 DAY)
FROM users 
WHERE id NOT IN (SELECT user_id FROM user_reputation_stats);
```

---

## 🔧 后端实现步骤

### 第一步：创建实体类

```java
// ReputationLog.java
@Data
@TableName("reputation_logs")
public class ReputationLog {
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    @TableField("user_id")
    private String userId;
    
    @TableField("change_type")
    private String changeType;
    
    private Integer points;
    private String reason;
    private String category;
    
    @TableField("related_id")
    private String relatedId;
    
    @TableField("consecutive_days")
    private Integer consecutiveDays;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField("processed_by")
    private String processedBy;
}

// UserReputationStats.java
@Data
@TableName("user_reputation_stats")
public class UserReputationStats {
    @TableId("user_id")
    private String userId;
    
    @TableField("current_score")
    private Integer currentScore;
    
    @TableField("current_level")
    private String currentLevel;
    
    @TableField("total_earned")
    private Integer totalEarned;
    
    @TableField("total_deducted")
    private Integer totalDeducted;
    
    // ... 其他字段
}
```

### 第二步：创建Mapper接口

```java
// ReputationLogMapper.java
@Mapper
public interface ReputationLogMapper extends BaseMapper<ReputationLog> {
    
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'deduct' " +
            "AND DATE(created_at) = #{date}")
    int getTodayDeductedPoints(@Param("userId") String userId, @Param("date") LocalDate date);
    
    @Select("SELECT * FROM reputation_logs WHERE user_id = #{userId} " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<ReputationLog> getRecentLogs(@Param("userId") String userId, @Param("limit") int limit);
}

// UserReputationStatsMapper.java
@Mapper
public interface UserReputationStatsMapper extends BaseMapper<UserReputationStats> {
    
    @Select("SELECT * FROM user_reputation_stats WHERE user_id = #{userId}")
    UserReputationStats selectByUserId(@Param("userId") String userId);
}
```

### 第三步：实现核心服务

参考设计文档中的 `ReputationService` 实现。

### 第四步：配置定时任务

```java
// ScheduledTaskConfig.java
@Configuration
@EnableScheduling
public class ScheduledTaskConfig {
    
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(5);
        scheduler.setThreadNamePrefix("reputation-task-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        return scheduler;
    }
}
```

---

## 📱 前端实现步骤

### 第一步：创建API服务

```typescript
// reputationService.ts
export interface ReputationStats {
  currentScore: number;
  currentLevel: string;
  totalEarned: number;
  totalDeducted: number;
  weeklyChange: number;
  protectionTimeRemaining?: number;
}

export interface ReputationLog {
  id: string;
  changeType: 'earn' | 'deduct';
  points: number;
  reason: string;
  category: string;
  createdAt: string;
}

class ReputationService {
  async getReputationStats(userId: string): Promise<ReputationStats> {
    const response = await fetch(`/api/v1/reputation/stats/${userId}`);
    return response.json();
  }
  
  async getReputationLogs(userId: string, page: number = 1, size: number = 20): Promise<ReputationLog[]> {
    const response = await fetch(`/api/v1/reputation/logs/${userId}?page=${page}&size=${size}`);
    return response.json();
  }
}

export const reputationService = new ReputationService();
```

### 第二步：创建信誉中心页面

```typescript
// ReputationCenter.tsx
import React, { useState, useEffect } from 'react';
import { Card, Progress, Tag, Timeline, Typography } from '@douyinfe/semi-ui';
import { reputationService } from '../services/reputationService';

const ReputationCenter: React.FC = () => {
  const [stats, setStats] = useState<ReputationStats | null>(null);
  const [logs, setLogs] = useState<ReputationLog[]>([]);
  
  useEffect(() => {
    loadReputationData();
  }, []);
  
  const loadReputationData = async () => {
    try {
      const userId = getCurrentUserId(); // 获取当前用户ID
      const [statsData, logsData] = await Promise.all([
        reputationService.getReputationStats(userId),
        reputationService.getReputationLogs(userId)
      ]);
      
      setStats(statsData);
      setLogs(logsData);
    } catch (error) {
      console.error('加载信誉数据失败:', error);
    }
  };
  
  return (
    <div className="reputation-center">
      {/* 信誉概览卡片 */}
      <Card title="信誉概览">
        {stats && (
          <div>
            <div className="reputation-score">
              <Typography.Title heading={2}>{stats.currentScore}</Typography.Title>
              <Tag color="blue">{stats.currentLevel}</Tag>
            </div>
            <Progress percent={getProgressPercent(stats.currentScore)} />
          </div>
        )}
      </Card>
      
      {/* 分数变更记录 */}
      <Card title="分数变更记录">
        <Timeline>
          {logs.map(log => (
            <Timeline.Item key={log.id}>
              <div className={`log-item ${log.changeType}`}>
                <span className="points">
                  {log.changeType === 'earn' ? '+' : '-'}{log.points}
                </span>
                <span className="reason">{log.reason}</span>
                <span className="time">{formatTime(log.createdAt)}</span>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      </Card>
    </div>
  );
};
```

---

## 🧪 测试方案

### 1. 单元测试

```java
@SpringBootTest
class ReputationServiceTest {
    
    @Autowired
    private ReputationService reputationService;
    
    @Test
    void testAddPoints() {
        String userId = "test-user-id";
        reputationService.addPoints(userId, 5, "完成学习任务", "daily_study");
        
        // 验证分数是否正确增加
        UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
        assertEquals(105, stats.getCurrentScore());
    }
    
    @Test
    void testProtectionPeriod() {
        String userId = "new-user-id";
        // 创建新用户（在保护期内）
        
        reputationService.deductPoints(userId, 5, "测试扣分", "daily_study");
        
        // 验证保护期内不扣分
        UserReputationStats stats = userReputationStatsMapper.selectByUserId(userId);
        assertEquals(100, stats.getCurrentScore());
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@Transactional
class DailyStudyCheckTaskTest {
    
    @Autowired
    private DailyStudyCheckTask dailyStudyCheckTask;
    
    @Test
    void testDailyStudyCheck() {
        // 准备测试数据：创建用户但不创建学习记录
        
        // 执行定时任务
        dailyStudyCheckTask.checkDailyStudy();
        
        // 验证扣分是否正确执行
    }
}
```

---

## 🚀 部署配置

### 1. 应用配置

```yaml
# application.yml
spring:
  task:
    scheduling:
      pool:
        size: 5
      thread-name-prefix: "reputation-"
      
reputation:
  protection-days: 3
  daily-deduct-limit: 15
  weekly-deduct-limit: 50
  monthly-deduct-limit: 100
```

### 2. 监控配置

```java
// ReputationMetrics.java
@Component
public class ReputationMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter pointsEarnedCounter;
    private final Counter pointsDeductedCounter;
    
    public ReputationMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.pointsEarnedCounter = Counter.builder("reputation.points.earned")
            .description("Total points earned")
            .register(meterRegistry);
        this.pointsDeductedCounter = Counter.builder("reputation.points.deducted")
            .description("Total points deducted")
            .register(meterRegistry);
    }
    
    public void recordPointsEarned(int points) {
        pointsEarnedCounter.increment(points);
    }
    
    public void recordPointsDeducted(int points) {
        pointsDeductedCounter.increment(points);
    }
}
```

---

## 📊 运营监控

### 1. 关键指标监控

- 每日活跃学习用户数
- 信誉分数分布
- 扣分用户比例
- 定时任务执行状态

### 2. 告警配置

```yaml
# 监控告警配置
alerts:
  - name: "定时任务执行失败"
    condition: "scheduled_task_failure_rate > 0.1"
    action: "发送邮件通知"
    
  - name: "大量用户被扣分"
    condition: "daily_deduct_user_rate > 0.3"
    action: "发送钉钉通知"
```

---

## ✅ 验收标准

### 功能验收
- [ ] 每日00:00定时任务正常执行
- [ ] 新用户3天保护期生效
- [ ] 加分扣分逻辑正确
- [ ] 等级计算准确
- [ ] 前端页面正常显示

### 性能验收
- [ ] 定时任务执行时间 < 5分钟
- [ ] API响应时间 < 500ms
- [ ] 数据库查询优化

### 安全验收
- [ ] 防止恶意刷分
- [ ] 数据一致性保证
- [ ] 异常情况处理

---

## 📞 技术支持

如有问题，请联系开发团队或查阅详细设计文档 `reputation-system-design.md`。
