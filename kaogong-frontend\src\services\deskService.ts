import { request } from './api';

// 小桌相关接口类型定义
export interface DeskResponse {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  ownerUsername: string;
  ownerNickname: string;
  maxMembers: number;
  currentMembers: number;
  availableSlots: number;
  autoApproveRules: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  isOwner: boolean;
  isMember: boolean;
  hasApplied: boolean;
  canJoin: boolean;
  pendingApplications?: number;
}

export interface CreateDeskRequest {
  name: string;
  description?: string;
  maxMembers: number;
  autoApproveRules?: string;
}

export interface UpdateDeskRequest {
  name?: string;
  description?: string;
  maxMembers?: number;
  autoApproveRules?: string;
}

export interface SearchDesksParams {
  keyword?: string;
  page?: number;
  size?: number;
}

export interface PageResponse<T> {
  records: T[];
  total: number;
  pages: number;
  current: number;
  size: number;
}

export interface DeskMemberResponse {
  id: string;
  deskId: string;
  userId: string;
  username: string;
  nickname: string;
  role: string;
  status: string;
  reputationScore: number;
  reputationLevel: string;
  joinReason: string;
  joinedAt: string;
  lastActiveAt: string;
  recentStudyDays: number;
  recentQuestions: number;
  isOwner: boolean;
  isActive: boolean;
}

export interface DeskRankingResponse {
  rank: number;
  userId: string;
  username: string;
  nickname: string;
  reputationScore: number;
  studyDays: number;
  totalQuestions: number;
  totalCorrect: number;
  accuracyRate: number;
  totalScore: number;
  isOwner: boolean;
}

export interface DeskApplicationResponse {
  id: string;
  deskId: string;
  deskName: string;
  deskDescription: string;
  userId: string;
  username: string;
  nickname: string;
  reputationScore: number;
  reputationLevel: string;
  reason: string;
  studyPlan: string;
  status: string;
  appliedAt: string;
  processedAt?: string;
  processedBy?: string;
  isPending: boolean;
  isApproved: boolean;
  isRejected: boolean;
}

export interface ApplyDeskRequest {
  deskId: string;
  reason: string;
  studyPlan?: string;
}

export interface ProcessApplicationRequest {
  action: 'approved' | 'rejected';
  remark?: string;
}

/**
 * 小桌管理服务
 * 提供小桌相关的所有API调用方法
 */
export const deskService = {
  /**
   * 创建小桌
   */
  createDesk: (data: CreateDeskRequest): Promise<DeskResponse> =>
    request.post('/v1/desks', data),

  /**
   * 获取小桌详情
   */
  getDeskById: (deskId: string): Promise<DeskResponse> =>
    request.get(`/v1/desks/${deskId}`),

  /**
   * 更新小桌信息
   */
  updateDesk: (deskId: string, data: UpdateDeskRequest): Promise<DeskResponse> =>
    request.put(`/v1/desks/${deskId}`, data),

  /**
   * 解散小桌
   */
  dissolveDesk: (deskId: string): Promise<void> =>
    request.delete(`/v1/desks/${deskId}`),

  /**
   * 搜索小桌
   */
  searchDesks: (params: SearchDesksParams): Promise<PageResponse<DeskResponse>> => {
    const queryParams = new URLSearchParams();
    if (params.keyword) queryParams.append('keyword', params.keyword);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.size) queryParams.append('size', params.size.toString());
    const queryString = queryParams.toString();
    return request.get(`/v1/desks${queryString ? `?${queryString}` : ''}`);
  },

  /**
   * 获取我的小桌
   */
  getMyDesks: (): Promise<DeskResponse[]> =>
    request.get('/v1/desks/my'),

  /**
   * 获取小桌统计信息
   */
  getDeskStatistics: (): Promise<Record<string, any>> =>
    request.get('/v1/desks/statistics'),

  /**
   * 获取小桌成员列表
   */
  getDeskMembers: (deskId: string): Promise<DeskMemberResponse[]> =>
    request.get(`/v1/desks/${deskId}/members`),

  /**
   * 移除成员
   */
  removeMember: (deskId: string, memberId: string): Promise<void> =>
    request.delete(`/v1/desks/${deskId}/members/${memberId}`),

  /**
   * 更新成员角色
   */
  updateMemberRole: (deskId: string, memberId: string, role: string): Promise<void> =>
    request.put(`/v1/desks/${deskId}/members/${memberId}?role=${role}`),

  /**
   * 离开小桌
   */
  leaveDesk: (deskId: string): Promise<void> =>
    request.post(`/v1/desks/${deskId}/members/leave`),

  /**
   * 获取小桌排行榜
   */
  getDeskRanking: (deskId: string, period = 'week'): Promise<DeskRankingResponse[]> =>
    request.get(`/v1/desks/${deskId}/members/ranking?period=${period}`),

  /**
   * 更新活跃时间
   */
  updateActiveTime: (deskId: string): Promise<void> =>
    request.post(`/v1/desks/${deskId}/members/active`),

  /**
   * 申请加入小桌
   */
  applyToJoinDesk: (data: ApplyDeskRequest): Promise<DeskApplicationResponse> =>
    request.post('/v1/desk-applications', data),

  /**
   * 获取我的申请历史
   */
  getMyApplications: (status?: string): Promise<DeskApplicationResponse[]> =>
    request.get(`/v1/desk-applications${status ? `?status=${status}` : ''}`),

  /**
   * 处理申请
   */
  processApplication: (applicationId: string, data: ProcessApplicationRequest): Promise<void> =>
    request.put(`/v1/desk-applications/${applicationId}/process`, data),

  /**
   * 取消申请
   */
  cancelApplication: (applicationId: string): Promise<void> =>
    request.delete(`/v1/desk-applications/${applicationId}`),

  /**
   * 获取小桌申请列表
   */
  getApplicationsByDesk: (deskId: string, status?: string): Promise<DeskApplicationResponse[]> =>
    request.get(`/v1/desk-applications/desk/${deskId}${status ? `?status=${status}` : ''}`),
};
