# 考公刷题记录系统 - 应用配置文件
# =====================================================

server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  profiles:
    # active: dev  # 暂时禁用dev profile
    include: file-upload # 包含文件上传配置

  # Spring MVC配置
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 50MB
      file-size-threshold: 2KB
      resolve-lazily: false

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************************&createDatabaseIfNotExist=true
    username: ${DB_USERNAME:xph}
    password: ${DB_PASSWORD:04551Wqh}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5
      auto-commit: true
      idle-timeout: 30000
      pool-name: KaogongHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_empty
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.wqh.publicexaminationassistant.entity

# JWT配置
jwt:
  secret: ${JWT_SECRET:kaogongSystemSecretKeyForJWTAuthenticationThatIsLongEnoughToMeetSecurityRequirements}
  expiration: 7200 # 2小时，单位：秒
  refresh-expiration: 604800 # 7天，单位：秒
  header: Authorization
  token-prefix: 'Bearer '

# 应用配置
app:
  name: 考公刷题记录系统
  version: 1.0.0
  description: 基于小桌系统和信誉体系的考公刷题记录平台

  # 邀请码配置
  invite:
    default-expiry-days: 30
    max-generate-count: 100

  # 小桌系统配置
  desk:
    max-members: 8
    min-members: 2
    application-timeout-hours: 48

  # 信誉系统配置
  reputation:
    initial-score: 100
    levels:
      newbie: 0-99
      regular: 100-299
      expert: 300-599
      master: 600+

# 日志配置
logging:
  level:
    com.wqh.publicexaminationassistant: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/kaogong-system.log
    max-size: 100MB
    max-history: 30

# SpringDoc OpenAPI配置
springdoc:
  api-docs:
    path: /api-docs
    groups:
      enabled: true
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    disable-swagger-default-url: true
    display-request-duration: true
    csrf:
      enabled: true
  packages-to-scan: com.wqh.publicexaminationassistant.controller
  paths-to-match: /v1/**
  default-consumes-media-type: application/json
  default-produces-media-type: application/json

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true

# 缓存配置
cache:
  caffeine:
    specs:
      userCache: maximumSize=1000,expireAfterWrite=30m
      rankingCache: maximumSize=500,expireAfterWrite=5m
      announcementCache: maximumSize=200,expireAfterWrite=10m
      notificationCache: maximumSize=100,expireAfterWrite=1m

---
# 开发环境配置
spring:
  profiles: dev

  datasource:
    url: ***************************************************************************************************************************************************************************************
    username: ${DB_USERNAME:xph}
    password: ${DB_PASSWORD:04551Wqh}
  redis:
    host: localhost
    port: 6379
    password:

logging:
  level:
    root: INFO
    com.wqh.publicexaminationassistant: DEBUG
