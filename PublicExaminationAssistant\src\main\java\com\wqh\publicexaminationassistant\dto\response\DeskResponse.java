package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小桌响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class DeskResponse {

    /**
     * 小桌ID
     */
    private String id;

    /**
     * 小桌名称
     */
    private String name;

    /**
     * 小桌描述
     */
    private String description;

    /**
     * 桌长ID
     */
    private String ownerId;

    /**
     * 桌长用户名
     */
    private String ownerUsername;

    /**
     * 桌长昵称
     */
    private String ownerNickname;

    /**
     * 最大成员数
     */
    private Integer maxMembers;

    /**
     * 当前成员数
     */
    private Integer currentMembers;

    /**
     * 剩余可加入人数
     */
    private Integer availableSlots;

    /**
     * 自动审核规则
     */
    private String autoApproveRules;

    /**
     * 小桌状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 当前用户是否为桌长
     */
    private Boolean isOwner;

    /**
     * 当前用户是否为成员
     */
    private Boolean isMember;

    /**
     * 当前用户是否已申请
     */
    private Boolean hasApplied;

    /**
     * 是否可以加入
     */
    private Boolean canJoin;

    /**
     * 待处理申请数量
     */
    private Integer pendingApplications;
}
