package com.wqh.publicexaminationassistant.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小桌成员响应DTO
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
public class DeskMemberResponse {

    /**
     * 成员关系ID
     */
    private String id;

    /**
     * 小桌ID
     */
    private String deskId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 角色（owner/member）
     */
    private String role;

    /**
     * 成员状态
     */
    private String status;

    /**
     * 信誉分数
     */
    private Integer reputationScore;

    /**
     * 信誉等级
     */
    private String reputationLevel;

    /**
     * 加入理由
     */
    private String joinReason;

    /**
     * 加入时间
     */
    private LocalDateTime joinedAt;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveAt;

    /**
     * 最近学习天数
     */
    private Integer recentStudyDays;

    /**
     * 最近题目数量
     */
    private Integer recentQuestions;

    /**
     * 是否为桌长
     */
    private Boolean isOwner;

    /**
     * 是否活跃
     */
    private Boolean isActive;
}
