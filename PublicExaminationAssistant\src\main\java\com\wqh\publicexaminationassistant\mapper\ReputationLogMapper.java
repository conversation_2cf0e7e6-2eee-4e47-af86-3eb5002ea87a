package com.wqh.publicexaminationassistant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wqh.publicexaminationassistant.entity.ReputationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * 信誉记录表Mapper接口
 * 提供信誉记录数据访问操作
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface ReputationLogMapper extends BaseMapper<ReputationLog> {

    /**
     * 获取用户今日已扣分数
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'deduct' " +
            "AND DATE(created_at) = #{date}")
    int getTodayDeductedPoints(@Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 获取用户最近的信誉记录
     */
    @Select("SELECT * FROM reputation_logs WHERE user_id = #{userId} " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<ReputationLog> getRecentLogs(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 获取用户指定分类的记录
     */
    @Select("SELECT * FROM reputation_logs WHERE user_id = #{userId} AND category = #{category} " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<ReputationLog> getLogsByCategory(@Param("userId") String userId,
                                         @Param("category") String category,
                                         @Param("limit") int limit);

    /**
     * 获取用户本周扣分记录
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'deduct' " +
            "AND YEARWEEK(created_at, 1) = YEARWEEK(#{date}, 1)")
    int getWeeklyDeductedPoints(@Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 获取用户本月扣分记录
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'deduct' " +
            "AND YEAR(created_at) = YEAR(#{date}) AND MONTH(created_at) = MONTH(#{date})")
    int getMonthlyDeductedPoints(@Param("userId") String userId, @Param("date") LocalDate date);

    /**
     * 获取用户总获得分数
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'earn'")
    int getTotalEarnedPoints(@Param("userId") String userId);

    /**
     * 获取用户总扣除分数
     */
    @Select("SELECT COALESCE(SUM(points), 0) FROM reputation_logs " +
            "WHERE user_id = #{userId} AND change_type = 'deduct'")
    int getTotalDeductedPoints(@Param("userId") String userId);

    /**
     * 获取指定日期范围内的记录统计
     */
    @Select("SELECT category, change_type, COUNT(*) as count, SUM(points) as total_points " +
            "FROM reputation_logs " +
            "WHERE created_at BETWEEN #{startDate} AND #{endDate} " +
            "GROUP BY category, change_type " +
            "ORDER BY category, change_type")
    List<java.util.Map<String, Object>> getStatsByDateRange(@Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate);
}
