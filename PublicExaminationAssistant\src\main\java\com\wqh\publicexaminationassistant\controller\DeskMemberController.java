package com.wqh.publicexaminationassistant.controller;

import com.wqh.publicexaminationassistant.common.result.ApiResponse;
import com.wqh.publicexaminationassistant.dto.response.DeskMemberResponse;
import com.wqh.publicexaminationassistant.dto.response.DeskRankingResponse;
import com.wqh.publicexaminationassistant.security.JwtUserDetails;
import com.wqh.publicexaminationassistant.service.DeskMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小桌成员管理控制器
 * 提供成员查询、管理、排行榜等API
 * 
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
@RestController
@RequestMapping("/v1/desks/{deskId}/members")
@RequiredArgsConstructor
@Tag(name = "小桌成员管理", description = "小桌成员查询、管理、排行榜等功能")
public class DeskMemberController {

    private final DeskMemberService deskMemberService;

    /**
     * 获取小桌成员列表
     */
    @GetMapping
    @Operation(summary = "获取成员列表", description = "获取小桌的所有成员信息（仅成员可查看）")
    public ApiResponse<List<DeskMemberResponse>> getDeskMembers(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查看小桌成员: userId={}, deskId={}", userDetails.getId(), deskId);
        
        List<DeskMemberResponse> response = deskMemberService.getDeskMembers(deskId, userDetails.getId());
        return ApiResponse.success(response, "获取成员列表成功");
    }

    /**
     * 移除成员
     */
    @DeleteMapping("/{memberId}")
    @Operation(summary = "移除成员", description = "从小桌中移除指定成员（仅桌长可操作）")
    public ApiResponse<Void> removeMember(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @Parameter(description = "成员用户ID") 
            @PathVariable String memberId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户移除小桌成员: operatorId={}, deskId={}, memberId={}", 
                userDetails.getId(), deskId, memberId);
        
        deskMemberService.removeMember(deskId, memberId, userDetails.getId());
        return ApiResponse.success(null, "成员移除成功");
    }

    /**
     * 更新成员角色
     */
    @PutMapping("/{memberId}")
    @Operation(summary = "更新成员角色", description = "更新成员的角色（如转让桌长权限）")
    public ApiResponse<Void> updateMemberRole(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @Parameter(description = "成员用户ID") 
            @PathVariable String memberId,
            @Parameter(description = "新角色（owner/member）") 
            @RequestParam String role,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户更新成员角色: operatorId={}, deskId={}, memberId={}, role={}", 
                userDetails.getId(), deskId, memberId, role);
        
        deskMemberService.updateMemberRole(deskId, memberId, role, userDetails.getId());
        return ApiResponse.success(null, "成员角色更新成功");
    }

    /**
     * 离开小桌
     */
    @PostMapping("/leave")
    @Operation(summary = "离开小桌", description = "当前用户离开小桌（桌长不能直接离开）")
    public ApiResponse<Void> leaveDesk(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户离开小桌: userId={}, deskId={}", userDetails.getId(), deskId);
        
        deskMemberService.leaveDesk(deskId, userDetails.getId());
        return ApiResponse.success(null, "离开小桌成功");
    }

    /**
     * 获取小桌排行榜
     */
    @GetMapping("/ranking")
    @Operation(summary = "获取排行榜", description = "获取小桌成员学习排行榜（仅成员可查看）")
    public ApiResponse<List<DeskRankingResponse>> getDeskRanking(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @Parameter(description = "统计周期（day/week/month/all）", example = "week") 
            @RequestParam(defaultValue = "week") String period,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.info("用户查看小桌排行榜: userId={}, deskId={}, period={}", 
                userDetails.getId(), deskId, period);
        
        List<DeskRankingResponse> response = deskMemberService.getDeskRanking(deskId, period, userDetails.getId());
        return ApiResponse.success(response, "获取排行榜成功");
    }

    /**
     * 更新成员活跃时间
     */
    @PostMapping("/active")
    @Operation(summary = "更新活跃时间", description = "更新当前用户在小桌中的最后活跃时间")
    public ApiResponse<Void> updateActiveTime(
            @Parameter(description = "小桌ID") 
            @PathVariable String deskId,
            @AuthenticationPrincipal JwtUserDetails userDetails) {
        
        log.debug("用户更新活跃时间: userId={}, deskId={}", userDetails.getId(), deskId);
        
        deskMemberService.updateMemberActiveTime(deskId, userDetails.getId());
        return ApiResponse.success(null, "活跃时间更新成功");
    }
}
