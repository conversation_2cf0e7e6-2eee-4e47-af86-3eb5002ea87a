import React, { useState } from 'react';
import { Card, Upload, Button, Toast, Typography, Space, Avatar } from '@douyinfe/semi-ui';
import { IconCamera, IconUpload } from '@douyinfe/semi-icons';
import { authService } from '../services/authService';
import { useAuthStore } from '../stores/useAuthStore';
import { validateImageFile } from '../utils/fileValidation';

const { Title, Text } = Typography;

const AvatarUploadTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState('');
  const { user, updateUser } = useAuthStore();

  // 头像文件选择处理
  const handleAvatarFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('选择的文件:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    // 使用文件验证工具
    const validation = validateImageFile(file, 10 * 1024 * 1024); // 10MB

    if (!validation.isValid) {
      Toast.error(validation.errorMessage || '文件验证失败');
      return;
    }

    try {
      setLoading(true);
      Toast.info('正在上传头像...');

      const updatedUser = await authService.uploadAvatar(file);

      // 更新本地状态
      setAvatarUrl(updatedUser.avatarUrl || '');
      updateUser(updatedUser);

      Toast.success('头像上传成功！');
      console.log('上传成功，新头像URL:', updatedUser.avatarUrl);
    } catch (error: any) {
      console.error('头像上传失败:', error);
      Toast.error(error.message || '头像上传失败');
    } finally {
      setLoading(false);
      // 清空文件输入，允许重复选择同一文件
      event.target.value = '';
    }
  };

  return (
    <div style={{ 
      padding: '40px',
      maxWidth: '800px',
      margin: '0 auto',
      fontFamily: 'var(--font-system)'
    }}>
      <Card style={{ marginBottom: '20px' }}>
        <Title level={2} style={{ marginBottom: '16px' }}>
          🖼️ 头像上传测试
        </Title>
        <Text>
          这个页面用于测试头像上传功能。请选择一张图片文件进行上传。
        </Text>
      </Card>

      <Card>
        <Space vertical style={{ width: '100%' }} spacing={24}>
          {/* 当前头像显示 */}
          <div style={{ textAlign: 'center' }}>
            <Title level={4}>当前头像</Title>
            <Avatar 
              src={avatarUrl || user?.avatarUrl} 
              size="extra-large"
              style={{ margin: '16px' }}
            >
              {user?.nickname?.charAt(0) || user?.username?.charAt(0) || 'U'}
            </Avatar>
            <div>
              <Text type="secondary">
                {avatarUrl || user?.avatarUrl ? '已设置头像' : '未设置头像'}
              </Text>
            </div>
          </div>

          {/* 上传组件 */}
          <div style={{ textAlign: 'center' }}>
            <Title level={4}>上传新头像</Title>

            <input
              type="file"
              accept="image/*"
              onChange={handleAvatarFileChange}
              style={{ display: 'none' }}
              id="avatar-upload-input-test"
              disabled={loading}
            />

            <Button
              icon={<IconUpload />}
              loading={loading}
              disabled={loading}
              size="large"
              theme="solid"
              onClick={() => {
                if (!loading) {
                  document.getElementById('avatar-upload-input-test')?.click();
                }
              }}
            >
              {loading ? '上传中...' : '选择图片'}
            </Button>

            <div style={{ marginTop: '16px' }}>
              <Text type="secondary" size="small">
                支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 10MB
              </Text>
            </div>
          </div>

          {/* 测试信息 */}
          <div style={{ 
            backgroundColor: '#f8f9fa', 
            padding: '16px', 
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <Title level={5}>测试信息</Title>
            <Space vertical style={{ width: '100%' }}>
              <Text>
                <strong>用户ID:</strong> {user?.id || '未登录'}
              </Text>
              <Text>
                <strong>用户名:</strong> {user?.username || '未登录'}
              </Text>
              <Text>
                <strong>当前头像URL:</strong> {user?.avatarUrl || '无'}
              </Text>
              <Text>
                <strong>新头像URL:</strong> {avatarUrl || '无'}
              </Text>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default AvatarUploadTest;
