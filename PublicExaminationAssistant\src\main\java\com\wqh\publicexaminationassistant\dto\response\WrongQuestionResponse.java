package com.wqh.publicexaminationassistant.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 错题响应DTO
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
public class WrongQuestionResponse {

    /**
     * 错题ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 关联的学习记录ID
     */
    private String studyRecordId;

    /**
     * 学习模块类型
     */
    private String moduleType;

    /**
     * 学习模块名称 (中文显示)
     */
    private String moduleName;

    /**
     * 题目类型
     */
    private String questionType;

    /**
     * 题目类型名称 (中文显示)
     */
    private String questionTypeName;

    /**
     * 难度等级
     */
    private String difficultyLevel;

    /**
     * 难度等级名称 (中文显示)
     */
    private String difficultyLevelName;

    /**
     * 题目内容
     */
    private String questionContent;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 解析
     */
    private String explanation;

    /**
     * 掌握状态
     */
    private String masteryStatus;

    /**
     * 掌握状态名称 (中文显示)
     */
    private String masteryStatusName;

    /**
     * 复习次数
     */
    private Integer reviewCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 最后复习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewedAt;

    /**
     * 关联的学习记录信息 (可选)
     */
    private StudyRecordBasicInfo studyRecord;

    /**
     * 关联的图片列表
     */
    private List<ImageUploadResponse> images;

    /**
     * 学习记录基本信息内部类
     */
    @Data
    public static class StudyRecordBasicInfo {
        private String id;
        private String moduleType;
        private String moduleName;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private java.time.LocalDate studyDate;
    }
}
