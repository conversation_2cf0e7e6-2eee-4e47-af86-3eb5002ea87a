/* 个人中心页面样式 */
.profile-container {
  min-height: calc(100vh - 80px);
  background:
    /* 纸质纹理层 */
    radial-gradient(circle at 20% 80%, rgba(237, 137, 54, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(66, 153, 225, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(72, 187, 120, 0.02) 0%, transparent 50%),
    /* 纸质基础渐变 */
    linear-gradient(135deg, var(--paper-bg) 0%, var(--paper-warm) 50%, var(--paper-cream) 100%),
    /* 纸质纹理噪点 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(45, 55, 72, 0.005) 2px,
      rgba(45, 55, 72, 0.005) 4px
    );
  padding: 40px 20px;
  font-family: var(--font-system);
  position: relative;
  overflow-x: hidden;
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* 手绘风格卡片 */
.sketch-card {
  background: var(--paper-bg);
  border: 2px solid var(--ink-dark);
  border-radius: 15px;
  transform: rotate(-0.5deg);
  box-shadow: 
    3px 3px 0px var(--shadow-light),
    6px 6px 0px rgba(45, 55, 72, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 24px;
  position: relative;
}

.sketch-card:hover {
  transform: rotate(0deg) translateY(-3px);
  box-shadow: 
    5px 5px 0px var(--shadow-light),
    10px 10px 0px rgba(45, 55, 72, 0.15);
}

.sketch-card:nth-child(even) {
  transform: rotate(0.5deg);
}

.sketch-card:nth-child(even):hover {
  transform: rotate(0deg) translateY(-3px);
}

/* 增强的头像卡片 */
.profile-avatar-card {
  text-align: center;
  padding: 30px 20px;
  position: relative;
  overflow: hidden;
}

.enhanced-avatar-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(241, 245, 249, 0.95) 100%);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* 装饰性背景元素 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(66, 153, 225, 0.1), rgba(72, 187, 120, 0.1));
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 60px;
  height: 60px;
  top: -20px;
  right: -20px;
  animation-delay: 0s;
}

.circle-2 {
  width: 40px;
  height: 40px;
  bottom: 20px;
  left: -10px;
  animation-delay: 2s;
}

.circle-3 {
  width: 30px;
  height: 30px;
  top: 50%;
  left: -15px;
  animation-delay: 4s;
}

/* 头像上传容器 */
.avatar-upload-container {
  position: relative;
  z-index: 2;
  margin-bottom: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  position: relative;
}

.enhanced-avatar {
  transition: all 0.3s ease;
}

.enhanced-avatar:hover {
  transform: translateY(-2px);
}

/* 头像外圈装饰 */
.avatar-ring {
  position: relative;
  padding: 8px;
  border-radius: 50%;
  background: linear-gradient(45deg, #4299e1, #48bb78, #ed8936);
  background-size: 300% 300%;
  animation: gradientShift 4s ease infinite;
}

.avatar-ring-inner {
  border-radius: 50%;
  padding: 4px;
  background: white;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 上传提示 */
.upload-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateY(10px);
}

.enhanced-avatar:hover .upload-hint {
  opacity: 1;
  transform: translateY(0);
}

/* 用户信息区域 */
.user-info-section {
  position: relative;
  z-index: 2;
}

.user-name-container {
  margin-bottom: 16px;
}

.enhanced-name {
  position: relative;
  display: inline-block;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #2d3748, #4a5568);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.name-underline {
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #4299e1, #48bb78);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.enhanced-name:hover .name-underline {
  width: 100%;
}

/* 用户徽章区域 */
.user-badges {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.enhanced-role {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  transform: rotate(-1deg);
  transition: all 0.3s ease;
}

.enhanced-role:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

/* 在线状态 */
.online-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #48bb78;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #48bb78;
  animation: pulse 2s ease-in-out infinite;
  box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 6px rgba(72, 187, 120, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
  }
}

.avatar-upload .semi-upload-add {
  border-radius: 50%;
  border: none;
  background: transparent;
}

.avatar-hover-mask {
  background-color: rgba(0, 0, 0, 0.5);
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.user-name {
  font-family: 'Kalam', cursive;
  font-size: 24px;
  font-weight: 700;
  color: var(--ink-dark);
  margin: 0;
  text-shadow: 1px 1px 0px rgba(255, 255, 255, 0.8);
}

.user-role {
  font-size: 14px;
  color: var(--ink-medium);
  margin: 0;
  padding: 4px 12px;
  background: var(--accent-blue);
  color: white;
  border-radius: 12px;
  display: inline-block;
  transform: rotate(-1deg);
}

/* 增强的表单卡片 */
.enhanced-form-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(241, 245, 249, 0.95) 100%);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.enhanced-form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4299e1, #48bb78, #ed8936, #9f7aea);
  background-size: 300% 100%;
  animation: gradientFlow 4s ease-in-out infinite;
}

@keyframes gradientFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 增强的卡片标题 */
.enhanced-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Kalam', cursive;
  font-size: 20px;
  font-weight: 700;
  color: var(--ink-dark);
  position: relative;
}

.title-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  transition: all 0.3s ease;
}

.title-icon-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

.title-icon {
  color: white;
  font-size: 18px;
}

.title-text {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-decoration {
  font-size: 16px;
  animation: sparkle 2s ease-in-out infinite;
}

/* 表单容器 */
.form-container {
  position: relative;
  z-index: 2;
}

.enhanced-form {
  position: relative;
}

/* 表单字段组 */
.form-field-group {
  margin-bottom: 20px;
  position: relative;
}

/* 增强的标签 */
.enhanced-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 15px;
  color: var(--ink-dark);
  margin-bottom: 8px;
}

.label-icon {
  color: #4299e1;
  font-size: 16px;
}

.required-star {
  color: #e53e3e;
  font-weight: 700;
  margin-left: 2px;
}

/* 增强的输入框 */
.enhanced-input {
  position: relative;
  overflow: hidden;
}

.enhanced-input .semi-input {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 2px solid rgba(66, 153, 225, 0.2);
  border-radius: 12px;
  padding: 14px 16px;
  font-size: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.enhanced-input .semi-input:hover {
  border-color: rgba(66, 153, 225, 0.4);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.1);
  transform: translateY(-1px);
}

.enhanced-input .semi-input:focus {
  border-color: #4299e1;
  box-shadow:
    0 0 0 3px rgba(66, 153, 225, 0.1),
    0 4px 20px rgba(66, 153, 225, 0.15);
  transform: translateY(-2px);
}

.enhanced-input .semi-input-prefix {
  color: #4299e1;
  margin-right: 8px;
}

/* 字段提示 */
.field-hint {
  margin-top: 6px;
  padding-left: 4px;
}

.hint-text {
  font-size: 13px;
  color: #718096;
  font-style: italic;
}

/* 增强的表单操作区域 */
.enhanced-form-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 2px dashed rgba(66, 153, 225, 0.2);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 增强的保存按钮 */
.enhanced-save-btn {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  box-shadow:
    0 6px 20px rgba(66, 153, 225, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  height: 48px;
  font-weight: 600;
  font-size: 15px;
}

.enhanced-save-btn:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(66, 153, 225, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.enhanced-save-btn:active {
  transform: translateY(0);
}

.enhanced-save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.btn-icon {
  font-size: 18px;
}

.btn-text {
  font-weight: 600;
}

.btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.enhanced-save-btn:hover .btn-shine {
  left: 100%;
}

/* 表单状态指示器 */
.form-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.status-success {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #38a169;
  background: linear-gradient(135deg, rgba(56, 161, 105, 0.1), rgba(72, 187, 120, 0.1));
  border: 1px solid rgba(56, 161, 105, 0.2);
}

.status-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #d69e2e;
  background: linear-gradient(135deg, rgba(214, 158, 46, 0.1), rgba(237, 137, 54, 0.1));
  border: 1px solid rgba(214, 158, 46, 0.2);
}

.status-icon {
  font-size: 16px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(66, 153, 225, 0.2);
  border-top: 3px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #718096;
  font-size: 15px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 增强的安全设置卡片 */
.enhanced-security-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(254, 252, 232, 0.95) 50%,
    rgba(255, 248, 220, 0.95) 100%);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255, 193, 7, 0.3);
  box-shadow:
    0 10px 40px rgba(255, 193, 7, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.enhanced-security-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ffc107, #ff9800, #f44336, #e91e63);
  background-size: 300% 100%;
  animation: securityGradient 3s ease-in-out infinite;
}

@keyframes securityGradient {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.security-icon {
  background: linear-gradient(135deg, #ffc107, #ff9800);
}

.security-badge {
  font-size: 16px;
  animation: securityPulse 2s ease-in-out infinite;
}

@keyframes securityPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 安全设置区域 */
.enhanced-security-section {
  position: relative;
  z-index: 2;
}

.security-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 安全项目 */
.security-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
  border: 2px solid rgba(255, 193, 7, 0.2);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.security-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 193, 7, 0.4);
}

.security-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffc107, #ff9800);
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.security-item-content {
  flex: 1;
}

.security-item-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--ink-dark);
}

.security-item-desc {
  margin: 0;
  font-size: 14px;
  color: #718096;
  line-height: 1.5;
}

.security-item-action {
  flex-shrink: 0;
}

/* 增强的安全按钮 */
.enhanced-security-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #ffc107;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 255, 255, 0.9));
  color: #b7791f;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.enhanced-security-btn:hover {
  background: linear-gradient(135deg, #ffc107, #ff9800);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.enhanced-security-btn .btn-arrow {
  transition: transform 0.3s ease;
}

.enhanced-security-btn:hover .btn-arrow {
  transform: translateX(3px);
}

/* 安全提示卡片 */
.security-tips-card {
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.05), rgba(255, 255, 255, 0.9));
  border: 2px solid rgba(66, 153, 225, 0.2);
  border-radius: 16px;
  padding: 20px;
  margin-top: 16px;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
}

.tips-icon {
  color: #4299e1;
  font-size: 18px;
}

.tips-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--ink-dark);
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: #4a5568;
}

.tip-check {
  color: #38a169;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

/* 增强的分割线 */
.enhanced-divider {
  border-color: rgba(66, 153, 225, 0.2);
  position: relative;
}

.enhanced-divider::before {
  content: '✨';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 0 16px;
  font-size: 16px;
}

/* 表单样式 */
.profile-form-card .semi-form-field {
  margin-bottom: 20px;
}

.profile-form-card .semi-form-field-label {
  font-weight: 600;
  color: var(--ink-dark);
}

.profile-form-card .semi-input {
  border: 2px dashed var(--ink-light);
  border-radius: 8px;
  background: var(--paper-warm);
  transition: all 0.3s ease;
}

.profile-form-card .semi-input:focus {
  border-color: var(--accent-blue);
  border-style: solid;
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
}

/* 手绘风格按钮 */
.sketch-button {
  border: 2px solid var(--ink-dark);
  border-radius: 12px;
  font-family: 'Kalam', cursive;
  font-weight: 600;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  box-shadow: 2px 2px 0px var(--shadow-light);
}

.sketch-button:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow: 4px 4px 0px var(--shadow-light);
}

.sketch-button.semi-button-primary {
  background: var(--accent-blue);
  border-color: var(--accent-blue);
  color: white;
}

.sketch-button.semi-button-light {
  background: var(--paper-bg);
  color: var(--ink-dark);
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px dashed var(--ink-light);
}

/* 安全设置区域 */
.security-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.security-desc {
  color: var(--ink-medium);
  margin: 0;
  font-size: 14px;
}

/* 模态框按钮 */
.modal-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--semi-color-border);
}

/* 浮动装饰元素 */
.floating-emoji {
  position: absolute;
  font-size: 24px;
  opacity: 0.6;
  pointer-events: none;
  z-index: 1;
}

.profile-emoji-1 {
  top: 10%;
  left: 5%;
  animation: float 6s ease-in-out infinite;
}

.profile-emoji-2 {
  top: 20%;
  right: 8%;
  animation: float 8s ease-in-out infinite reverse;
}

.profile-emoji-3 {
  bottom: 30%;
  left: 3%;
  animation: pulse 4s ease-in-out infinite;
}

.profile-emoji-4 {
  bottom: 15%;
  right: 5%;
  animation: twinkle 5s ease-in-out infinite;
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.6; transform: rotate(0deg); }
  25% { opacity: 0.8; transform: rotate(5deg); }
  50% { opacity: 1; transform: rotate(-5deg); }
  75% { opacity: 0.8; transform: rotate(3deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-container {
    padding: 20px 10px;
  }

  .profile-content .semi-row {
    flex-direction: column;
  }

  .profile-content .semi-col {
    width: 100% !important;
    margin-bottom: 20px;
  }

  .security-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .floating-emoji {
    display: none;
  }

  /* 移动端头像优化 */
  .enhanced-avatar-card {
    padding: 20px 15px;
  }

  .avatar-ring {
    padding: 6px;
  }

  .enhanced-name {
    font-size: 24px;
  }

  .decoration-circle {
    display: none;
  }

  /* 移动端信誉区域优化 */
  .enhanced-reputation {
    padding: 20px 16px;
    margin: 16px 0;
  }

  .score-number {
    font-size: 28px;
  }

  .enhanced-badge {
    padding: 10px 16px;
    font-size: 14px;
  }

  .reputation-center-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  /* 移动端表单优化 */
  .enhanced-form-card {
    padding: 20px;
  }

  .form-field-group {
    margin-bottom: 24px;
  }

  .enhanced-input .semi-input {
    padding: 12px 14px;
    font-size: 14px;
  }

  .enhanced-save-btn {
    height: 48px;
    font-size: 15px;
  }

  .security-item {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 20px;
  }

  .security-item-action {
    width: 100%;
  }

  .enhanced-security-btn {
    width: 100%;
    justify-content: center;
  }

  .tips-list {
    gap: 10px;
  }

  .tip-item {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .profile-form-card,
  .profile-security-card {
    padding: 20px;
  }
  
  .user-name {
    font-size: 20px;
  }
  
  .sketch-card {
    transform: none;
    margin-bottom: 16px;
  }
  
  .sketch-card:hover {
    transform: translateY(-2px);
  }
}

/* 增强的信誉等级样式 */
.enhanced-reputation {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.08) 0%,
    rgba(255, 255, 255, 0.95) 30%,
    rgba(255, 248, 220, 0.8) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  padding: 24px 20px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(255, 215, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.enhanced-reputation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4, #45B7D1, #FFD700);
  background-size: 300% 100%;
  animation: rainbowShimmer 3s ease-in-out infinite;
}

.enhanced-reputation::after {
  content: '✨';
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 16px;
  animation: twinkle 2s ease-in-out infinite;
}

@keyframes rainbowShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 信誉等级标题 */
.reputation-header {
  margin-bottom: 20px;
}

.reputation-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
}

.title-icon {
  color: #FFD700;
  font-size: 20px;
  animation: rotate 4s linear infinite;
}

.title-text {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-sparkle {
  font-size: 14px;
  animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0.5; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 信誉等级展示 */
.reputation-display {
  margin-bottom: 20px;
}

.reputation-badge-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  line-height: 25px;
}

.enhanced-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 700;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  transform: rotate(-1deg);
  transition: all 0.3s ease;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.enhanced-badge:hover {
  transform: rotate(0deg) translateY(-2px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.enhanced-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.enhanced-badge:hover::before {
  left: 100%;
}

.badge-icon {
  font-size: 18px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.badge-text {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 信誉分数展示 */
.reputation-score-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  font-weight: 700;
}

.score-number {
  font-size: 32px;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  animation: scoreGlow 2s ease-in-out infinite;
}

.score-unit {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

@keyframes scoreGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2); }
}

/* 进度条区域 */
.reputation-progress-area {
  margin-bottom: 20px;
}

.progress-info {
  margin-bottom: 8px;
  text-align: center;
}

.progress-text {
  font-size: 13px;
  color: #666;
}

.highlight-number {
  font-weight: 700;
  color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff, #bae7ff);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 14px;
}

.progress-container {
  position: relative;
}

.enhanced-progress {
  height: 8px !important;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-progress .semi-progress-line-outer {
  background: linear-gradient(90deg, #f0f0f0, #e8e8e8);
}

.enhanced-progress .semi-progress-line-inner {
  background: linear-gradient(90deg, #40a9ff, #1890ff, #096dd9) !important;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
  animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
  0%, 100% { box-shadow: 0 0 10px rgba(24, 144, 255, 0.3); }
  50% { box-shadow: 0 0 15px rgba(24, 144, 255, 0.5); }
}

/* 信誉中心按钮 */
.reputation-action {
  text-align: center;
}

.reputation-center-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: 2px solid #4299e1;
  border-radius: 25px;
  background: linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(255, 255, 255, 0.9));
  color: #4299e1;
  font-weight: 600;
  font-size: 14px;
  transform: rotate(-0.5deg);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
  backdrop-filter: blur(10px);
}

.reputation-center-btn:hover {
  transform: rotate(0deg) translateY(-2px);
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
  border-color: #3182ce;
}

.btn-arrow {
  transition: transform 0.3s ease;
}

.reputation-center-btn:hover .btn-arrow {
  transform: translateX(3px);
}

.reputation-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.reputation-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.reputation-score {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.reputation-progress-container {
  margin-top: 8px;
}

.reputation-progress-text {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

/* 等级颜色主题 */
.reputation-newbie {
  background: linear-gradient(135deg, #87d068, #52c41a);
  color: white;
}

.reputation-bronze {
  background: linear-gradient(135deg, #CD7F32, #B8860B);
  color: white;
}

.reputation-silver {
  background: linear-gradient(135deg, #C0C0C0, #A9A9A9);
  color: #333;
}

.reputation-gold {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #333;
}

.reputation-platinum {
  background: linear-gradient(135deg, #E5E4E2, #D3D3D3);
  color: #333;
}

.reputation-diamond {
  background: linear-gradient(135deg, #B9F2FF, #87CEEB);
  color: #333;
}

.reputation-master {
  background: linear-gradient(135deg, #FF6B6B, #FF4757);
  color: white;
}

.reputation-grandmaster {
  background: linear-gradient(135deg, #9B59B6, #8E44AD);
  color: white;
}
