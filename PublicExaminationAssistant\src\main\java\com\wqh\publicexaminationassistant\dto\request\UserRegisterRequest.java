package com.wqh.publicexaminationassistant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户注册请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@Schema(description = "用户注册请求")
public class UserRegisterRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字、下划线")
    @Schema(description = "用户名", example = "testuser")
    private String username;

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100字符")
    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @NotBlank(message = "密码不能为空")
    @Size(min = 8, max = 50, message = "密码长度必须在8-50字符之间")
    @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).+$", message = "密码必须包含字母和数字")
    @Schema(description = "密码", example = "password123")
    private String password;

    @NotBlank(message = "邀请码不能为空")
    @Size(min = 8, max = 8, message = "邀请码长度必须为8位")
    @Schema(description = "邀请码", example = "ABC12345")
    private String inviteCode;

    @Size(max = 50, message = "昵称长度不能超过50字符")
    @Schema(description = "昵称", example = "测试用户")
    private String nickname;
}
