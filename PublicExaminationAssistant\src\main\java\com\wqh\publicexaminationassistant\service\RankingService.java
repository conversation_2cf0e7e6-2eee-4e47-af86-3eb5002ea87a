package com.wqh.publicexaminationassistant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wqh.publicexaminationassistant.common.exception.BusinessException;
import com.wqh.publicexaminationassistant.common.result.ResultCode;
import com.wqh.publicexaminationassistant.dto.response.GlobalRankingResponse;
import com.wqh.publicexaminationassistant.dto.response.RankingStatistics;
import com.wqh.publicexaminationassistant.dto.response.UserRankResponse;
import com.wqh.publicexaminationassistant.entity.Ranking;
import com.wqh.publicexaminationassistant.enums.RankingPeriod;
import com.wqh.publicexaminationassistant.enums.RankingType;
import com.wqh.publicexaminationassistant.mapper.RankingMapper;
import com.wqh.publicexaminationassistant.mapper.StudyRecordMapper;
import com.wqh.publicexaminationassistant.mapper.StudyRecordMapper;
import com.wqh.publicexaminationassistant.mapper.UserReputationStatsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 排行榜服务类
 * 提供排行榜相关的业务逻辑处理
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RankingService {

    private final RankingMapper rankingMapper;
    private final StudyRecordMapper studyRecordMapper;

    private final UserReputationStatsMapper userReputationStatsMapper;

    /**
     * 获取全站排行榜
     *
     * @param type 排行榜类型
     * @param period 时间周期
     * @param page 页码
     * @param size 每页大小
     * @param currentUserId 当前用户ID
     * @return 排行榜数据
     */
    @Cacheable(value = "ranking:global", key = "#type + ':' + #period + ':' + #page + ':' + #size", 
               unless = "#result == null || #result.getRecords().isEmpty()")
    public Page<GlobalRankingResponse> getGlobalRanking(String type, String period, int page, int size, String currentUserId) {
        log.info("获取全站排行榜: type={}, period={}, page={}, size={}, userId={}", type, period, page, size, currentUserId);

        // 验证参数
        RankingType rankingType = RankingType.fromCode(type);
        if (rankingType == null) {
            throw new BusinessException(ResultCode.INVALID_RANKING_TYPE, "不支持的排行榜类型: " + type);
        }

        RankingPeriod rankingPeriod = RankingPeriod.fromCode(period);
        if (rankingPeriod == null) {
            throw new BusinessException(ResultCode.INVALID_PERIOD, "不支持的时间周期: " + period);
        }

        // 验证分页参数
        if (page < 1 || size < 1 || size > 100) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "分页参数错误");
        }

        try {
            // 获取时间范围
            LocalDateTime startTime = rankingPeriod.getStartTime();
            LocalDateTime endTime = rankingPeriod.getEndTime();

            // 创建分页对象
            Page<Map<String, Object>> pageRequest = new Page<>(page, size);

            // 查询排行榜数据
            Page<Map<String, Object>> rankingPage = rankingMapper.getGlobalRanking(
                    pageRequest, type, startTime, endTime, currentUserId);

            // 转换为响应DTO
            Page<GlobalRankingResponse> responsePage = new Page<>(page, size, rankingPage.getTotal());
            List<GlobalRankingResponse> responseList = rankingPage.getRecords().stream()
                    .map(record -> buildGlobalRankingResponse(record, type, period))
                    .collect(Collectors.toList());

            responsePage.setRecords(responseList);

            log.info("获取全站排行榜成功: type={}, period={}, total={}", type, period, responsePage.getTotal());
            return responsePage;

        } catch (Exception e) {
            log.error("获取全站排行榜失败: type={}, period={}", type, period, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取排行榜失败");
        }
    }

    /**
     * 获取用户个人排名
     *
     * @param userId 用户ID
     * @param type 排行榜类型
     * @param period 时间周期
     * @return 用户排名信息
     */
    @Cacheable(value = "ranking:user", key = "#userId + ':' + #type + ':' + #period", unless = "#result == null")
    public UserRankResponse getUserRank(String userId, String type, String period) {
        log.info("获取用户排名: userId={}, type={}, period={}", userId, type, period);

        // 验证参数
        RankingType rankingType = RankingType.fromCode(type);
        if (rankingType == null) {
            throw new BusinessException(ResultCode.INVALID_RANKING_TYPE, "不支持的排行榜类型: " + type);
        }

        RankingPeriod rankingPeriod = RankingPeriod.fromCode(period);
        if (rankingPeriod == null) {
            throw new BusinessException(ResultCode.INVALID_PERIOD, "不支持的时间周期: " + period);
        }

        try {
            // 获取时间范围
            LocalDateTime startTime = rankingPeriod.getStartTime();
            LocalDateTime endTime = rankingPeriod.getEndTime();

            // 查询用户排名信息
            Map<String, Object> rankData = rankingMapper.getUserRank(userId, type, startTime, endTime);

            if (rankData == null || rankData.isEmpty()) {
                log.warn("用户未参与排行榜: userId={}, type={}, period={}", userId, type, period);
                return buildEmptyUserRankResponse(type, period);
            }

            // 查询排名变化
            Map<String, Object> rankChange = getUserRankChange(userId, type, rankingPeriod);

            // 构建响应对象
            UserRankResponse response = buildUserRankResponse(rankData, rankChange, type, period);

            log.info("获取用户排名成功: userId={}, rank={}", userId, response.getCurrentRank());
            return response;

        } catch (Exception e) {
            log.error("获取用户排名失败: userId={}, type={}, period={}", userId, type, period, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取用户排名失败");
        }
    }

    /**
     * 获取排行榜统计信息
     *
     * @param type 排行榜类型
     * @param period 时间周期
     * @return 统计信息
     */
    @Cacheable(value = "ranking:stats", key = "#type + ':' + #period", unless = "#result == null")
    public RankingStatistics getRankingStatistics(String type, String period) {
        log.info("获取排行榜统计: type={}, period={}", type, period);

        // 验证参数
        RankingType rankingType = RankingType.fromCode(type);
        if (rankingType == null) {
            throw new BusinessException(ResultCode.INVALID_RANKING_TYPE, "不支持的排行榜类型: " + type);
        }

        RankingPeriod rankingPeriod = RankingPeriod.fromCode(period);
        if (rankingPeriod == null) {
            throw new BusinessException(ResultCode.INVALID_PERIOD, "不支持的时间周期: " + period);
        }

        try {
            // 获取时间范围
            LocalDateTime startTime = rankingPeriod.getStartTime();
            LocalDateTime endTime = rankingPeriod.getEndTime();

            // 查询统计信息
            Map<String, Object> statsData = rankingMapper.getRankingStatistics(type, startTime, endTime);
            List<Map<String, Object>> distributionData = rankingMapper.getScoreDistribution(type, startTime, endTime);

            // 构建响应对象
            RankingStatistics response = buildRankingStatistics(statsData, distributionData, type, period);

            log.info("获取排行榜统计成功: type={}, period={}, participants={}", 
                    type, period, response.getTotalParticipants());
            return response;

        } catch (Exception e) {
            log.error("获取排行榜统计失败: type={}, period={}", type, period, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取排行榜统计失败");
        }
    }

    /**
     * 计算并更新排行榜数据
     *
     * @param type 排行榜类型
     * @param period 时间周期
     * @return 更新的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int calculateAndUpdateRanking(String type, String period) {
        log.info("开始计算排行榜: type={}, period={}", type, period);

        // 验证参数
        RankingType rankingType = RankingType.fromCode(type);
        if (rankingType == null) {
            throw new BusinessException(ResultCode.INVALID_RANKING_TYPE, "不支持的排行榜类型: " + type);
        }

        RankingPeriod rankingPeriod = RankingPeriod.fromCode(period);
        if (rankingPeriod == null) {
            throw new BusinessException(ResultCode.INVALID_PERIOD, "不支持的时间周期: " + period);
        }

        try {
            // 获取时间范围
            LocalDateTime startTime = rankingPeriod.getStartTime();
            LocalDateTime endTime = rankingPeriod.getEndTime();

            // 根据排行榜类型计算分数
            List<Ranking> rankings = calculateRankingScores(rankingType, startTime, endTime);

            if (rankings.isEmpty()) {
                log.warn("没有数据需要计算排行榜: type={}, period={}", type, period);
                return 0;
            }

            // 排序并设置排名
            rankings.sort((r1, r2) -> r2.getScore().compareTo(r1.getScore()));
            for (int i = 0; i < rankings.size(); i++) {
                rankings.get(i).setRankPosition(i + 1);
                rankings.get(i).setRankingType(type);
                rankings.get(i).setCalculatedAt(LocalDateTime.now());
            }

            // 先删除该类型的旧排行榜记录（避免重复数据）
            log.info("删除旧排行榜记录: type={}", type);
            rankingMapper.deleteByTypeAndPeriod(type, rankingPeriod.getStartTime(), rankingPeriod.getEndTime());

            // 批量插入新记录
            int result = rankingMapper.batchInsertOrUpdate(rankings);

            // 清除相关缓存
            clearRankingCache(type, period);

            log.info("排行榜计算完成: type={}, period={}, records={}", type, period, result);
            return result;

        } catch (Exception e) {
            log.error("计算排行榜失败: type={}, period={}", type, period, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "计算排行榜失败");
        }
    }

    /**
     * 根据排行榜类型计算用户分数
     */
    private List<Ranking> calculateRankingScores(RankingType rankingType, LocalDateTime startTime, LocalDateTime endTime) {
        List<Ranking> rankings = new ArrayList<>();

        switch (rankingType) {
            case STUDY_QUESTIONS:
                rankings = calculateStudyQuestionsRanking(startTime, endTime);
                break;
            case STUDY_ACCURACY:
                rankings = calculateStudyAccuracyRanking(startTime, endTime);
                break;
            case STUDY_TIME:
                rankings = calculateStudyTimeRanking(startTime, endTime);
                break;
            case REPUTATION_SCORE:
                rankings = calculateReputationRanking(startTime, endTime);
                break;
            case COMPREHENSIVE:
                rankings = calculateComprehensiveRanking(startTime, endTime);
                break;
            default:
                log.warn("未知的排行榜类型: {}", rankingType);
        }

        return rankings;
    }

    /**
     * 计算刷题数量排名
     */
    private List<Ranking> calculateStudyQuestionsRanking(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算刷题数量排名: {} - {}", startTime, endTime);

        // 转换为LocalDate
        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        // 查询真实数据
        List<Map<String, Object>> rankingData = studyRecordMapper.getRankingByQuestionCount(startDate, endDate);

        List<Ranking> rankings = new ArrayList<>();
        for (Map<String, Object> data : rankingData) {
            Ranking ranking = new Ranking();
            ranking.setId(java.util.UUID.randomUUID().toString());
            ranking.setUserId((String) data.get("user_id"));
            ranking.setScore(((Number) data.get("total_score")).intValue());
            ranking.setRankingType("study_questions");
            ranking.setCalculatedAt(LocalDateTime.now());
            rankings.add(ranking);
        }

        log.info("计算刷题数量排名完成: 共{}个用户", rankings.size());
        return rankings;
    }

    /**
     * 计算正确率排名
     */
    private List<Ranking> calculateStudyAccuracyRanking(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算正确率排名: {} - {}", startTime, endTime);

        // 转换为LocalDate
        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        // 查询真实数据
        List<Map<String, Object>> rankingData = studyRecordMapper.getRankingByAccuracy(startDate, endDate);

        List<Ranking> rankings = new ArrayList<>();
        for (Map<String, Object> data : rankingData) {
            Ranking ranking = new Ranking();
            ranking.setId(java.util.UUID.randomUUID().toString());
            ranking.setUserId((String) data.get("user_id"));
            // 正确率已经是百分比，乘以100存储为整数
            ranking.setScore((int) (((Number) data.get("total_score")).doubleValue() * 100));
            ranking.setRankingType("study_accuracy");
            ranking.setCalculatedAt(LocalDateTime.now());
            rankings.add(ranking);
        }

        log.info("计算正确率排名完成: 共{}个用户", rankings.size());
        return rankings;
    }

    /**
     * 计算学习时长排名
     */
    private List<Ranking> calculateStudyTimeRanking(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算学习时长排名: {} - {}", startTime, endTime);

        // 转换为LocalDate
        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        // 查询真实数据
        List<Map<String, Object>> rankingData = studyRecordMapper.getRankingByStudyTime(startDate, endDate);

        List<Ranking> rankings = new ArrayList<>();
        for (Map<String, Object> data : rankingData) {
            Ranking ranking = new Ranking();
            ranking.setId(java.util.UUID.randomUUID().toString());
            ranking.setUserId((String) data.get("user_id"));
            ranking.setScore(((Number) data.get("total_score")).intValue());
            ranking.setRankingType("study_time");
            ranking.setCalculatedAt(LocalDateTime.now());
            rankings.add(ranking);
        }

        log.info("计算学习时长排名完成: 共{}个用户", rankings.size());
        return rankings;
    }

    /**
     * 计算信誉分数排名
     */
    private List<Ranking> calculateReputationRanking(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算信誉分数排名: {} - {}", startTime, endTime);

        // 信誉分数排名基于当前信誉分数，不受时间范围限制
        // 这里需要查询user_reputation_stats表，暂时返回空列表
        // TODO: 实现信誉分数查询逻辑
        List<Ranking> rankings = new ArrayList<>();

        log.info("计算信誉分数排名完成: 共{}个用户", rankings.size());
        return rankings;
    }

    /**
     * 计算综合排名
     */
    private List<Ranking> calculateComprehensiveRanking(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算综合排名: {} - {}", startTime, endTime);

        // 转换为LocalDate
        LocalDate startDate = startTime.toLocalDate();
        LocalDate endDate = endTime.toLocalDate();

        // 查询真实数据
        List<Map<String, Object>> rankingData = studyRecordMapper.getRankingByComprehensive(startDate, endDate);

        List<Ranking> rankings = new ArrayList<>();
        for (Map<String, Object> data : rankingData) {
            Ranking ranking = new Ranking();
            ranking.setId(java.util.UUID.randomUUID().toString());
            ranking.setUserId((String) data.get("user_id"));
            ranking.setScore(((Number) data.get("total_score")).intValue());
            ranking.setRankingType("comprehensive");
            ranking.setCalculatedAt(LocalDateTime.now());
            rankings.add(ranking);
        }

        log.info("计算综合排名完成: 共{}个用户", rankings.size());
        return rankings;
    }

    /**
     * 构建全站排行榜响应对象
     */
    private GlobalRankingResponse buildGlobalRankingResponse(Map<String, Object> record, String type, String period) {
        GlobalRankingResponse response = new GlobalRankingResponse();

        response.setRank(safeConvertToInteger(record.get("rank_position")));
        response.setUserId((String) record.get("user_id"));
        response.setUsername((String) record.get("username"));
        response.setNickname((String) record.get("nickname"));
        response.setAvatarUrl((String) record.get("avatar_url"));
        response.setScore(safeConvertToInteger(record.get("score")));
        response.setReputationLevel((String) record.get("reputation_level"));
        response.setRankChange(safeConvertToInteger(record.get("rank_change")));
        response.setRankingType(type);
        response.setPeriod(period);
        response.setCalculatedAt(safeConvertToLocalDateTime(record.get("calculated_at")));
        response.setIsCurrentUser(safeConvertToBoolean(record.get("is_current_user")));

        // 设置额外统计信息
        GlobalRankingResponse.ExtraStats extraStats = new GlobalRankingResponse.ExtraStats();
        extraStats.setTotalQuestions(safeConvertToInteger(record.get("total_questions")));
        extraStats.setCorrectQuestions(safeConvertToInteger(record.get("correct_questions")));
        extraStats.setAccuracyRate(safeConvertToDouble(record.get("accuracy_rate")));
        extraStats.setStudyTime(safeConvertToInteger(record.get("study_time")));
        extraStats.setStudyDays(safeConvertToInteger(record.get("study_days")));
        extraStats.setConsecutiveStudyDays(safeConvertToInteger(record.get("consecutive_study_days")));
        extraStats.setReputationScore(safeConvertToInteger(record.get("reputation_score")));
        response.setExtraStats(extraStats);

        return response;
    }

    /**
     * 构建用户排名响应对象
     */
    private UserRankResponse buildUserRankResponse(Map<String, Object> rankData, Map<String, Object> rankChange, String type, String period) {
        UserRankResponse response = new UserRankResponse();

        response.setCurrentRank(safeConvertToInteger(rankData.get("current_rank")));
        response.setTotalParticipants(safeConvertToInteger(rankData.get("total_participants")));
        response.setScore(safeConvertToInteger(rankData.get("score")));
        response.setRankChange(rankChange != null ? safeConvertToInteger(rankChange.get("rank_change")) : null);
        response.setPercentile(safeConvertToDouble(rankData.get("percentile")));
        response.setRankingType(type);
        response.setPeriod(period);
        response.setCalculatedAt(safeConvertToLocalDateTime(rankData.get("calculated_at")));
        response.setScoreGapToNext(safeConvertToInteger(rankData.get("score_gap_to_next")));
        response.setScoreGapToPrevious(safeConvertToInteger(rankData.get("score_gap_to_previous")));

        // 设置详细统计信息
        UserRankResponse.UserDetailStats detailStats = new UserRankResponse.UserDetailStats();
        detailStats.setTotalQuestions(safeConvertToInteger(rankData.get("total_questions")));
        detailStats.setCorrectQuestions(safeConvertToInteger(rankData.get("correct_questions")));
        detailStats.setAccuracyRate(safeConvertToDouble(rankData.get("accuracy_rate")));
        detailStats.setStudyTime(safeConvertToInteger(rankData.get("study_time")));
        detailStats.setStudyDays(safeConvertToInteger(rankData.get("study_days")));
        detailStats.setConsecutiveStudyDays(safeConvertToInteger(rankData.get("consecutive_study_days")));
        detailStats.setReputationScore(safeConvertToInteger(rankData.get("reputation_score")));
        detailStats.setComprehensiveScore(safeConvertToDouble(rankData.get("comprehensive_score")));
        response.setDetailStats(detailStats);

        return response;
    }

    /**
     * 构建空的用户排名响应（用户未参与排行榜时）
     */
    private UserRankResponse buildEmptyUserRankResponse(String type, String period) {
        UserRankResponse response = new UserRankResponse();
        response.setCurrentRank(null);
        response.setTotalParticipants(0);
        response.setScore(0);
        response.setRankChange(null);
        response.setPercentile(0.0);
        response.setRankingType(type);
        response.setPeriod(period);
        response.setCalculatedAt(LocalDateTime.now());
        response.setDetailStats(new UserRankResponse.UserDetailStats());
        return response;
    }

    /**
     * 构建排行榜统计信息
     */
    private RankingStatistics buildRankingStatistics(Map<String, Object> statsData, List<Map<String, Object>> distributionData, String type, String period) {
        RankingStatistics response = new RankingStatistics();

        response.setRankingType(type);
        response.setPeriod(period);
        response.setTotalParticipants(safeConvertToInteger(statsData.get("total_participants")));
        response.setAverageScore(safeConvertToDouble(statsData.get("average_score")));
        response.setMedianScore(safeConvertToDouble(statsData.get("median_score")));
        response.setMaxScore(safeConvertToInteger(statsData.get("max_score")));
        response.setMinScore(safeConvertToInteger(statsData.get("min_score")));
        response.setTopPercentileScore(safeConvertToInteger(statsData.get("top_percentile_score")));
        response.setUpdatedAt(safeConvertToLocalDateTime(statsData.get("updated_at")));
        response.setParticipantChange(safeConvertToInteger(statsData.get("participant_change")));

        // 构建分数分布
        List<RankingStatistics.ScoreDistribution> distributions = distributionData.stream()
                .map(this::buildScoreDistribution)
                .collect(Collectors.toList());
        response.setScoreDistribution(distributions);

        // 构建趋势信息
        RankingStatistics.TrendInfo trendInfo = new RankingStatistics.TrendInfo();
        trendInfo.setAverageScoreTrend((String) statsData.get("average_score_trend"));
        trendInfo.setParticipantTrend((String) statsData.get("participant_trend"));
        trendInfo.setCompetitionLevel((String) statsData.get("competition_level"));
        trendInfo.setDescription((String) statsData.get("trend_description"));
        response.setTrendInfo(trendInfo);

        return response;
    }

    /**
     * 构建分数分布对象
     */
    private RankingStatistics.ScoreDistribution buildScoreDistribution(Map<String, Object> data) {
        RankingStatistics.ScoreDistribution distribution = new RankingStatistics.ScoreDistribution();
        distribution.setRange((String) data.get("range"));
        distribution.setMinScore(safeConvertToInteger(data.get("min_score")));
        distribution.setMaxScore(safeConvertToInteger(data.get("max_score")));
        distribution.setCount(safeConvertToInteger(data.get("count")));
        distribution.setPercentage(safeConvertToDouble(data.get("percentage")));
        return distribution;
    }

    /**
     * 获取用户排名变化
     */
    private Map<String, Object> getUserRankChange(String userId, String type, RankingPeriod currentPeriod) {
        try {
            // 计算上一周期的时间范围
            RankingPeriod previousPeriod = getPreviousPeriod(currentPeriod);
            if (previousPeriod == null) {
                return null;
            }

            LocalDateTime currentStartTime = currentPeriod.getStartTime();
            LocalDateTime previousStartTime = previousPeriod.getStartTime();
            LocalDateTime previousEndTime = previousPeriod.getEndTime();

            return rankingMapper.getUserRankChange(userId, type, currentStartTime, previousStartTime, previousEndTime);
        } catch (Exception e) {
            log.warn("获取用户排名变化失败: userId={}, type={}", userId, type, e);
            return null;
        }
    }

    /**
     * 获取上一周期
     */
    private RankingPeriod getPreviousPeriod(RankingPeriod currentPeriod) {
        // 这里简化处理，实际应该根据具体的周期计算上一周期
        // 例如：本周的上一周期是上周，本月的上一周期是上月
        return currentPeriod;
    }

    /**
     * 删除过期的排行榜数据
     *
     * @param beforeTime 时间点
     * @param rankingType 排行榜类型
     * @return 删除的记录数
     */
    public int deleteOldRankings(LocalDateTime beforeTime, String rankingType) {
        log.debug("删除过期排行榜数据: beforeTime={}, rankingType={}", beforeTime, rankingType);

        try {
            int deleted = rankingMapper.deleteOldRankings(beforeTime, rankingType);

            // 清除相关缓存
            if (deleted > 0) {
                clearAllRankingCache();
            }

            log.info("删除过期排行榜数据完成: rankingType={}, deleted={}", rankingType, deleted);
            return deleted;
        } catch (Exception e) {
            log.error("删除过期排行榜数据失败: rankingType={}", rankingType, e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除过期排行榜数据失败");
        }
    }

    /**
     * 清除指定类型和周期的排行榜缓存
     *
     * @param type 排行榜类型
     * @param period 时间周期
     */
    @CacheEvict(value = {"ranking:global", "ranking:user", "ranking:stats"}, allEntries = true)
    public void clearRankingCache(String type, String period) {
        log.info("清除排行榜缓存: type={}, period={}", type, period);
    }

    /**
     * 清除所有排行榜缓存
     */
    @CacheEvict(value = {"ranking:global", "ranking:user", "ranking:stats"}, allEntries = true)
    public void clearAllRankingCache() {
        log.info("清除所有排行榜缓存");
    }

    /**
     * 安全地将数据库时间字段转换为LocalDateTime
     * 处理Timestamp和LocalDateTime的兼容性问题
     *
     * @param timeObj 时间对象
     * @return LocalDateTime
     */
    private LocalDateTime safeConvertToLocalDateTime(Object timeObj) {
        if (timeObj == null) {
            return LocalDateTime.now();
        }

        if (timeObj instanceof java.sql.Timestamp) {
            return ((java.sql.Timestamp) timeObj).toLocalDateTime();
        } else if (timeObj instanceof LocalDateTime) {
            return (LocalDateTime) timeObj;
        } else {
            log.warn("未知的时间类型: {}, 使用当前时间", timeObj.getClass().getSimpleName());
            return LocalDateTime.now();
        }
    }

    /**
     * 安全地将数据库字段转换为Boolean
     * 处理MySQL CASE表达式返回数值类型的问题
     *
     * @param boolObj Boolean对象（可能是Long、Integer、Boolean等）
     * @return Boolean
     */
    private Boolean safeConvertToBoolean(Object boolObj) {
        if (boolObj == null) {
            return false;
        }

        if (boolObj instanceof Boolean) {
            return (Boolean) boolObj;
        } else if (boolObj instanceof Number) {
            // MySQL的CASE表达式返回数值：1=true, 0=false
            return ((Number) boolObj).intValue() != 0;
        } else if (boolObj instanceof String) {
            String str = (String) boolObj;
            return "true".equalsIgnoreCase(str) || "1".equals(str);
        } else {
            log.warn("未知的Boolean类型: {}, 使用false", boolObj.getClass().getSimpleName());
            return false;
        }
    }

    /**
     * 安全地将数据库字段转换为Integer
     * 处理不同数值类型的兼容性问题
     *
     * @param intObj Integer对象（可能是Long、Integer、BigInteger等）
     * @return Integer
     */
    private Integer safeConvertToInteger(Object intObj) {
        if (intObj == null) {
            return null;
        }

        if (intObj instanceof Integer) {
            return (Integer) intObj;
        } else if (intObj instanceof Number) {
            return ((Number) intObj).intValue();
        } else if (intObj instanceof String) {
            try {
                return Integer.parseInt((String) intObj);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Integer: {}", intObj);
                return null;
            }
        } else {
            log.warn("未知的Integer类型: {}, 使用null", intObj.getClass().getSimpleName());
            return null;
        }
    }

    /**
     * 安全地将数据库字段转换为Double
     * 处理不同数值类型的兼容性问题
     *
     * @param doubleObj Double对象（可能是BigDecimal、Float、Double等）
     * @return Double
     */
    private Double safeConvertToDouble(Object doubleObj) {
        if (doubleObj == null) {
            return null;
        }

        if (doubleObj instanceof Double) {
            return (Double) doubleObj;
        } else if (doubleObj instanceof Number) {
            return ((Number) doubleObj).doubleValue();
        } else if (doubleObj instanceof String) {
            try {
                return Double.parseDouble((String) doubleObj);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Double: {}", doubleObj);
                return null;
            }
        } else {
            log.warn("未知的Double类型: {}, 使用null", doubleObj.getClass().getSimpleName());
            return null;
        }
    }
}
